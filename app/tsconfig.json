{"compilerOptions": {"target": "es2022", "module": "es2022", "lib": ["ESNEXT"], "allowJs": true, "jsx": "react-native", "noEmit": true, "strict": true, "moduleResolution": "node", "resolveJsonModule": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "skipLibCheck": true, "baseUrl": ".", "paths": {"@/*": ["src/*"], "@": ["src"]}}, "include": ["."], "exclude": ["node_modules", "babel.config.js", "metro.config.js", "jest.config.js"]}