// Top-level build file where you can add configuration options common to all sub-projects/modules.

apply from: rootProject.file('common.gradle')

buildscript {
    ext {
        buildToolsVersion = "30.0.2"
        minSdkVersion = 26 // Android 8.0
        compileSdkVersion = 32
        targetSdkVersion = 34
        ndkVersion = "21.4.7075529"
        kotlinVersion = "1.8.10"
        androidXAnnotation = "1.2.0"
        androidXBrowser = "1.3.0"
    }
    repositories {
        google()
        mavenCentral()
        // TODO: Remove jcenter() when all libraries have migrated to mavenCentral.
        jcenter()
    }
    dependencies {
        classpath("com.android.tools.build:gradle:4.2.2")
        classpath 'com.google.gms:google-services:4.3.8'
        classpath("org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlinVersion")
        // NOTE: Do not place your application dependencies here; they belong
        // in the individual module build.gradle files
    }
    configurations.all {

        resolutionStrategy {
            force 'com.google.android.play:core:1.10.3'
        }
    }
}

def isDetoxTestBuild = Boolean.valueOf(project.properties['isDetoxBuild'] ?: 'false')

allprojects {
    repositories {
        maven {
            // All of React Native (JS, Obj-C sources, Android binaries) is installed from npm
            url getPathRelativeToNodePackage('react-native', 'android')
        }
        maven {
            // Android JSC is installed from npm
            url getPathRelativeToNodePackage('jsc-android', 'dist')
        }
        maven {
            url "https://github.com/jitsi/jitsi-maven-repository/raw/master/releases"
        }
        mavenCentral {
            // We don't want to fetch react-native from Maven Central as there are
            // older versions over there.
            content {
                excludeGroup "com.facebook.react"
            }
        }
        maven {
            url "$rootDir/../node_modules/detox/Detox-android"
        }
        google()
        mavenCentral()
        // TODO: Remove jcenter() when all libraries have migrated to mavenCentral.
        jcenter()
        maven { url 'https://www.jitpack.io' }
        maven { url 'https://maven.scijava.org/content/repositories/public/' }
        maven {
        // Android JSC is installed from npm
        url("$rootDir/../node_modules/jsc-android/dist")
        }
    }

    subprojects { project ->
        afterEvaluate {
            def hasAndroidProperty = project.hasProperty('android')

            // Added to Fix Detox build command for libs with minSdk < required one.
            if (hasAndroidProperty && isDetoxTestBuild) {
                android {
                    compileSdkVersion rootProject.ext.compileSdkVersion
                    buildToolsVersion rootProject.ext.buildToolsVersion
                    dexOptions {
                        javaMaxHeapSize "4g"
                    }
                    defaultConfig {
                        minSdkVersion rootProject.ext.minSdkVersion
                        targetSdkVersion rootProject.ext.targetSdkVersion
                    }
                }
            }
        }
    }
}
