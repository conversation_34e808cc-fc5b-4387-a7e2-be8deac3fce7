{"name": "omni", "version": "3.3.3", "private": true, "scripts": {"clean": "rm -rf ./android/.gradle ./android/build ./android/app/build ./ios/Pods ./ios/Build ./node_modules && command -v ccache >/dev/null && ccache --clear || true", "start": "react-native start", "android": "react-native run-android --variant=prodDebug", "android:prod": "react-native run-android --active-arch-only --variant=prodDebug", "android:prod:assemble": "cd android && ./gradlew assembleProdRelease", "android:prod:bundle": "cd android && ./gradlew bundleProdRelease", "android:prod:install": "cd android && ./gradlew installProdRelease", "android:staging": "react-native run-android --appIdSuffix staging --variant=stagingDebug", "android:staging:assemble": "cd android && ./gradlew assembleStagingRelease", "android:staging:bundle": "cd android && ./gradlew bundleStagingRelease", "android:staging:install": "cd android && ./gradlew installStagingRelease", "android:dev": "react-native run-android --appIdSuffix dev --variant=devDebug", "android:dev:assemble": "cd android && ./gradlew assembleDevRelease", "android:dev:bundle": "cd android && ./gradlew bundleDevRelease", "android:dev:install": "cd android && ./gradlew installDevRelease", "android:storybook": "react-native run-android --appIdSuffix staging --variant=storybookDebug", "android:storybook:assemble": "cd android && ./gradlew assembleStorybookRelease", "android:storybook:install": "cd android && ./gradlew installStorybookRelease", "adb:devices": "adb devices", "adb:reverse": "adb reverse tcp:8081 tcp:8081", "ios": "react-native run-ios --scheme='omni'", "ios:device": "react-native run-ios --device --scheme='omni'", "ios:staging": "react-native run-ios --scheme='omni staging'", "ios:staging:device": "react-native run-ios --device --scheme='omni staging'", "ios:dev": "react-native run-ios --scheme='omni dev'", "ios:dev:device": "react-native run-ios --device --scheme='omni dev'", "ios:storybook": "react-native run-ios --scheme='omni storybook'", "ios:storybook:device": "react-native run-ios --device --scheme='omni storybook'", "test": "jest --force<PERSON>xit", "e2e:packager": "yarn react-native start", "e2e:build:android-debug": "yarn detox build -c android.emu.debug", "e2e:build:android-release": "detox build -c android.emu.release", "e2e:build:android-ci-release": "detox build -c android.ci.release", "e2e:build:ios-debug": "detox build -c ios.sim.debug", "e2e:build:ios-release": "detox build -c ios.sim.release", "e2e:test:android-debug": "detox test -c android.emu.debug --debug-synchronization 500", "e2e:test:android-release": "detox test -R 5 -c android.emu.release --artifacts-location e2e/artifacts --take-screenshots=failing --record-logs=failing --loglevel info --debug-synchronization 10000 --retries 3", "e2e:test:ios-debug": "detox test -c ios.sim.debug --debug-synchronization 500", "e2e:test:ios-release": "detox test -R 5 -c ios.sim.release --artifacts-location e2e/artifacts --take-screenshots=failing --record-logs=failing --loglevel info --debug-synchronization 10000 --retries 3", "local": "bash ./scripts/local-setup.sh && yarn build", "build": "tsc -b .", "api:codegen": "./scripts/generate-api.ts", "graphql:codegen": "graphql-codegen --config src/graphql/codegen.ts", "optimize-assets": "npx imageoptim-cli -a assets/images", "bundle-vis": "npx react-native-bundle-visualizer --entry-file ./index.js --reset-cache", "lint": "eslint \"*/**/*.{js,ts,tsx}\"", "signedCodepush": "./scripts/signedCodepush.sh", "codepush:android:steakwallet": "./scripts/codepushWithSentry/index.ts --platform android --buildVariant dev", "codepush:ios:steakwallet": "./scripts/codepushWithSentry/index.ts --platform ios --buildVariant dev", "codepush:steakwallet": "yarn codepush:ios:beta && yarn codepush:android:beta", "update-stories": "sb-rn-get-stories --config-path .ondevice", "prestart": "yarn update-stories", "storybook-watcher": "sb-rn-watcher --config-path .ondevice", "storybook": "STORYBOOK_TELEMETRY_DEBUG=1 start-storybook -p 6006", "build-storybook": "build-storybook", "deploy-storybook": "yarn build-storybook && firebase deploy", "delete:tags": "./scripts/deleteTags.sh"}, "dependencies": {"@azure/core-asynciterator-polyfill": "^1.0.2", "@contentful/rich-text-react-renderer": "^15.12.0", "@contentful/rich-text-types": "^15.12.0", "@craftzdog/react-native-buffer": "^6.0.3", "@ethersproject/shims": "^5.7.0", "@formatjs/intl-locale": "^2.4.33", "@gorhom/bottom-sheet": "^4.4.5", "@json-rpc-tools/utils": "^1.7.6", "@os-team/i18next-react-native-language-detector": "^1.0.16", "@papercups-io/chat-widget-native": "^1.0.5", "@ramp-network/ramp-instant-sdk": "^4.0.4", "@ramp-network/react-native-sdk": "^1.0.2", "@react-native-async-storage/async-storage": "^1.17.11", "@react-native-community/blur": "^4.2.0", "@react-native-community/checkbox": "^0.5.7", "@react-native-community/clipboard": "^1.5.1", "@react-native-community/netinfo": "^9.4.1", "@react-native-community/push-notification-ios": "^1.8.0", "@react-native-community/slider": "^4.2.0", "@react-native-masked-view/masked-view": "^0.2.6", "@react-native-picker/picker": "1.15.0", "@react-navigation/bottom-tabs": "^6.2.0", "@react-navigation/devtools": "^6.0.4", "@react-navigation/material-top-tabs": "^6.1.1", "@react-navigation/native": "^6.0.6", "@react-navigation/native-stack": "^6.2.5", "@sentry/react-native": "5.32.0", "@shopify/flash-list": "^1.4.0", "@shopify/react-native-skia": "^0.1.130", "@stakekit/api-hooks": "0.0.95", "@steakwallet/chains": "*", "@steakwallet/signers": "*", "@steakwallet/types": "*", "@tanstack/react-query": "^4", "@tradle/react-native-http": "^2.0.1", "@walletconnect/jsonrpc-types": "^1.0.3", "@walletconnect/react-native-compat": "^2.10.6", "@walletconnect/types": "^2.10.6", "@walletconnect/utils": "^2.10.6", "@walletconnect/web3wallet": "^1.9.5", "auto-zustand-selectors-hook": "2", "axios": "0.27.2", "bcrypt-react-native": "^1.1.1", "big-integer": "^1.6.51", "bignumber.js": "^9.1.0", "bs58": "^5.0.0", "cosmjs-types": "^0.9.0", "cross-fetch": "^3.1.5", "date-fns": "^2.28.0", "ethers": "5.4", "expo": "^44.0.6", "expo-asset": "^8.4.5", "expo-constants": "^13.0.2", "expo-file-system": "13.2.0", "expo-font": "^10.0.5", "expo-intent-launcher": "~10.1.3", "expo-linear-gradient": "11.0.3", "expo-local-authentication": "^12.1.1", "expo-modules-core": "^0.6.5", "expo-notifications": "^0.14.1", "expo-random": "12.1.2", "expo-screen-capture": "^4.1.1", "expo-secure-store": "~11.1.0", "expo-splash-screen": "^0.16.2", "expo-status-bar": "^1.2.0", "fast-text-encoding": "^1.0.6", "graphql": "^16.2.0", "graphql-request": "^7.1.2", "i18next": "^21.6.16", "i18next-chained-backend": "^3.0.2", "i18next-resources-to-backend": "^1.0.0", "immer": "^9.0.16", "intl": "^1.2.5", "jail-monkey": "^2.7.0", "jest-mock": "28.1.3", "jetifier": "^2.0.0", "lodash.clamp": "^4.0.3", "lodash.debounce": "^4.0.8", "lodash.isequal": "^4.5.0", "lodash.mapkeys": "^4.6.0", "lodash.merge": "^4.6.2", "lodash.omit": "^4.5.0", "lodash.pickby": "^4.6.0", "lodash.range": "^3.2.0", "lodash.throttle": "^4.1.1", "lottie-ios": "3.2.3", "lottie-react-native": "5.0.1", "memoizee": "^0.4.15", "minisearch": "^5.0.0", "mixpanel-react-native": "1.3.7", "moti": "^0.18.0", "neverthrow": "^6.0.0", "node-libs-react-native": "^1.2.1", "normalizr": "^3.6.2", "promise": "8.3.0", "purify-ts": "^2.0.1", "react": "^17.0.2", "react-content-loader": "^6.0.3", "react-dom": "^17.0.2", "react-i18next": "^11.16.7", "react-native": "0.67.5", "react-native-action-sheet": "^2.2.0", "react-native-animateable-text": "~0.9.1", "react-native-appstate-hook": "^1.0.6", "react-native-autocomplete-input": "^5.2.0", "react-native-blurhash": "~1.1.10", "react-native-branch": "^5.6.2", "react-native-camera": "^3.43.9", "react-native-chart-kit": "^6.11.0", "react-native-code-push": "^7.0.4", "react-native-collapsible": "^1.6.0", "react-native-config": "^1.4.5", "react-native-context-menu-view": "^1.5.2", "react-native-device-info": "^8.1.3", "react-native-fast-image": "8.6.3", "react-native-fs": "^2.17.0", "react-native-gesture-handler": "2.4.2", "react-native-get-random-values": "^1.7.0", "react-native-graph": "^0.1.1", "react-native-haptic-feedback": "^1.13.1", "react-native-in-app-review": "^4.3.3", "react-native-inappbrowser-reborn": "^3.7.0", "react-native-jazzicon": "^0.1.2", "react-native-level-fs": "^3.0.1", "react-native-mmkv": "2.4.1", "react-native-permissions": "^3.0.4", "react-native-phrase-sdk": "^1.0.0", "react-native-picker-select": "^8.0.4", "react-native-pressable-opacity": "1.0.8", "react-native-progress": "^5.0.0", "react-native-push-notification": "^7.4.0", "react-native-qrcode-scanner": "^1.5.5", "react-native-qrcode-svg": "^6.1.1", "react-native-quick-base64": "^1.0.0", "react-native-randombytes": "^3.6.0", "react-native-reanimated": "2.14.1", "react-native-safe-area-context": "^4.1.2", "react-native-screens": "^3.10.1", "react-native-sensitive-info": "https://github.com/graoapp/react-native-sensitive-info.git#commit=9a6e858e71f133dbf0cf4ec09e3bdbd57470e7eb", "react-native-sha256": "^1.4.10", "react-native-smooth-pincode-input": "^1.0.9", "react-native-sodium-jsi": "https://github.com/gabimoncha/react-native-sodium-jsi.git#commit=f4af0e94d1bfa1280e54dc6da9528234dd14a3e3", "react-native-svg": "12.5.1", "react-native-tab-view": "^3.1.1", "react-native-tcp-socket": "^5.5.0", "react-native-themis": "^0.14.7", "react-native-url-polyfill": "^1.3.0", "react-native-vector-icons": "^8.1.0", "react-native-video": "^5.2.1", "react-native-video-cache": "^2.0.5", "react-native-web": "^0.17.5", "react-native-webview": "11.21.1", "react-string-replace": "^0.4.4", "siwe": "^1.1.6", "styled-components": "^5.3.5", "styled-system": "^5.1.5", "superjson": "^2.2.2", "ts-is-present": "^1.2.1", "ts-retry-promise": "^0.7.0", "use-debounce": "^9.0.4", "vm-browserify": "^1.1.2", "xregexp": "^5.1.1", "zustand": "4.5.4"}, "devDependencies": {"@babel/core": "^7.18.13", "@babel/plugin-proposal-class-static-block": "^7.21.0", "@babel/plugin-proposal-export-namespace-from": "^7.16.7", "@babel/plugin-proposal-numeric-separator": "^7.18.6", "@babel/plugin-proposal-private-methods": "^7.18.6", "@babel/plugin-transform-class-properties": "^7.25.9", "@babel/plugin-transform-flow-strip-types": "^7.25.9", "@babel/plugin-transform-logical-assignment-operators": "^7.23.3", "@babel/plugin-transform-named-capturing-groups-regex": "^7.14.8", "@babel/preset-typescript": "^7.18.6", "@babel/runtime": "7.14.8", "@cosmjs/encoding": "0.27.0", "@faker-js/faker": "^7.3.0", "@graphql-codegen/cli": "5.0.3", "@graphql-codegen/typescript-react-query": "^6.1.0", "@react-native-community/datetimepicker": "^5.1.0", "@storybook/addon-actions": "6.4.18", "@storybook/addon-backgrounds": "6.4.18", "@storybook/addon-controls": "6.4.18", "@storybook/addon-essentials": "6.4.18", "@storybook/addon-links": "6.4.18", "@storybook/addon-ondevice-actions": "6.0.1-beta.7", "@storybook/addon-ondevice-backgrounds": "6.0.1-beta.7", "@storybook/addon-ondevice-controls": "6.0.1-beta.7", "@storybook/addon-ondevice-notes": "6.0.1-beta.7", "@storybook/addon-react-native-web": "0.0.18", "@storybook/builder-webpack5": "6.4.18", "@storybook/manager-webpack5": "6.4.18", "@storybook/react": "6.4.18", "@storybook/react-native": "6.0.1-beta.7", "@svgr/webpack": "^5", "@testing-library/jest-native": "^4.0.11", "@testing-library/react-hooks": "^8.0.1", "@testing-library/react-native": "^12.0.1", "@types/jest": "28.1.3", "@types/libsodium-wrappers": "0.7.6", "@types/lodash.clamp": "^4.0.7", "@types/lodash.debounce": "^4.0.7", "@types/lodash.isequal": "^4.5.6", "@types/lodash.merge": "^4.6.7", "@types/lodash.omit": "^4.5.7", "@types/lodash.pickby": "^4.6.7", "@types/lodash.range": "^3.2.7", "@types/lodash.throttle": "^4.1.7", "@types/memoizee": "^0.4.7", "@types/react": "~17.0.39", "@types/react-native": "~0.67.6", "@types/react-native-autocomplete-input": "^5.1.0", "@types/react-native-push-notification": "^7.3.1", "@types/react-native-vector-icons": "^6.4.8", "@types/react-native-video": "^5.0.12", "@types/react-test-renderer": "^17.0.2", "@types/semver": "^7.3.9", "@types/styled-components": "^5.1.25", "@types/styled-components-react-native": "^5.1.3", "@types/styled-system": "^5.1.15", "@welldone-software/why-did-you-render": "^7.0.1", "appcenter-cli": "^2.13.2", "babel-jest": "28.1.3", "babel-loader": "^8.2.2", "babel-plugin-date-fns": "^2.0.0", "babel-plugin-lodash": "^3.3.4", "babel-plugin-module-resolver": "^4.1.0", "babel-plugin-react-native-web": "^0.17.5", "babel-plugin-rewrite-require": "^1.14.5", "babel-plugin-styled-components": "^2.0.1", "babel-plugin-transform-remove-console": "^6.9.4", "babel-preset-expo": "^8.3.0", "browserify-fs": "^1.0.0", "circular-dependency-plugin": "^5.2.2", "detox": "^20.1.2", "eslint": "^8.14.0", "eslint-config-prettier": "^8.5.0", "eslint-import-resolver-typescript": "^2.5.0", "eslint-plugin-import": "^2.25.3", "eslint-plugin-react": "^7.28.0", "eslint-plugin-react-hooks": "^4.3.0", "eslint-plugin-react-native": "^4.0.0", "gradle-to-js": "^2.0.1", "husky": "^7.0.4", "jest": "28.1.3", "jest-expo": "^46.0.2", "libsodium-wrappers": "0.7.6", "lint-staged": "^12.3.5", "metro-react-native-babel-preset": "^0.76.0", "mixpanel-browser": "^2.45.0", "msw": "1.1.0", "net-browserify": "^0.2.4", "node-polyfill-webpack-plugin": "^2.0.0", "orval": "^7.3.0", "patch-package": "^6.5.1", "plist": "^3.0.6", "react-native-bundle-visualizer": "3.1.2", "react-native-config-node": "^0.0.3", "react-native-flipper": "^0.131.1", "react-native-flipper-zustand": "^1.0.2", "react-native-kill-packager": "^1.0.0", "react-native-mmkv-flipper-plugin": "^0.0.1", "react-native-monorepo-tools": "^1.2.1", "react-native-performance": "^2.1.0", "react-native-performance-flipper-reporter": "^2.0.0", "react-native-svg-transformer": "^0.14.3", "react-query-native-devtools": "^3.0.2", "react-test-renderer": "^17.0.2", "shelljs": "^0.8.5", "storybook-addon-styled-component-theme": "^2.0.0", "storybook-react-i18next": "^1.0.20", "ts-jest": "28.0.8", "typescript": "^5.7.2", "url": "^0.11.0", "webpack": "5", "zx": "^7.1.1"}, "engines": {"node": "<17", "npm": "<8.2"}, "expo": {"autolinking": {"exclude": ["react-native-reanimated"]}}}