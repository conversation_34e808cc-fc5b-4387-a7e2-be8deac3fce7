{"root": ["./app.tsx", "./codepushandsentrywrapper.tsx", "./generated.ts", "./global.d.ts", "./index.d.ts", "./index.js", "./jest.setup.js", "./orval-instance.ts", "./orval.config.ts", "./react-native.config.js", "./shim.js", "./startup.ts", "./wdyr.ts", "./__mocks__/mixpanel-react-native.js", "./__mocks__/react-native-branch.js", "./__mocks__/react-native-code-push.js", "./__mocks__/react-native-config.js", "./__mocks__/react-native-device-info.js", "./__mocks__/react-native-performance.js", "./__mocks__/react-native-sodium-jsi.js", "./__mocks__/react-native-themis.js", "./__mocks__/@react-native-community/push-notification-ios.js", "./__mocks__/@shopify/react-native-skia.js", "./e2e/environment.js", "./e2e/global-setup.js", "./e2e/global-teardown.js", "./e2e/init.js", "./e2e/jest.config.js", "./e2e/jest_setup.ts", "./e2e/scripts/consts.ts", "./e2e/src/__tests__/onboarding.spec.ts", "./e2e/src/usecases/onboarding/index.ts", "./e2e/src/usecases/onboarding/utils.ts", "./e2e/src/utils/consts.ts", "./e2e/src/utils/index.ts", "./e2e/src/utils/retries.ts", "./jest/transform-stub.js", "./polyfills/base64.ts", "./polyfills/promise.any.ts", "./polyfills/index.ts", "./scripts/generate-api.ts", "./scripts/codepushwithsentry/augmentation.ts", "./scripts/codepushwithsentry/index.ts", "./scripts/codepushwithsentry/types.ts", "./src/analytics/events.ts", "./src/analytics/index.ts", "./src/analytics/use-performance-analytics.ts", "./src/analytics/use-session-analytics.ts", "./src/api/index.ts", "./src/api/schemas/addressdto.ts", "./src/api/schemas/addressdtoadditionaladdresses.ts", "./src/api/schemas/addressesdto.ts", "./src/api/schemas/addressesdtoadditionaladdresses.ts", "./src/api/schemas/addressesrequestdto.ts", "./src/api/schemas/appversiondto.ts", "./src/api/schemas/appleattribution.ts", "./src/api/schemas/appleattributionrequest.ts", "./src/api/schemas/appleattributionrequestappleattribution.ts", "./src/api/schemas/availablebalancedto.ts", "./src/api/schemas/awaittransactiondto.ts", "./src/api/schemas/balancedto.ts", "./src/api/schemas/balancesrequestdto.ts", "./src/api/schemas/branchattributionrequest.ts", "./src/api/schemas/branchdatarequest.ts", "./src/api/schemas/branchreferrerrequest.ts", "./src/api/schemas/broadcasttransactionsrequest.ts", "./src/api/schemas/broadcasttransactionsresponse.ts", "./src/api/schemas/broadcasttransactionsresponseresponseitem.ts", "./src/api/schemas/category.ts", "./src/api/schemas/chainscontrollerfindtokensparams.ts", "./src/api/schemas/chainscontrollerfindtransactionsparams.ts", "./src/api/schemas/chainscontrollergethighlightedtokenlistsparams.ts", "./src/api/schemas/chainscontrollergetpaginatedtokenlistparams.ts", "./src/api/schemas/chartdatadto.ts", "./src/api/schemas/decoderequestdto.ts", "./src/api/schemas/decoderesponsedto.ts", "./src/api/schemas/decoderesponsedtoparamsitem.ts", "./src/api/schemas/decodedstringdto.ts", "./src/api/schemas/decodedstringparameterdto.ts", "./src/api/schemas/deviceidandtokendto.ts", "./src/api/schemas/domainprovider.ts", "./src/api/schemas/enrichedaddressdto.ts", "./src/api/schemas/enrichedaddressdtoens.ts", "./src/api/schemas/enrichedaddressparameterdto.ts", "./src/api/schemas/enrichedamountdto.ts", "./src/api/schemas/enrichedamountdtotoken.ts", "./src/api/schemas/enrichedamountparameterdto.ts", "./src/api/schemas/enrichednftdto.ts", "./src/api/schemas/enrichednftparameterdto.ts", "./src/api/schemas/ensdto.ts", "./src/api/schemas/evmtransactiondto.ts", "./src/api/schemas/executeroute.ts", "./src/api/schemas/executerouterequestdto.ts", "./src/api/schemas/executerouteresponsedto.ts", "./src/api/schemas/featureflags.ts", "./src/api/schemas/featureflagsdto.ts", "./src/api/schemas/feedto.ts", "./src/api/schemas/findnftsdto.ts", "./src/api/schemas/gasdto.ts", "./src/api/schemas/gasdtoparameters.ts", "./src/api/schemas/gasestimatedto.ts", "./src/api/schemas/gasmode.ts", "./src/api/schemas/gasmodevaluedto.ts", "./src/api/schemas/gasmodesdto.ts", "./src/api/schemas/gaspricestepdto.ts", "./src/api/schemas/gaspricestepdtogasargs.ts", "./src/api/schemas/gettransactionsdto.ts", "./src/api/schemas/gettransactionsdtoadditionaladdresses.ts", "./src/api/schemas/graphpoints.ts", "./src/api/schemas/hidenftsrequestdto.ts", "./src/api/schemas/highlightedtokenlistsresponsedto.ts", "./src/api/schemas/highlightedtokenslist.ts", "./src/api/schemas/historicaltransactionstatus.ts", "./src/api/schemas/index.ts", "./src/api/schemas/mixpanelidrequest.ts", "./src/api/schemas/networks.ts", "./src/api/schemas/nftcollectiondto.ts", "./src/api/schemas/nftdto.ts", "./src/api/schemas/nftimagedto.ts", "./src/api/schemas/nfttraitdto.ts", "./src/api/schemas/nfttransferdto.ts", "./src/api/schemas/nftscontrollerfindstakednftsparams.ts", "./src/api/schemas/nftscontrollerfindstakednftsv2v2params.ts", "./src/api/schemas/notification.ts", "./src/api/schemas/object.ts", "./src/api/schemas/offrampsaleevent.ts", "./src/api/schemas/onramppurchaseevent.ts", "./src/api/schemas/oraclesbychain.ts", "./src/api/schemas/paginatedtokenlistreplydto.ts", "./src/api/schemas/paginationmetadatadto.ts", "./src/api/schemas/portfolioitembalancedto.ts", "./src/api/schemas/preparetransactionrequestdto.ts", "./src/api/schemas/pricechartsdto.ts", "./src/api/schemas/pricechartsdtodata.ts", "./src/api/schemas/pricerequestdto.ts", "./src/api/schemas/priceresponsedto.ts", "./src/api/schemas/priorities.ts", "./src/api/schemas/profiledatadto.ts", "./src/api/schemas/profiledatarequestdto.ts", "./src/api/schemas/protocolsdto.ts", "./src/api/schemas/rampevent.ts", "./src/api/schemas/rampeventtype.ts", "./src/api/schemas/ramppurchase.ts", "./src/api/schemas/ramppurchaseeventtype.ts", "./src/api/schemas/rampsale.ts", "./src/api/schemas/rampsaleeventtype.ts", "./src/api/schemas/registertransactionnotificationdto.ts", "./src/api/schemas/registertransactionnotificationdtonotification.ts", "./src/api/schemas/resolverequestdto.ts", "./src/api/schemas/resolverequestdtonetwork.ts", "./src/api/schemas/resolveresponsedto.ts", "./src/api/schemas/reviewrouterequestdto.ts", "./src/api/schemas/runnabletxerrordto.ts", "./src/api/schemas/runnabletxerrortype.ts", "./src/api/schemas/runnabletxsuccessdto.ts", "./src/api/schemas/sendrequestdto.ts", "./src/api/schemas/stakednftdto.ts", "./src/api/schemas/stakednftsresponsev1dto.ts", "./src/api/schemas/stakednftsresponsev2dto.ts", "./src/api/schemas/statusimagedto.ts", "./src/api/schemas/submitrequestdto.ts", "./src/api/schemas/submitresponsedto.ts", "./src/api/schemas/suggestedgasoptiondto.ts", "./src/api/schemas/supportedwallets.ts", "./src/api/schemas/swapprovider.ts", "./src/api/schemas/swaproutedetails.ts", "./src/api/schemas/swaproutedetailsdto.ts", "./src/api/schemas/swaproutedetailsdtorouteobject.ts", "./src/api/schemas/swaproutesrequest.ts", "./src/api/schemas/swapstatus.ts", "./src/api/schemas/swaptoolsdto.ts", "./src/api/schemas/swaptransactiondto.ts", "./src/api/schemas/swapsgasdto.ts", "./src/api/schemas/tags.ts", "./src/api/schemas/tokenbalancedto.ts", "./src/api/schemas/tokendto.ts", "./src/api/schemas/tokensdto.ts", "./src/api/schemas/tokensrequestdto.ts", "./src/api/schemas/transactionpricerequestdto.ts", "./src/api/schemas/transactionpriceresponsedto.ts", "./src/api/schemas/transactionstatus.ts", "./src/api/schemas/transactionstatusdto.ts", "./src/api/schemas/transactionstatusdtoraw.ts", "./src/api/schemas/transactions.ts", "./src/api/schemas/transactionsdto.ts", "./src/api/schemas/transactionsresponsedto.ts", "./src/api/schemas/txdetailsdto.ts", "./src/api/schemas/unsignedtransactiondto.ts", "./src/api/schemas/updatepushnotificationtokenrequestdto.ts", "./src/assets/assets.ts", "./src/assets/index.ts", "./src/components/animations.tsx", "./src/components/assetscontrol.tsx", "./src/components/bayc.tsx", "./src/components/basenft.tsx", "./src/components/blurhashplaceholderimage.tsx", "./src/components/box.tsx", "./src/components/coin.tsx", "./src/components/custombackdrop.tsx", "./src/components/discreetmodebutton.tsx", "./src/components/earningscontrolsection.tsx", "./src/components/floatinggradientgroup.tsx", "./src/components/gasoption.tsx", "./src/components/gasselector.tsx", "./src/components/gradientplaceholderimage.tsx", "./src/components/headers.stories.tsx", "./src/components/headers.tsx", "./src/components/icons.tsx", "./src/components/identicon.tsx", "./src/components/image.tsx", "./src/components/infolistitem.tsx", "./src/components/infostack.tsx", "./src/components/input.tsx", "./src/components/keyboardavoidingview.tsx", "./src/components/loading.tsx", "./src/components/loadingspinner.tsx", "./src/components/nftcollectioncontextmenu.tsx", "./src/components/nftcontextmenu.tsx", "./src/components/networkfee.tsx", "./src/components/networkpickerpill.tsx", "./src/components/omnifastimage.tsx", "./src/components/omniiconbutton.tsx", "./src/components/omnipressable.tsx", "./src/components/paginatorindicator.tsx", "./src/components/pill.tsx", "./src/components/price.tsx", "./src/components/progress.tsx", "./src/components/remotesvg.tsx", "./src/components/separator.tsx", "./src/components/splash.tsx", "./src/components/stack.tsx", "./src/components/steakbutton.stories.tsx", "./src/components/steakbutton.tsx", "./src/components/success.tsx", "./src/components/suspensenavigationtransition.tsx", "./src/components/textinputgroup.tsx", "./src/components/tokencontextmenu.tsx", "./src/components/touchabledismisskeyboardcontainer.tsx", "./src/components/validatorimage.tsx", "./src/components/yieldopportunity.tsx", "./src/components/index.ts", "./src/components/amountinput/amountinput.stories.tsx", "./src/components/amountinput/amountinput.tsx", "./src/components/amountinput/amounttextinput.tsx", "./src/components/amountinput/index.tsx", "./src/components/assetlistitems/assetlistitemname.tsx", "./src/components/assetlistitems/assetlistitems.stories.tsx", "./src/components/assetlistitems/deprecationwarning.tsx", "./src/components/assetlistitems/portfolioitemerror.tsx", "./src/components/assetlistitems/portfolioitemwithbalance.tsx", "./src/components/assetlistitems/portfolioyieldwithbalance.tsx", "./src/components/assetlistitems/toggleportfolioitem.tsx", "./src/components/assetlistitems/walletportfolioitem.tsx", "./src/components/assetlistitems/walletportfolioyield.tsx", "./src/components/assetlistitems/index.tsx", "./src/components/assetlistitems/types.ts", "./src/components/authenticated/authenticated.tsx", "./src/components/authenticated/biometricsauthenticated.tsx", "./src/components/authenticated/passcodeauthenticated.tsx", "./src/components/authenticated/index.tsx", "./src/components/bottomsheet/bottomsheet.stories.tsx", "./src/components/bottomsheet/bottomsheet.tsx", "./src/components/bottomsheet/bottomsheetflashlist.tsx", "./src/components/bottomsheet/bottomsheetlist.tsx", "./src/components/bottomsheet/index.tsx", "./src/components/countdown/countdown.stories.tsx", "./src/components/countdown/countdown.tsx", "./src/components/countdown/index.ts", "./src/components/error/error.stories.tsx", "./src/components/error/error.tsx", "./src/components/error/index.tsx", "./src/components/filters/filter.tsx", "./src/components/filters/filterlist.tsx", "./src/components/filters/index.tsx", "./src/components/filters/utils.ts", "./src/components/history/historynote.tsx", "./src/components/history/swaphistoryitem.tsx", "./src/components/history/txhistoryitem.tsx", "./src/components/history/index.tsx", "./src/components/layout/index.tsx", "./src/components/layout/types.ts", "./src/components/layout/use-insets.ts", "./src/components/lottieviewcomponent/lottieviewcomponent.tsx", "./src/components/lottieviewcomponent/lottieviewcomponent.web.tsx", "./src/components/lottieviewcomponent/index.tsx", "./src/components/passcodeinput/backspace.tsx", "./src/components/passcodeinput/numpad.tsx", "./src/components/passcodeinput/passcodedisplay.tsx", "./src/components/passcodeinput/passcodeinput.stories.tsx", "./src/components/passcodeinput/passcodeinput.tsx", "./src/components/passcodeinput/index.tsx", "./src/components/seedphraseinput/seedphraseinput.stories.tsx", "./src/components/seedphraseinput/seedphraseinput.tsx", "./src/components/seedphraseinput/index.tsx", "./src/components/stories/progressbar.tsx", "./src/components/stories/stories.tsx", "./src/components/stories/stories.web.tsx", "./src/components/stories/index.ts", "./src/components/text/animatedtext.tsx", "./src/components/text/animatedtext.web.tsx", "./src/components/text/text.stories.tsx", "./src/components/text/text.test.tsx", "./src/components/text/text.tsx", "./src/components/text/index.ts", "./src/components/yield/list.tsx", "./src/components/yield/listitem.tsx", "./src/components/yield/index.ts", "./src/components/yield/types.tsx", "./src/components/explore/carousel.tsx", "./src/components/explore/carouselitem.tsx", "./src/components/explore/explorehomepage.tsx", "./src/components/explore/featuredblogpost.tsx", "./src/components/explore/featuredcollectionitem.tsx", "./src/components/explore/featureddapp.tsx", "./src/components/explore/featurednativeintegration.tsx", "./src/components/explore/common.tsx", "./src/components/explore/index.tsx", "./src/components/explore/types.tsx", "./src/components/phrase/phraseitem.tsx", "./src/components/phrase/index.ts", "./src/components/qr/qrcomponent.tsx", "./src/components/qr/qrcomponent.web.tsx", "./src/components/qr/qrwrapper.tsx", "./src/components/qr/index.ts", "./src/components/qr/types.ts", "./src/components/stake/stakeamount.stories.tsx", "./src/components/stake/stakeamountslider.tsx", "./src/components/stake/stakeinfo.tsx", "./src/components/stake/stakepill.tsx", "./src/components/stake/customstakeinforows.ts", "./src/components/stake/index.ts", "./src/components/stake/types.tsx", "./src/components/stake/use-stake-info-rows.ts", "./src/config/config.ts", "./src/config/index.ts", "./src/constants/colors.ts", "./src/constants/environmentvariables.ts", "./src/constants/images.ts", "./src/constants/languages.ts", "./src/constants/layout.ts", "./src/constants/spacing.ts", "./src/constants/theme.ts", "./src/constants/index.ts", "./src/constants/links.ts", "./src/constants/testnets.ts", "./src/contentful/api.ts", "./src/contentful/constants.ts", "./src/contentful/use-contentful.ts", "./src/graphql/codegen.ts", "./src/graphql/generated/graphql.ts", "./src/hooks/index.ts", "./src/hooks/types.ts", "./src/hooks/use-active-account.ts", "./src/hooks/use-active-wallet.ts", "./src/hooks/use-ask-for-review.ts", "./src/hooks/use-asset-amount.ts", "./src/hooks/use-balance-description.ts", "./src/hooks/use-balances.ts", "./src/hooks/use-chain.ts", "./src/hooks/use-countdown.ts", "./src/hooks/use-edit-account.tsx", "./src/hooks/use-ethereum.ts", "./src/hooks/use-feature-flags.ts", "./src/hooks/use-gas-fee-token.ts", "./src/hooks/use-gas.tsx", "./src/hooks/use-initialise-chains.ts", "./src/hooks/use-initialise-stake-kit.ts", "./src/hooks/use-initialise-walletconnect.ts", "./src/hooks/use-initialise-wallets-store.ts", "./src/hooks/use-interval.ts", "./src/hooks/use-is-read-only-wallet-by-id.ts", "./src/hooks/use-is-read-only-wallet.ts", "./src/hooks/use-locale-date-utils.ts", "./src/hooks/use-max-amount.ts", "./src/hooks/use-modals.ts", "./src/hooks/use-network-action-sheet.ts", "./src/hooks/use-network-ens.ts", "./src/hooks/use-nft-actions.ts", "./src/hooks/use-nfts.ts", "./src/hooks/use-passcode-setup.ts", "./src/hooks/use-passcode-unlock.ts", "./src/hooks/use-pending-action.ts", "./src/hooks/use-portfolio.ts", "./src/hooks/use-previous.ts", "./src/hooks/use-price-charts.ts", "./src/hooks/use-prices.ts", "./src/hooks/use-read-only-alert.ts", "./src/hooks/use-saved-ref.ts", "./src/hooks/use-screen-change-listeners.ts", "./src/hooks/use-search-query.ts", "./src/hooks/use-select-account.ts", "./src/hooks/use-send.ts", "./src/hooks/use-stake-kit-stake-calculate-transaction-gas.ts", "./src/hooks/use-stake-kit-stake.ts", "./src/hooks/use-staked-nfts.ts", "./src/hooks/use-stakekit-transaction.ts", "./src/hooks/use-stories.ts", "./src/hooks/use-success-image.ts", "./src/hooks/use-token-filters.ts", "./src/hooks/use-token-list.ts", "./src/hooks/use-token-symbol.ts", "./src/hooks/use-tokens.ts", "./src/hooks/use-transaction-submit.ts", "./src/hooks/use-user-validators.ts", "./src/hooks/use-wallet-by-id.ts", "./src/hooks/use-wallpaper-background.tsx", "./src/hooks/use-app-version/index.ts", "./src/hooks/use-app-version/use-app-version.ts", "./src/hooks/use-app-version/use-app-version.web.ts", "./src/hooks/use-authenticated-biometrics/index.ts", "./src/hooks/use-authenticated-biometrics/use-authenticated-biometrics.ts", "./src/hooks/use-authenticated-biometrics/use-authenticated-biometrics.web.ts", "./src/hooks/use-check-for-update/index.ts", "./src/hooks/use-check-for-update/use-check-for-update.ts", "./src/hooks/use-check-for-update/use-check-for-update.web.ts", "./src/hooks/use-keyboard-height/index.ts", "./src/hooks/use-keyboard-height/use-keyboard-height.ts", "./src/hooks/use-keyboard-height/use-keyboard-height.web.ts", "./src/hooks/use-permissions/index.ts", "./src/hooks/use-permissions/use-permissions.ts", "./src/hooks/use-permissions/use-permissions.web.ts", "./src/hooks/use-ramp/index.ts", "./src/hooks/use-ramp/use-ramp.ts", "./src/hooks/use-ramp/use-ramp.web.ts", "./src/hooks/use-screenshot-warning/index.ts", "./src/hooks/use-screenshot-warning/use-screenshot-warning.ts", "./src/hooks/use-screenshot-warning/use-screenshot-warning.web.ts", "./src/hooks/use-ssl-certificate/index.ts", "./src/hooks/use-ssl-certificate/use-ssl-certificate.ts", "./src/hooks/use-ssl-certificate/use-ssl-certificate.web.ts", "./src/hooks/utils/balance-of-implementation.test.ts", "./src/hooks/utils/balance-of-implementation.ts", "./src/hooks/utils/balance-of.test.ts", "./src/hooks/utils/balance-of.ts", "./src/hooks/utils/constants.ts", "./src/hooks/utils/parse-token-string.test.ts", "./src/hooks/utils/parse-token-string.ts", "./src/localization/i18n.ts", "./src/localization/i18nfortests.ts", "./src/localization/index.ts", "./src/localization/system-fonts.ts", "./src/localization/types.ts", "./src/localization/use-change-language.ts", "./src/localization/locales/index.ts", "./src/navigation/appnavigator.tsx", "./src/navigation/screenregistry.ts", "./src/navigation/index.tsx", "./src/navigation/methods.ts", "./src/navigation/types.ts", "./src/navigation/use-deeplink.ts", "./src/navigation/use-navigation.ts", "./src/providers/queryprovider.tsx", "./src/providers/uiwrappers.tsx", "./src/providers/index.tsx", "./src/screens/add-wallet/backupwallet.tsx", "./src/screens/add-wallet/choosewallet.tsx", "./src/screens/add-wallet/choosewallettype.tsx", "./src/screens/add-wallet/confirmphrase.tsx", "./src/screens/add-wallet/importcomplete.tsx", "./src/screens/add-wallet/inputphrase.tsx", "./src/screens/add-wallet/selectaccounts.tsx", "./src/screens/add-wallet/showphrase.tsx", "./src/screens/add-wallet/watchwallet.tsx", "./src/screens/add-wallet/common.tsx", "./src/screens/add-wallet/index.ts", "./src/screens/add-wallet/types.ts", "./src/screens/add-wallet/utils/getnextwalletname.ts", "./src/screens/add-wallet/utils/getreadonlywalletconf.ts", "./src/screens/add-wallet/utils/isreadonlyimportparams.ts", "./src/screens/add-wallet/utils/sanitizewalletsforimport.ts", "./src/screens/artist/index.tsx", "./src/screens/choose-account/index.tsx", "./src/screens/earn/earnhome.tsx", "./src/screens/earn/earnwrapper.tsx", "./src/screens/earn/index.ts", "./src/screens/earn/types.ts", "./src/screens/earn/hooks/index.ts", "./src/screens/earn/hooks/use-on-press-item.ts", "./src/screens/explore/collection.tsx", "./src/screens/explore/dapp.stories.tsx", "./src/screens/explore/dapp.tsx", "./src/screens/explore/explorerhome.tsx", "./src/screens/explore/explorerhomewrapper.tsx", "./src/screens/explore/nativeintegration.tsx", "./src/screens/explore/navigation.tsx", "./src/screens/explore/common.tsx", "./src/screens/explore/index.ts", "./src/screens/explore/types.ts", "./src/screens/explore/use-navigation.ts", "./src/screens/explore/hooks/index.ts", "./src/screens/explore/hooks/on-press-item.ts", "./src/screens/gas/customgas.tsx", "./src/screens/gas/gasmodal.tsx", "./src/screens/gas/selectgas.tsx", "./src/screens/gas/index.ts", "./src/screens/gas/types.ts", "./src/screens/gas/use-navigation.ts", "./src/screens/home/<USER>", "./src/screens/home/<USER>", "./src/screens/home/<USER>", "./src/screens/home/<USER>", "./src/screens/home/<USER>", "./src/screens/home/<USER>", "./src/screens/home/<USER>", "./src/screens/home/<USER>/index.ts", "./src/screens/home/<USER>/use-animated-positioning.tsx", "./src/screens/insufficient/insufficientfunds.stories.tsx", "./src/screens/insufficient/insufficientfunds.tsx", "./src/screens/insufficient/modal.tsx", "./src/screens/insufficient/index.ts", "./src/screens/logout/logoutmodal.tsx", "./src/screens/logout/logoutmodalwrapper.tsx", "./src/screens/logout/index.ts", "./src/screens/logout/logout.ts", "./src/screens/multi-validator/multivalidatorearnings.tsx", "./src/screens/multi-validator/validatoritem.tsx", "./src/screens/multi-validator/validatorslist.tsx", "./src/screens/multi-validator/validatorslistmodalwrapper.tsx", "./src/screens/multi-validator/index.ts", "./src/screens/multi-validator/hooks/multi-validator-balance.tsx", "./src/screens/nfts/nftbackground.tsx", "./src/screens/nfts/nfts.tsx", "./src/screens/nfts/nftscollection.tsx", "./src/screens/nfts/nftsdetail.tsx", "./src/screens/nfts/nftsdetailwrapper.tsx", "./src/screens/nfts/nftswrapper.tsx", "./src/screens/nfts/common.tsx", "./src/screens/nfts/index.tsx", "./src/screens/nfts/types.ts", "./src/screens/nfts/editaccount/collection.tsx", "./src/screens/nfts/editaccount/nfts.tsx", "./src/screens/nfts/editaccount/index.ts", "./src/screens/nfts/editaccount/modal.tsx", "./src/screens/nfts/editaccount/types.ts", "./src/screens/nfts/editaccount/use-navigation.ts", "./src/screens/onboarding/androidface.tsx", "./src/screens/onboarding/faceid.tsx", "./src/screens/onboarding/passcodesetup.tsx", "./src/screens/onboarding/securewallet.tsx", "./src/screens/onboarding/welcome.tsx", "./src/screens/onboarding/types.ts", "./src/screens/pending/actions.tsx", "./src/screens/pending/error.tsx", "./src/screens/pending/modal.tsx", "./src/screens/pending/preview.tsx", "./src/screens/pending/previewwrapper.tsx", "./src/screens/pending/success.tsx", "./src/screens/pending/validatorsubsetlist.tsx", "./src/screens/pending/validators.tsx", "./src/screens/pending/index.ts", "./src/screens/pending/types.ts", "./src/screens/pending/use-navigation.ts", "./src/screens/permissions/permissions.tsx", "./src/screens/permissions/use-permission-info-rows.ts", "./src/screens/receive/receive.tsx", "./src/screens/receive/receivewrapper.tsx", "./src/screens/receive/index.ts", "./src/screens/security/invalidcertificatescreen.tsx", "./src/screens/security/lockscreen.tsx", "./src/screens/security/securityscreen.stories.tsx", "./src/screens/security/securityscreen.tsx", "./src/screens/security/tampereddevicescreen.tsx", "./src/screens/security/index.ts", "./src/screens/security/types.ts", "./src/screens/security/use-security-checks.ts", "./src/screens/send/error.tsx", "./src/screens/send/modal.tsx", "./src/screens/send/sendamount.stories.tsx", "./src/screens/send/sendamount.tsx", "./src/screens/send/sendlist.tsx", "./src/screens/send/sendlistwrapper.tsx", "./src/screens/send/sendpreview.stories.tsx", "./src/screens/send/sendpreview.tsx", "./src/screens/send/sendrecipient.stories.tsx", "./src/screens/send/sendrecipient.tsx", "./src/screens/send/successwrapper.tsx", "./src/screens/send/common.tsx", "./src/screens/send/index.ts", "./src/screens/send/types.ts", "./src/screens/send/use-navigation.ts", "./src/screens/send/hooks/use-send-amount.ts", "./src/screens/send-nft/error.tsx", "./src/screens/send-nft/modal.tsx", "./src/screens/send-nft/sendnftpreview.stories.tsx", "./src/screens/send-nft/sendnftpreview.tsx", "./src/screens/send-nft/sendnftrecipient.stories.tsx", "./src/screens/send-nft/sendnftrecipient.tsx", "./src/screens/send-nft/success.tsx", "./src/screens/send-nft/common.tsx", "./src/screens/send-nft/index.ts", "./src/screens/send-nft/types.ts", "./src/screens/send-nft/use-navigation.ts", "./src/screens/send-nft/hooks/use-send-nft.ts", "./src/screens/settings/account.tsx", "./src/screens/settings/applock.tsx", "./src/screens/settings/backupsecurity.tsx", "./src/screens/settings/passcode.tsx", "./src/screens/settings/preferences.tsx", "./src/screens/settings/secretphrase.stories.tsx", "./src/screens/settings/secretphrase.tsx", "./src/screens/settings/settings.tsx", "./src/screens/settings/settingswrapper.tsx", "./src/screens/settings/common.tsx", "./src/screens/settings/index.tsx", "./src/screens/settings/types.ts", "./src/screens/stake/amount.tsx", "./src/screens/stake/amountwrapper.tsx", "./src/screens/stake/error.tsx", "./src/screens/stake/info.tsx", "./src/screens/stake/infowrapper.tsx", "./src/screens/stake/modal.tsx", "./src/screens/stake/preview.tsx", "./src/screens/stake/previewwrapper.tsx", "./src/screens/stake/stakechoosenft.tsx", "./src/screens/stake/stakechoosevalidator.tsx", "./src/screens/stake/success.tsx", "./src/screens/stake/successwrapper.tsx", "./src/screens/stake/unstakechoosenft.tsx", "./src/screens/stake/unstakechoosevalidator.tsx", "./src/screens/stake/bayc.ts", "./src/screens/stake/common.ts", "./src/screens/stake/index.ts", "./src/screens/stake/types.ts", "./src/screens/stake/use-navigation.ts", "./src/screens/swaps/reviewtxscreen.stories.tsx", "./src/screens/swaps/reviewtxscreen.tsx", "./src/screens/swaps/slippagescreen.stories.tsx", "./src/screens/swaps/slippagescreen.tsx", "./src/screens/swaps/submittedtxscreen.stories.tsx", "./src/screens/swaps/submittedtxscreen.tsx", "./src/screens/swaps/swaphistoryscreen.stories.tsx", "./src/screens/swaps/swaphistoryscreen.tsx", "./src/screens/swaps/swaptokenlistscreen.tsx", "./src/screens/swaps/swapsmainview.tsx", "./src/screens/swaps/swapsscreen.stories.tsx", "./src/screens/swaps/swapsscreen.tsx", "./src/screens/swaps/index.tsx", "./src/screens/swaps/types.ts", "./src/screens/swaps/components/gaswarning.tsx", "./src/screens/swaps/components/nobalance.tsx", "./src/screens/swaps/components/reviewtxamount.tsx", "./src/screens/swaps/components/reviewtxdetails.tsx", "./src/screens/swaps/components/selecttoken.tsx", "./src/screens/swaps/components/swaproute.tsx", "./src/screens/swaps/components/txamount.tsx", "./src/screens/swaps/components/index.tsx", "./src/screens/swaps/hooks/index.ts", "./src/screens/swaps/hooks/use-execute-route.ts", "./src/screens/swaps/hooks/use-quotations.ts", "./src/screens/swaps/hooks/use-swap-history.ts", "./src/screens/swaps/hooks/use-transaction-broadcast.ts", "./src/screens/swaps/hooks/use-transaction-by-swapid.ts", "./src/screens/temporary-error/error.tsx", "./src/screens/temporary-error/modal.tsx", "./src/screens/temporary-error/index.ts", "./src/screens/tips/custom-portfolios.tsx", "./src/screens/tips/wallet-connect.tsx", "./src/screens/update-account/choosename.tsx", "./src/screens/update-account/choosenamewrapper.tsx", "./src/screens/update-account/choosewallets.tsx", "./src/screens/update-account/choosewalletswrapper.tsx", "./src/screens/update-account/common.tsx", "./src/screens/update-account/index.ts", "./src/screens/update-account/types.ts", "./src/screens/update-available/update-available.tsx", "./src/screens/wallet/balances.tsx", "./src/screens/wallet/detail.stories.tsx", "./src/screens/wallet/detail.tsx", "./src/screens/wallet/detailwrapper.tsx", "./src/screens/wallet/earnmodal.tsx", "./src/screens/wallet/listemptycontentcomponent.tsx", "./src/screens/wallet/stakeinfomodal.tsx", "./src/screens/wallet/stakeinfomodalwrapper.tsx", "./src/screens/wallet/index.ts", "./src/screens/wallet/types.ts", "./src/screens/wallet/chart/chart.tsx", "./src/screens/wallet/chart/data.ts", "./src/screens/wallet/chart/index.ts", "./src/screens/wallet/sections/availablebalance.tsx", "./src/screens/wallet/sections/banners.tsx", "./src/screens/wallet/sections/controls.tsx", "./src/screens/wallet/sections/currentlyearning.tsx", "./src/screens/wallet/sections/roi.tsx", "./src/screens/wallet/sections/summary.tsx", "./src/screens/wallet/sections/zeroyields.tsx", "./src/screens/wallet/sections/index.ts", "./src/screens/wallet/tokens/tokenlist.tsx", "./src/screens/wallet/tokens/tokens.tsx", "./src/screens/wallet/tokens/tokenswrapper.tsx", "./src/screens/wallet/tokens/index.tsx", "./src/screens/wallet/tokens/utils.ts", "./src/screens/wallet-connect/complete.tsx", "./src/screens/wallet-connect/common.tsx", "./src/screens/wallet-connect/index.ts", "./src/screens/wallet-connect/info.stories.tsx", "./src/screens/wallet-connect/info.tsx", "./src/screens/wallet-connect/modal.tsx", "./src/screens/wallet-connect/overrides.ts", "./src/screens/wallet-connect/types.ts", "./src/screens/wallet-connect/utils.ts", "./src/screens/wallet-connect/action/action-wrapper.tsx", "./src/screens/wallet-connect/action/action.tsx", "./src/screens/wallet-connect/action/auth-request.tsx", "./src/screens/wallet-connect/action/components.tsx", "./src/screens/wallet-connect/action/index.ts", "./src/screens/wallet-connect/action/transaction.tsx", "./src/screens/wallet-connect/action/types.ts", "./src/screens/wallet-connect/qr-scanner/walletconnectqr.tsx", "./src/screens/wallet-connect/qr-scanner/walletconnectqrwrapper.tsx", "./src/screens/wallet-connect/qr-scanner/index.ts", "./src/screens/wallet-connect/session/dapp-wrapper.tsx", "./src/screens/wallet-connect/session/dapp.tsx", "./src/screens/wallet-connect/session/index.ts", "./src/screens/wallet-connect/session/session-request.tsx", "./src/services/api.ts", "./src/services/browser.ts", "./src/services/constants.ts", "./src/services/covalent.ts", "./src/services/ens.ts", "./src/services/index.ts", "./src/services/minimizer.ts", "./src/services/notifications.ts", "./src/services/notifications.web.ts", "./src/services/permissions.ts", "./src/services/permissions.web.ts", "./src/services/profile.ts", "./src/services/fiat/index.ts", "./src/services/security/testbuttons.tsx", "./src/services/security/cleansecuritystores.ts", "./src/services/security/index.ts", "./src/services/security/passcode.ts", "./src/services/security/passcode.web.ts", "./src/services/security/securestore.ts", "./src/services/security/types.ts", "./src/services/storage/index.ts", "./src/services/storage/keys.ts", "./src/services/storage/storage.ts", "./src/types/account.ts", "./src/types/index.ts", "./src/types/utils.ts", "./src/utils/action-sheet.ts", "./src/utils/address-placeholder.ts", "./src/utils/capitalise.ts", "./src/utils/compare-versions.ts", "./src/utils/console.ts", "./src/utils/device.ts", "./src/utils/equal-addresses.ts", "./src/utils/format-address.ts", "./src/utils/format-amount.ts", "./src/utils/format-network.ts", "./src/utils/format-token-symbol.ts", "./src/utils/get-deep-link-wc-topic.ts", "./src/utils/get-error-string.ts", "./src/utils/get-native-asset-path.ts", "./src/utils/get-sk-icon-url.ts", "./src/utils/hit-slops.ts", "./src/utils/index.ts", "./src/utils/input.ts", "./src/utils/is-nil.ts", "./src/utils/parse-json-or-superjson.ts", "./src/utils/parse-qr.ts", "./src/utils/parse-wallet-connect-uri.ts", "./src/utils/price-change.test.ts", "./src/utils/price-change.ts", "./src/utils/random-int.ts", "./src/utils/read-only-wallet.ts", "./src/utils/rem.ts", "./src/utils/result-helpers.ts", "./src/utils/shared.ts", "./src/utils/sum-balances.test.ts", "./src/utils/sum-balances.ts", "./src/utils/token-compare.ts", "./src/utils/truncate-amount.ts", "./src/utils/unique-values.ts", "./src/utils/uuid.ts", "./src/utils/validate-email.ts", "./src/utils/wait-for-ms.ts", "./src/utils/with-error-request-retry.ts", "./src/utils/yearn-stake.ts", "./src/utils/yields.ts", "./src/utils/bip39/_wordlists.js", "./src/utils/bip39/index.js", "./src/utils/sentry/index.ts", "./src/utils/sentry/sentry.ts", "./src/utils/sodium/index.ts", "./src/utils/sodium/sodium.ts", "./src/utils/sodium/sodium.web.ts", "./src/zustandstate/content.ts", "./src/zustandstate/feature-flags.ts", "./src/zustandstate/index.ts", "./src/zustandstate/settings.ts", "./src/zustandstate/swaps.ts", "./src/zustandstate/types.ts", "./src/zustandstate/validators.ts", "./src/zustandstate/addresses/addresses.ts", "./src/zustandstate/addresses/index.ts", "./src/zustandstate/chains/chains.ts", "./src/zustandstate/chains/constants.ts", "./src/zustandstate/chains/helpers.ts", "./src/zustandstate/chains/index.ts", "./src/zustandstate/chains/token-deprecation.ts", "./src/zustandstate/chains/utils.ts", "./src/zustandstate/nfts/index.ts", "./src/zustandstate/nfts/nfts.ts", "./src/zustandstate/portfolios/index.ts", "./src/zustandstate/portfolios/migrations.test.ts", "./src/zustandstate/portfolios/migrations.ts", "./src/zustandstate/portfolios/portfolios.ts", "./src/zustandstate/stake-kit/helpers.ts", "./src/zustandstate/stake-kit/index.ts", "./src/zustandstate/stake-kit/stake-kit.ts", "./src/zustandstate/stake-kit/utils/get-base-token-from-yield-op.test.ts", "./src/zustandstate/stake-kit/utils/get-base-token-from-yield-op.ts", "./src/zustandstate/stake-kit/utils/index.ts", "./src/zustandstate/stake-kit/utils/utils.ts", "./src/zustandstate/storageengine/index.ts", "./src/zustandstate/storageengine/mmkvstorageengine.ts", "./src/zustandstate/wallet-connect/constants.ts", "./src/zustandstate/wallet-connect/get-address-from-request.test.ts", "./src/zustandstate/wallet-connect/get-address-from-request.ts", "./src/zustandstate/wallet-connect/helpers.ts", "./src/zustandstate/wallet-connect/index.ts", "./src/zustandstate/wallet-connect/types.ts", "./src/zustandstate/wallet-connect/utils.ts", "./src/zustandstate/wallet-connect/wallet-connect.ts", "./src/zustandstate/wallet-connect/connectors/constants.ts", "./src/zustandstate/wallet-connect/connectors/cosmos.ts", "./src/zustandstate/wallet-connect/connectors/evm.ts", "./src/zustandstate/wallet-connect/connectors/index.ts", "./src/zustandstate/wallet-connect/connectors/solana.ts", "./src/zustandstate/wallet-connect/connectors/types.ts", "./src/zustandstate/wallet-connect/v2/client.ts", "./src/zustandstate/wallet-connect/v2/index.ts", "./src/zustandstate/wallet-connect/v2/utils/get-chain-id-from-request.ts", "./src/zustandstate/wallet-connect/v2/utils/get-chain-ids-from-proposal-namespaces.ts", "./src/zustandstate/wallet-connect/v2/utils/get-chain-ids-from-session-namespaces.ts", "./src/zustandstate/wallet-connect/v2/utils/get-fully-qualified-addresses.ts", "./src/zustandstate/wallet-connect/v2/utils/get-network-from-request.ts", "./src/zustandstate/wallet-connect/v2/utils/get-session-from-request.ts", "./src/zustandstate/wallet-connect/v2/utils/get-topic-from-uri.ts", "./src/zustandstate/wallet-connect/v2/utils/get-unsuported-methods.ts", "./src/zustandstate/wallet-connect/v2/utils/get-wallet-from-request.ts", "./src/zustandstate/wallet-connect/v2/utils/get-wallet-network-from-address.ts", "./src/zustandstate/wallet-connect/v2/utils/get-wallets-supported-namespaces.ts", "./src/zustandstate/wallet-connect/v2/utils/index.ts", "./src/zustandstate/wallet-connect/v2/utils/is-valid-caip2-key.ts", "./src/zustandstate/wallet-connect/v2/utils/set-address-to-wallet-network.ts", "./src/zustandstate/wallet-connect/v2/utils/split-to-prefix-and-chain-id.ts", "./src/zustandstate/wallets/hydrate-wallets.ts", "./src/zustandstate/wallets/index.ts", "./src/zustandstate/wallets/migrations.test.ts", "./src/zustandstate/wallets/migrations.ts", "./src/zustandstate/wallets/utils.ts", "./src/zustandstate/wallets/wallets.ts", "./storybook/data/nftgallery.ts", "./storybook/data/nfts.ts", "./storybook/data/dapps.ts", "./storybook/data/explore.ts", "./storybook/data/index.ts", "./storybook/data/portfolio.ts", "./storybook/data/wallet-connect.ts", "./storybook/data/zustand/addresses.ts", "./storybook/data/zustand/chains.ts", "./storybook/data/zustand/index.ts", "./storybook/data/zustand/mock.ts", "./storybook/data/zustand/nfts.ts", "./storybook/data/zustand/portfolio.ts", "./storybook/data/zustand/settings.ts", "./storybook/data/zustand/stake-kit.ts", "./storybook/data/zustand/swaps.ts", "./storybook/data/zustand/validators.ts", "./storybook/data/zustand/wallet-connect.ts", "./storybook/data/zustand/wallets.ts", "./storybook/stories/components.stories.tsx", "./storybook/stories/edit.stories.tsx", "./storybook/stories/layout.stories.tsx", "./storybook/stories/modals.stories.tsx", "./storybook/stories/nfts.stories.tsx", "./storybook/stories/onboarding.stories.tsx", "./storybook/stories/pending.stories.tsx", "./storybook/stories/receive.stories.tsx", "./storybook/stories/settings.stories.tsx", "./storybook/stories/stake.stories.tsx", "./storybook/stories/walletconnect.stories.tsx", "./storybook/stories/common.ts", "./storybook/stories/stakecomponents/infolistitem.stories.tsx", "./storybook/stories/stakecomponents/stakeinfo.stories.tsx", "./storybook/stories/stakecomponents/stakepill.stories.tsx", "./test/format-token-symbol.test.ts", "./test/input.test.ts", "./test/parse-qr.test.ts", "./test/parse-wallet-connect-uri.test.ts", "./test/update-available.test.ts", "./test/use-permissions-info-rows.test.ts"], "errors": true, "version": "5.7.2"}