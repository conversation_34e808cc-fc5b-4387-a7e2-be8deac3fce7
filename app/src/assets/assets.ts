const BASE_URL = `https://raw.githubusercontent.com/steakwallet/assets/master`;

export const getTokenIconUrl = (name?: string) =>
  name ? `${BASE_URL}/tokenicons/${name.toLowerCase()}.png` : undefined;

export const Assets = {
  omni: `${BASE_URL}/omni/omni.png`,
  chainIcons: {
    akash: `${BASE_URL}/chain-icons/akash.png`,
    arbitrum: `${BASE_URL}/chain-icons/arbitrum.png`,
    'avalanche-c': `${BASE_URL}/chain-icons/avalanche.png`,
    binance: `${BASE_URL}/chain-icons/binance.png`,
    celo: `${BASE_URL}/chain-icons/celo.png`,
    cosmos: `${BASE_URL}/chain-icons/cosmos.png`,
    ethereum: `${BASE_URL}/chain-icons/ethereum.png`,
    fantom: `${BASE_URL}/chain-icons/fantom.png`,
    gnosis: `${BASE_URL}/chain-icons/gnosis.png`,
    harmony: `${BASE_URL}/chain-icons/harmony.png`,
    juno: `${BASE_URL}/chain-icons/juno.png`,
    kava: `${BASE_URL}/chain-icons/kava.png`,
    luna: `${BASE_URL}/chain-icons/luna.png`,
    moonriver: `${BASE_URL}/chain-icons/moonriver.png`,
    near: `${BASE_URL}/chain-icons/near.png`,
    okx: `${BASE_URL}/chain-icons/okx.jpeg`,
    okc: `${BASE_URL}/chain-icons/okx.jpeg`,
    optimism: `${BASE_URL}/chain-icons/optimism.png`,
    osmosis: `${BASE_URL}/chain-icons/osmosis.png`,
    persistence: `${BASE_URL}/chain-icons/persistence.png`,
    polygon: `${BASE_URL}/chain-icons/polygon.png`,
    solana: `${BASE_URL}/chain-icons/solana.png`,
    stargaze: `${BASE_URL}/chain-icons/stargaze.png`,
    terra: `${BASE_URL}/chain-icons/terra.png`,
    tezos: `${BASE_URL}/chain-icons/tezos.png`,
    xdai: `${BASE_URL}/chain-icons/xdai.png`,
    zksync: `${BASE_URL}/chain-icons/zksync.png`,
    bitcoin: 'https://assets.coingecko.com/coins/images/1/small/bitcoin.png',
    tron: 'https://s2.coinmarketcap.com/static/img/coins/128x128/1958.png',
  },
  providers: {
    aave: `${BASE_URL}/providers/aave.png`,
    ape: `${BASE_URL}/providers/ape.png`,
    anchor: `${BASE_URL}/providers/anchor.png`,
    'ape-bakc': `${BASE_URL}/providers/ape_bakc.png`,
    'ape-bayc': `${BASE_URL}/providers/ape_bayc.png`,
    'ape-mayc': `${BASE_URL}/providers/ape_mayc.png`,
    benqi: `${BASE_URL}/providers/benqi.png`,
    chainlink: `${BASE_URL}/providers/chainlink.png`,
    lido: `${BASE_URL}/providers/lido.png`,
    paraspace: `${BASE_URL}/providers/paraspace.png`,
    pendle: `${BASE_URL}/providers/pendle.png`,
    sushi: `${BASE_URL}/providers/sushi.png`,
    yearn: `${BASE_URL}/providers/yearn.png`,
  },
  wallets: {
    keplr: `${BASE_URL}/wallets/keplr.png`,
    metamask: `${BASE_URL}/wallets/metamask.png`,
    omni: `${BASE_URL}/wallets/omni.png`,
    omniBlack: `${BASE_URL}/wallets/omniBlack.png`,
    omniQRLogo: `${BASE_URL}/wallets/omniQRLogo.png`,
    phantom: `${BASE_URL}/wallets/phantom.png`,
    steakwallet: `${BASE_URL}/wallets/steakwallet.png`,
    temple: `${BASE_URL}/wallets/temple.png`,
    polkadot: `${BASE_URL}/wallets/polkadot.png`,
    tonkeeper: `${BASE_URL}/wallets/tonkeeper.png`,
  },
} as const;
