import { GraphQLClient } from 'graphql-request';
import { Config } from 'react-native-config';

import { graphqlEndpoint } from './constants';
import { contentful_delivery, contentful_preview } from '@/constants/EnvironmentVariables';

export const IS_PREVIEW = __DEV__ || Config.ENV !== 'production';

export const contentfulClient = new GraphQLClient(graphqlEndpoint, {
  headers: {
    authorization: `Bearer ${
      IS_PREVIEW
        ? contentful_preview
        : contentful_delivery
    }`,
  },
});
