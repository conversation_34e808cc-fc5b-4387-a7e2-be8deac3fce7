/**
 * Generated by orval v7.3.0 🍺
 * Do not edit manually.
 * API Reference
 * Documentation for the Omni REST API
 * OpenAPI spec version: 1.0
 */

export type Networks = typeof Networks[keyof typeof Networks];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const Networks = {
  'avalanche-c': 'avalanche-c',
  arbitrum: 'arbitrum',
  binance: 'binance',
  celo: 'celo',
  ethereum: 'ethereum',
  'ethereum-goerli': 'ethereum-goerli',
  fantom: 'fantom',
  gnosis: 'gnosis',
  harmony: 'harmony',
  moonriver: 'moonriver',
  optimism: 'optimism',
  okc: 'okc',
  polygon: 'polygon',
  zksync: 'zksync',
  akash: 'akash',
  cosmos: 'cosmos',
  juno: 'juno',
  kava: 'kava',
  osmosis: 'osmosis',
  persistence: 'persistence',
  stargaze: 'stargaze',
  near: 'near',
  solana: 'solana',
  tezos: 'tezos',
  terra: 'terra',
} as const;
