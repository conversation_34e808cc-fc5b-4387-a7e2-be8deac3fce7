/**
 * Generated by orval v7.3.0 🍺
 * Do not edit manually.
 * API Reference
 * Documentation for the Omni REST API
 * OpenAPI spec version: 1.0
 */
import type { RegisterTransactionNotificationDtoNotification } from './registerTransactionNotificationDtoNotification';

export interface RegisterTransactionNotificationDto {
  deviceId: string;
  hash: string;
  /** @nullable */
  notification: RegisterTransactionNotificationDtoNotification;
}
