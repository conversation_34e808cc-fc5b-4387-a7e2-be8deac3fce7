import React, { useCallback } from 'react';

import { BaseTokens } from '@steakwallet/chains';

import { RouteProp } from '@react-navigation/native';

import { QrStackParamList } from '@/components';
import { openSendModal, openWalletConnectSessionModal } from '@/hooks';
import { useNavigation } from '@/navigation/use-navigation';
import { parseWalletConnectUri } from '@/utils/parse-wallet-connect-uri';
import {
  getCachedChains,
  useWalletsHooks,
  useWalletConnectHooks,
} from '@/zustandState';
import { getChainIdsFromSessionNamespaces } from '@/zustandState/wallet-connect/v2';

import { WalletConnectQrComponent } from './WalletConnectQR';

import { useSessionsWithEns } from '../common';
import { UiSession } from '../types';

export function WalletConnectQRWrapper({
  route,
}: {
  route: RouteProp<QrStackParamList, 'WalletConnectQR'>;
}) {
  const wallet = useWalletsHooks.useActiveWallet();
  const navigation = useNavigation();
  const handleWalletConnectUri = useWalletConnectHooks.useHandleUri();
  const sessionsWithEns = useSessionsWithEns();

  const handleQrCode = useCallback(
    (uri: string) => {
      const isAddress = BaseTokens.find((t) => {
        const chains = getCachedChains(t);
        return chains?.[0]?.isValidAddress(uri);
      });

      if (isAddress && wallet) {
        openSendModal({
          token: isAddress,
          recipient: uri,
          walletId: wallet.id,
        });
      }

      const { valid, uri: walletConnectUri } = parseWalletConnectUri(uri);
      if (valid) {
        handleWalletConnectUri(walletConnectUri!);
      }
    },
    [handleWalletConnectUri, wallet],
  );

  const sessions: UiSession[] = sessionsWithEns.map((s) => ({
    id: s.session.topic,
    name: s.session.peer.metadata.name,
    description: s.session.peer.metadata.description,
    icon: s.session.peer.metadata.icons[0],
    url: s.session.peer.metadata.url,
    chainIds: getChainIdsFromSessionNamespaces(s.session.namespaces),
    wallets: s.wallets,
  }));

  return (
    <WalletConnectQrComponent
      fullScreen={route.params.fullScreen}
      sessions={sessions}
      walletId={wallet!.id}
      onClose={navigation.goBack}
      onScan={handleQrCode}
      onPressSession={(session) => openWalletConnectSessionModal(session.id)}
    />
  );
}
