import { useCallback } from 'react';

import { useSwapsControllerExecuteRoute } from '@/api';
import { TokenWithWalletId, useAddress, useWalletById } from '@/hooks';
import { log, warn } from '@/utils';
import { SwapRouteDetails } from '@/zustandState';

export function useExecuteRoute(fromToken: TokenWithWalletId) {
  const { isLoading, mutateAsync } = useSwapsControllerExecuteRoute();
  const wallet = useWalletById(fromToken.walletId);

  const address = useAddress(fromToken, fromToken.walletId, wallet?.options);

  const executeRoute = useCallback(
    async (quote: SwapRouteDetails, steps: number) => {
      try {
        if (!address) {
          return;
        }

        log({ fromToken });

        const data = await mutateAsync({
          data: {
            route: quote,
            address,
            // TODO: This is the step index to execute
            index: 0,
            walletId: fromToken.walletId,
            // previousTxHash: '',
            // feeAccount: '',
          },
        });

        log('===== executeRoute =====');
        log({ ...data });
        return data;
      } catch (e) {
        warn('executeQuote', e);
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [fromToken, address],
  );

  log('isExecuting:', isLoading);

  return { executeRoute };
}
