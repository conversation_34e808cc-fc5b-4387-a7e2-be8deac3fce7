import React, { useState } from 'react';
import { Keyboard, ScrollView } from 'react-native';

import { Token } from '@steakwallet/types';

import { BalanceTypes } from '@stakekit/api-hooks';
import BigNumber from 'bignumber.js';
import { useTranslation } from 'react-i18next';

import ChevronLeftIcon from '@/assets/images/chevron-left.svg';
import {
  FloatingGradientGroup,
  Header,
  NetworkFee,
  OmniSafeAreaView,
  Stack,
  SteakButton,
} from '@/components';
import {
  fetchSuccessImage,
  useAssetAmount,
  useBalances,
  useGasProps,
  useReadOnlyAlert,
  useWalletById,
} from '@/hooks';
import {
  RootStackRouteProp,
  useNavigation,
  useRoute,
} from '@/navigation/use-navigation';
import { warn } from '@/utils';
import { isTokenBalanceErrorResponse, useSwapsHooks } from '@/zustandState';

import { ReviewTxAmount, ReviewTxDetails } from './components';
import { useExecuteRoute, useTransactionBroadcast } from './hooks';

export const ReviewTxScreen = () => {
  const { t } = useTranslation();
  const { goBack, navigate, addListener } = useNavigation();
  const {
    quote,
    txType,
    gas,
    gasToken,
    timeCount,
    omniFee,
    omniFeeToken,
    walletId,
  } = useRoute<RootStackRouteProp<'ReviewTx'>>().params;

  const fromToken = useSwapsHooks.useFromToken();

  const [loading, setLoading] = useState(false);
  const gasProps = useGasProps(gasToken);
  const { balanceOf } = useBalances();
  const gasBalances = balanceOf(gasToken, walletId);
  const gasBalance = isTokenBalanceErrorResponse(gasBalances)
    ? 0
    : new BigNumber(
        gasBalances.balances.find((b) => b.type === BalanceTypes.available)
          ?.amount ?? '0',
      );

  const gasTokenBalance = useAssetAmount(gasToken, gasBalance);

  const { executeRoute } = useExecuteRoute(fromToken!);
  const { transactionBroadcast } = useTransactionBroadcast(fromToken!);
  const [sticky, setSticky] = useState(false);

  React.useEffect(() => {
    addListener('focus', () => {
      Keyboard.dismiss();
      setSticky(true);
    });
  }, [addListener]);

  const wallet = useWalletById(walletId);

  const { isReadOnlyWallet, showAlert } = useReadOnlyAlert(
    wallet?.options ?? null,
  );

  const handleApprove = async () => {
    if (isReadOnlyWallet) {
      return showAlert();
    }

    setLoading(true);
    const routeData = await executeRoute(quote, quote.steps);
    if (!routeData) {
      warn('Error executing route. No data returned');
      return;
      // TODO: handle error here
    }

    const response = await transactionBroadcast(routeData, gasProps.step);
    const image = await fetchSuccessImage(
      response ? response.every((x) => x.success) : false,
    );

    navigate('SubmittedTx', {
      txType,
      from: quote.fromToken as Token,
      to: quote.toToken as Token,
      image,
      walletId: fromToken!.walletId,
      receivedAmountUSD: quote.receivedAmountUSD,
      gasAmountUSD: quote.gasAmountUSD,
      omniFeeAmountUSD: quote.omniFeeAmountUSD,
      response,
    });
    setLoading(false);
  };

  const insufficientFunds = gasTokenBalance.amount < gas.amount;

  const [floatingGradientGroupHeight, setFloatingGradientGroupHeight] =
    useState(0);

  const disabled = !isReadOnlyWallet && (loading || insufficientFunds);

  return (
    <>
      <OmniSafeAreaView variant='dark'>
        <Header
          iconLeft={ChevronLeftIcon}
          title={t(`header.swaps_review_${txType}`)}
          onPressLeft={goBack}
        />
        <ScrollView
          showsVerticalScrollIndicator={false}
          contentContainerStyle={{ paddingBottom: floatingGradientGroupHeight }}
        >
          <Stack distribute='space-between' width='100%' padding={16}>
            <ReviewTxAmount />
            <ReviewTxDetails
              timeCount={timeCount}
              slippage={quote.slippage}
              gasFee={gas}
              omniFee={omniFee}
              gasToken={gasToken}
              omniFeeToken={omniFeeToken}
            />
          </Stack>
        </ScrollView>
      </OmniSafeAreaView>

      <FloatingGradientGroup
        noOffset
        sticky={sticky}
        onLayout={(e) =>
          setFloatingGradientGroupHeight(e.nativeEvent.layout.height)
        }
      >
        <NetworkFee
          gas={gas}
          gasTokenAmount={gasTokenBalance.amount}
          {...gasProps}
        />
        <SteakButton
          full
          title={t(`swaps.approve_${txType}`)}
          onPress={handleApprove}
          submitting={loading}
          disabled={disabled}
        />
      </FloatingGradientGroup>
    </>
  );
};
