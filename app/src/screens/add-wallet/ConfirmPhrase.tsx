import React, { useState } from 'react';

import { ImportableWallets } from '@steakwallet/signers';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components/native';

import { Events, track } from '@/analytics';
import { Stack, Text } from '@/components';
import { PhraseItem } from '@/components/phrase';
import { SteakButton } from '@/components/SteakButton';
import { Colors, safeBottom, Spacing } from '@/constants';
import {
  RootStackRouteProp,
  useNavigation,
  useRoute,
} from '@/navigation/use-navigation';

import { BackBtn, Container, PhraseContainer } from './common';

const ConfirmContainer = styled.View<{ status?: 'success' | 'error' }>`
  padding: 16px 16px 4px 16px;
  border-radius: 12px;
  background: rgba(0, 0, 0, 0.3);
  flex-wrap: wrap;
  flex-direction: row;
  margin-bottom: 8px;
  margin-top: ${Spacing.xl}px;
  border-width: 2px;
  min-height: 68px;
  width: 100%;
  border: transparent;
  ${({ status }) =>
    status === 'success' &&
    `
    border: ${Colors.wasabi}
  `};
  ${({ status }) =>
    status === 'error' &&
    `
    border: ${Colors.rare}
  `};
`;

const shuffle = (arr: string[]) => {
  const newArr = arr.slice();
  for (let i = newArr.length - 1; i > 0; i--) {
    const rand = Math.floor(Math.random() * (i + 1));
    [newArr[i], newArr[rand]] = [newArr[rand], newArr[i]];
  }
  return newArr;
};

export function ConfirmPhraseScreen() {
  const { t } = useTranslation();
  const { navigate, goBack } = useNavigation();
  const { mnemonic: seed, ...params } =
    useRoute<RootStackRouteProp<'ConfirmPhrase'>>().params;

  const [parts] = useState(seed.split(' '));
  const [shuffled, setShuffled] = useState(shuffle(parts));
  const [reconstructed, setReconstructed] = useState<string[]>([]);

  const onDone = () => {
    track(Events.COMPLETED_WALLET_BACKUP);
    const onboardedWallet = {
      ...params,
      wallets: [
        {
          options: {
            walletType: ImportableWallets.Omni,
            mnemonic: seed,
            index: 0,
          },
          selected: true,
          networks: [],
        },
      ],
    };
    navigate('ImportComplete', {
      ...params,
      ...onboardedWallet,
      start: performance.now(),
    });
  };

  const add = (index: number) => {
    const word = shuffled[index];
    setShuffled((words) => {
      words.splice(index, 1);
      return words;
    });
    setReconstructed((words) => [...words, word]);
  };

  const remove = (index: number) => {
    const word = reconstructed[index];
    setReconstructed((words) => {
      words.splice(index, 1);
      return words;
    });
    setShuffled((words) => [...words, word]);
  };

  const isFinished = reconstructed.length === parts.length;
  const isError = isFinished && reconstructed.join(' ') !== seed;
  const nextButtonDisabled = !isFinished || isError;

  return (
    <Container testID='onboarding/confirmPhrase/top'>
      <BackBtn onPress={goBack} testID='onboarding/confirmPhrase/backBtn' />
      <Stack space={Spacing.base}>
        <Text size='2xl' medium tight>
          {t('confirmPhrase.verify_secret_phrase')}
        </Text>
        <Text inline={false} opacity={0.7}>
          {t('confirmPhrase.select_correct_order')}
        </Text>
      </Stack>
      <ConfirmContainer
        status={
          isFinished && !isError ? 'success' : isError ? 'error' : undefined
        }
      >
        {reconstructed.map((word, index) => (
          <PhraseItem
            key={`word_${word}`}
            num={index}
            word={word}
            onPress={() => remove(index)}
          />
        ))}
      </ConfirmContainer>
      <PhraseContainer>
        {shuffled.map((word, index) => (
          <PhraseItem
            key={`word_${word}`}
            num={index}
            word={word}
            onPress={() => add(index)}
            testID={`word_${word}`}
          />
        ))}
      </PhraseContainer>
      <Stack pb={safeBottom} space={0}>
        <SteakButton
          testID='onboarding/confirmPhrase/nextBtn'
          title={t('global.next')}
          full
          disabled={nextButtonDisabled}
          onPress={onDone}
        />
      </Stack>
    </Container>
  );
}
