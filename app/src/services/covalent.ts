import {
  getChainIdByNetwork,
  withTimeout,
  fetchWithRetries,
  isEvmChain,
} from '@steakwallet/chains';
import {
  Networks,
  covalentSupportedChains,
  EvmNetworks,
} from '@steakwallet/types';

import { Config } from 'react-native-config';

import { log } from '@/utils/console';
import { covalent } from '@/constants/EnvironmentVariables';

export const covalentApiKey = covalent;

export async function getEVMTokenBalances(
  address: string,
  network: Networks,
): Promise<(string | undefined)[]> {
  if (!isEvmChain(network)) {
    return [];
  }

  if (!covalentSupportedChains.includes(network as EvmNetworks)) {
    return [];
  }

  const chainId = getChainIdByNetwork(network);
  if (!chainId) {
    log(`covalent: cannot find chain ID for network ${network}`);
    return [];
  }

  try {
    const response = await withTimeout(
      async () =>
        fetchWithRetries(
          `https://api.covalenthq.com/v1/${chainId}/address/${address}/balances_v2/?key=${covalentApiKey}`,
        ),
      10000, // 10_000
    ).catch(() => ({ status: 400, json: () => null }));
    if (response.status !== 200) {
      log('invalid covalent response', response.status);
      return [];
    }

    const result = await response.json();
    return (
      result.data.items
        .filter((token: { balance: number }) => token.balance > 0)
        // filtering out dust
        .filter((token: { type: string }) => token.type === 'cryptocurrency')
        .map((token: { contract_address: string }) => {
          return token.contract_address ===
            // native token
            '******************************************'
            ? undefined
            : token.contract_address;
        })
    );
  } catch (e) {
    log('covalent error', e);
    return [];
  }
}
