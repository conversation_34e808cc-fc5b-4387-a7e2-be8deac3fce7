import { useCallback } from 'react';

import { WalletOptions } from '@steakwallet/signers';

import {
  useChainsControllerSubmit,
  transactionsControllerGetTransactionStatus,
} from '@/api';
import {
  HistoricalTransactionStatus,
  TransactionsResponseDto,
  SubmitResponseDto,
} from '@/api/schemas';
import { StatusImage } from '@/components';
import { log, warn } from '@/utils/console';

import { useReadOnlyAlert } from './use-read-only-alert';
import { fetchSuccessImage } from './use-success-image';
import { useWalletById } from './use-wallet-by-id';
import { Config } from 'react-native-config';
import { withRequestErrorRetry } from '../utils/with-error-request-retry';
import { Networks } from '@steakwallet/types';
import { memoizeGetSigningWallet } from '@/zustandState';
import { wallet_api } from '@/constants/EnvironmentVariables';

export interface TxSubmitProps {
  walletId: string;
  onSuccess: (args: { link: string; image: StatusImage }) => void;
  onError: (image: StatusImage, error: Error) => void;
}

const STATUS_ATTEMPTS = 10;
const STATUS_INTERVAL = 1000;

const SLOW_NETWORKS: Networks[] = [
  Networks.Ethereum,
  Networks.Binance,
  Networks.Bitcoin,
];

export function useTransactionSubmit({
  walletId,
  onSuccess,
  onError,
}: TxSubmitProps) {
  const wallet = useWalletById(walletId);
  const { isReadOnlyWallet, showAlert } = useReadOnlyAlert(
    wallet?.options ?? null,
  );

  const { mutateAsync, isLoading } = useChainsControllerSubmit({
    request: { headers: { 'X-API-KEY': wallet_api } },
  });

  const onSubmit = useCallback(
    // SendResponseDto is the schema for /chains/send, /nfts/transfer, /yields/enter, /yields/exit, /yields/pending_actions/{id}
    async (data: TransactionsResponseDto) => {
      let signedWallet;

      if (isReadOnlyWallet) {
        return showAlert();
      }

      let lastTransactionNetwork: Networks | null = null;
      let lastTransaction: SubmitResponseDto | null = null;

      const successImage = fetchSuccessImage(true);
      const errorImage = fetchSuccessImage(false);

      try {
        for (const { network, tx } of data.txs) {
          const opts = wallet?.options;
          if (!opts) {
            warn('[use-transaction-submit] no wallet options');
            return;
          }

          let signed: string;
          signedWallet = await memoizeGetSigningWallet(
            // TODO: check Networks type
            // @ts-ignore
            network as Networks,
            opts as WalletOptions,
          );
          signed = await signedWallet.signTransaction(tx);

          const result = (
            await withRequestErrorRetry({
              fn: () => mutateAsync({ data: { network, signed } }),
              retryTimes: 10,
            })
          ).unsafeCoerce();

          lastTransactionNetwork = network as Networks;
          lastTransaction = result;
        }
      } catch (e) {
        log('[use-transaction-submit] mutateAsync err', e);
        onError(
          await errorImage,
          new Error('[use-transaction-submit] mutateAsync err', { cause: e }),
        );
        return;
      }

      if (!lastTransactionNetwork || !lastTransaction) {
        log('[use-transaction-submit] missing lastTransaction');
        onError(
          await errorImage,
          new Error('[use-transaction-submit] missing lastTransaction'),
        );
        return;
      }

      if (SLOW_NETWORKS.includes(lastTransactionNetwork)) {
        log('[use-transaction-submit] slow network, skipping to success');
        // TODO: navigate to a "transaction submitted" page rather than success
        onSuccess({ image: await successImage, link: lastTransaction.link });
        return;
      }

      // poll for the status of the last transaction for 25 seconds
      for (let attempts = 0; attempts < STATUS_ATTEMPTS; attempts++) {
        const network = lastTransactionNetwork;
        const hash = lastTransaction.transactionHash;

        const status = (
          await withRequestErrorRetry({
            fn: () => transactionsControllerGetTransactionStatus(network, hash),
            retryTimes: 10,
          })
        ).unsafeCoerce();

        log('[use-transaction-submit] status', status.status);
        if (status.status === HistoricalTransactionStatus.success) {
          onSuccess({
            image: await successImage,
            link: lastTransaction.link,
          });
          log('[use-transaction-submit] mutateAsync', lastTransaction.link);
          return;
        }

        await new Promise((resolve) => setTimeout(resolve, STATUS_INTERVAL));
      }

      log(
        '[use-transaction-submit] transaction not mined within interval * attempts',
      );
      onError(
        await errorImage,
        new Error(
          '[use-transaction-submit] transaction not mined within interval * attempts',
        ),
      );
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [],
  );

  return {
    onSubmit,
    isLoading,
  };
}
