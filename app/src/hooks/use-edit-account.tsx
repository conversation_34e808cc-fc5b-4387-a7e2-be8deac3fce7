import { useCallback, useLayoutEffect, useRef, useState } from 'react';

import { NFT } from '@steakwallet/types';

import { Account, isWallet, openEditAccountImagesModal } from '@/hooks';
import { useNavigation } from '@/navigation/use-navigation';
import { updateProfile } from '@/services';
import { usePortfoliosHooks, useWalletsHooks } from '@/zustandState';

export const useEditAccount = (account: Account | null) => {
  const [background, setBackground] = useState<{
    animationUrl?: string | null;
    backgroundUrl?: string | null;
  }>({});
  const { goBack } = useNavigation();

  const updates = useRef<{ background?: string }>({});
  const updateWallet = useWalletsHooks.useUpdateWallet();
  const updatePortfolio = usePortfoliosHooks.useUpdatePortfolio();
  const addPortfolio = usePortfoliosHooks.useAddPortfolio();
  const portfolios = usePortfoliosHooks.usePortfolios();

  useLayoutEffect(() => {
    try {
      const background = JSON.parse(account?.background || '');
      setBackground(background);
    } catch (e) {
      setBackground({ backgroundUrl: account?.background });
    }
  }, [account?.background]);

  const uploadAccountChanges = useCallback(async () => {
    if (!account) {
      return;
    }
    if (isWallet(account)) {
      await updateWallet(account.id, updates.current);
      await updateProfile(account.id, updates.current);
    } else {
      const portfolio = {
        ...account,
        ...updates.current,
      };
      if (portfolios.find((p) => p.id === account.id)) {
        await updatePortfolio(account.id, portfolio);
      }
    }
  }, [account, portfolios, updatePortfolio, updateWallet]);

  const changeBackground = useCallback(() => {
    if (!account) {
      return;
    }
    return new Promise<NFT | null>((resolve) => {
      openEditAccountImagesModal({
        title: 'Background',
        onPressImage: (nft: NFT) => {
          goBack();
          const image = nft.image.standard;
          const background = {
            backgroundUrl: image,
            animationUrl: nft.animationUrl,
          };
          updates.current['background'] = JSON.stringify(background);
          setBackground(background);
          resolve(nft);
        },
        onClose: () => {
          resolve(null);
        },
        activeImage: background as string,
        account,
      });
    });
  }, [account, background, goBack]);

  return { background, changeBackground, uploadAccountChanges };
};
