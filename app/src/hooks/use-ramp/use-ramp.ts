import { useCallback, useMemo } from 'react';

import { Token } from '@steakwallet/types';

import { ramp as rampKey } from '@/constants/EnvironmentVariables';

import RampSdk, { RampEventTypes } from '@ramp-network/react-native-sdk';
import { Config } from 'react-native-config';
import { getUniqueId } from 'react-native-device-info';

import { Events, track } from '@/analytics';
import { useWebhooksControllerRampCreateEvent } from '@/api';
import { RampEventType } from '@/api/schemas';
import { rampTokenIds, rampTokens } from '@/services';
import { Wallet } from '@/types';
import { log, warn } from '@/utils/console';
import { equalTokens } from '@/utils/token-compare';
import {
  useFeatureFlagsHooks,
  useSettingsHooks,
  useWalletsHooks,
} from '@/zustandState';

import { getAddress } from '..';

function getRampIdFromToken(token: Token) {
  return Object.keys(rampTokens).find((id) => {
    if (!rampTokens[id]) {
      return false;
    }
    return equalTokens(token, rampTokens[id]);
  });
}

export interface RampProps {
  supported: boolean;
  open?: (wallet?: Wallet) => void;
}

const rampEndpoint = `${Config.WALLET_API_URL}/webhooks/ramp`;

export function useRamp(token?: Token): RampProps {
  const fiatFeatureFlag = useFeatureFlagsHooks.useFiatOnRamp();
  const wallets = useWalletsHooks.useWallets();
  const activeWallet = useWalletsHooks.useActiveWallet();

  const setDelayCodePush = useSettingsHooks.useSetDelayCodePush();

  const rampId = token && getRampIdFromToken(token);

  const { mutateAsync } = useWebhooksControllerRampCreateEvent();

  const open = useCallback(
    async (wallet?: Wallet) => {
      setDelayCodePush(true);

      const swapAsset = rampId ?? rampTokenIds.join(',');
      const defaultAsset = rampId ?? 'ETH';
      const w = wallet ?? activeWallet ?? wallets[0];

      let userAddress = token
        ? (await getAddress(token, w.id, w.options)) ?? undefined
        : undefined;

      const ramp = new RampSdk();

      ramp.on(RampEventTypes.PURCHASE_CREATED, async (event) => {
        log('WidgetEventTypes.PURCHASE_CREATED', event);

        const rampId =
          event.payload &&
          'purchase' in event.payload &&
          event.payload.purchase.id;

        if (rampId) {
          track(Events.RAMP_PURCHASE_CREATED, {
            rampId,
          });
          try {
            await mutateAsync({
              data: {
                deviceId: getUniqueId(),
                rampId,
                type: RampEventType.PURCHASE,
              },
            });
          } catch (e) {
            warn('ramp create event: PURCHASE_CREATED', e);
          }
        }
      });

      ramp.on(RampEventTypes.WIDGET_CLOSE, (event) => {
        log('WidgetEventTypes.WIDGET_CLOSE', event);
      });

      ramp.show({
        hostAppName: 'Omni',
        hostLogoUrl:
          'https://raw.githubusercontent.com/steakwallet/assets/master/logo.png?h=250',
        webhookStatusUrl: `${rampEndpoint}/update-purchase-status`,
        // TODO: need to update ramp sdk for this
        // offrampWebhookV3Url: `${rampEndpoint}/update-sale-status`,
        deepLinkScheme: 'omni',
        hostApiKey: rampKey,
        swapAsset,
        defaultAsset,
        userAddress,
        fiatCurrency: 'USD',
        fiatValue: '50',
      });
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [setDelayCodePush, rampId, activeWallet, wallets, token],
  );

  return useMemo(() => {
    if (!fiatFeatureFlag) {
      return { supported: false };
    }

    return { supported: token ? (rampId ? true : false) : true, open };
  }, [fiatFeatureFlag, open, rampId, token]);
}
