import { useCallback } from 'react';

import { Networks, Token } from '@steakwallet/types';

import {ramp as rampApiKey} from '@/constants/EnvironmentVariables'

import {
  RampInstantEventTypes,
  RampInstantSDK,
} from '@ramp-network/ramp-instant-sdk';
import { Config } from 'react-native-config';

import { rampTokenIds, rampTokens } from '@/services';
import { log } from '@/utils/console';
import { equalTokens } from '@/utils/token-compare';
import { useSettingsHooks, useWalletsHooks } from '@/zustandState';

import { getAddress } from '..';

function getRampIdFromToken(token: Token) {
  return Object.keys(rampTokens).find((id) => {
    if (!rampTokens[id]) {
      return false;
    }
    return equalTokens(token, rampTokens[id]);
  });
}

export interface RampProps {
  supported: boolean;
  open?: () => void;
}

export function useRamp(token?: Token): RampProps {
  const wallets = useWalletsHooks.useWallets();
  const wallet = useWalletsHooks.useActiveWallet();

  const setDelayCodePush = useSettingsHooks.useSetDelayCodePush();

  const rampId = token && getRampIdFromToken(token);

  const open = useCallback(async () => {
    setDelayCodePush(true);

    const swapAsset = rampId ?? rampTokenIds.join(',');
    const defaultAsset = rampId ?? 'ETH';
    const w = wallet ?? wallets[0];

    const userAddress = token
      ? await getAddress(token, w.id, w.options)
      : await getAddress(
          { network: Networks.Ethereum } as Token,
          w.id,
          w.options,
        );
    if (!userAddress) {
      return;
    }

    const ramp = new RampInstantSDK({
      hostAppName: 'Omni',
      hostLogoUrl:
        'https://raw.githubusercontent.com/steakwallet/assets/master/logo.png?h=250',
      // deepLinkScheme: 'omni',
      hostApiKey: rampApiKey,
      url: 'https://app.ramp.network', // This is necessary to fix a android bug where widget remains in infinite loading.
      swapAsset,
      defaultAsset,
      userAddress,
      variant: 'hosted-mobile',
    });

    ramp.on(RampInstantEventTypes.PURCHASE_CREATED, (event) => {
      log('WidgetEventTypes.PURCHASE_CREATED', event);
    });

    ramp.on(RampInstantEventTypes.WIDGET_CLOSE, (event) => {
      log('WidgetEventTypes.WIDGET_CLOSE', event);
    });

    ramp.show();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [rampId, wallet, wallets, token]);

  return { supported: token ? (rampId ? true : false) : true, open };
}
