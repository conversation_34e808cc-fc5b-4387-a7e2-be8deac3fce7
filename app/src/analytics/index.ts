import * as Analytics from 'mixpanel-react-native';
import { getUniqueId } from 'react-native-device-info';

import { Device } from '@/utils/device';

import { Events } from './events';
import { dev_mixpanel, mixpanel as mixpanelToken } from '@/constants/EnvironmentVariables';
export { Events };

export * from './use-performance-analytics';
export * from './use-session-analytics';

export let mixpanel: Analytics.Mixpanel;

if (Device.isWeb()) {
  mixpanel = (Analytics as any).init(
    __DEV__ ? dev_mixpanel : mixpanelToken,
  );
} else {
  mixpanel = new Analytics.Mixpanel(
    __DEV__ ? dev_mixpanel : mixpanelToken,
  );
}

export const track = (event: Events, props?: { [x: string]: any }) => {
  mixpanel.track(event, props);
};

export const registerSuperProps = (props: { [x: string]: any }) => {
  mixpanel.registerSuperPropertiesOnce(props);
};

registerSuperProps({ 'Omni Device ID': getUniqueId() });
