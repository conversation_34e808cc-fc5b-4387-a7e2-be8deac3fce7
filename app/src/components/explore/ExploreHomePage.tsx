import React, { MutableRefObject, useEffect, useMemo, useRef } from 'react';
import { RefreshControl, StyleSheet, View } from 'react-native';

import { isPresent } from 'ts-is-present';

import { List } from '@/components';
import { Colors } from '@/constants';
import {
  BlogPostsType,
  CollectionsType,
  DappsType,
  NativeIntegrationsType,
  SectionsType,
  useFeatureFlagsHooks,
  Yields,
} from '@/zustandState';

import { Carousel } from './Carousel';
import { FeaturedBlogPost } from './FeaturedBlogPost';
import { FeaturedCollectionItem } from './FeaturedCollectionItem';
import { FeaturedDapp } from './FeaturedDapp';
import { FeaturedNativeIntegration } from './FeaturedNativeIntegration';
import { Item } from './types';
import { FlashList, FlashListProps } from '@shopify/flash-list';
import { useSavedRef } from '@/hooks';

interface ExploreHomePageProps {
  sectionId: string;
  yields: Yields;

  onPressItem: (item: Item, featured?: boolean) => void;

  refreshing: boolean;
  onRefresh: () => void;

  sections: SectionsType;
  collections: CollectionsType;
  dapps: DappsType;
  blogPosts: BlogPostsType;
  integrations: NativeIntegrationsType;
}

type TypedFlashListProps = FlashListProps<any>;
type ExtraData = {
  onPressItemRef: MutableRefObject<ExploreHomePageProps['onPressItem']>;
  dapps: ExploreHomePageProps['dapps'];
  collections: ExploreHomePageProps['collections'];
  blogPosts: ExploreHomePageProps['blogPosts'];
  integrations: ExploreHomePageProps['integrations'];
  nftsEnabled: boolean;
  yields: ExploreHomePageProps['yields'];
};

export function ExploreHomePage({
  onPressItem,

  sections,
  collections,
  dapps,
  blogPosts,
  integrations,

  sectionId,
  yields,

  refreshing,
  onRefresh,
}: ExploreHomePageProps) {
  const ref = useRef<FlashList<any>>(null);
  const nftsEnabled = useFeatureFlagsHooks.useNfts();

  const section = useMemo(
    () => Object.values(sections).find((s) => s?.sys.id === sectionId),
    [sections, sectionId],
  );

  useEffect(() => {
    if (section?.collectionsCollection?.items.length) {
      ref.current?.scrollToIndex({
        index: 0,
        animated: true,
      });
    }
  }, [section]);

  const data = useMemo(() => {
    let items = section?.collectionsCollection?.items
      .map((c) => {
        if (!c) {
          return null;
        }

        const item =
          dapps[c.sys.id] ??
          collections[c.sys.id] ??
          blogPosts[c.sys.id] ??
          integrations[c.sys.id];
        if (!item) {
          return null;
        }

        return item;
      })
      .filter(isPresent);

    if (!nftsEnabled && items) {
      items = items?.filter((x) => {
        const isNftRelated =
          x.title?.toLowerCase().includes('nft') ||
          x.subtitle?.toLowerCase().includes('nft');
        return !isNftRelated;
      });
    }
    return items;
  }, [section, dapps, collections, blogPosts, integrations, nftsEnabled]);

  const onPressItemRef = useSavedRef(onPressItem);

  const extraData: ExtraData = useMemo(
    () => ({
      blogPosts,
      collections,
      dapps,
      integrations,
      nftsEnabled,
      onPressItemRef,
      yields,
    }),
    [
      blogPosts,
      collections,
      dapps,
      integrations,
      nftsEnabled,
      onPressItemRef,
      yields,
    ],
  );

  if (!section) {
    return null;
  }

  return (
    <FlashList
      ref={ref}
      data={data}
      renderItem={renderItem}
      extraData={extraData}
      estimatedItemSize={350}
      refreshControl={
        <RefreshControl
          refreshing={refreshing}
          onRefresh={onRefresh}
          colors={[Colors.black]}
          tintColor={Colors.white}
          // @ts-ignore
          size={Platform.OS === 'android' ? 'default' : 10}
        />
      }
      ListFooterComponent={<View style={styles.hack} />}
    />
  );
}

const renderItem: NonNullable<TypedFlashListProps>['renderItem'] = ({
  item,
  extraData,
}) => {
  const {
    onPressItemRef,
    dapps,
    collections,
    blogPosts,
    integrations,
    nftsEnabled,
    yields,
  } = extraData as ExtraData;

  const onPressFeatured = () => onPressItemRef.current(item, true);

  if (item?.__typename === 'Dapp') {
    return <FeaturedDapp dapp={item} onPress={onPressFeatured} />;
  }

  if (item?.__typename === 'BlogPost') {
    return <FeaturedBlogPost blogPost={item} onPress={onPressFeatured} />;
  }

  if (
    item?.__typename === 'NativeIntegration' &&
    item?.description &&
    item.image?.url &&
    item.subtitle
  ) {
    return (
      <FeaturedNativeIntegration integration={item} onPress={onPressFeatured} />
    );
  }

  if (item?.__typename === 'Collection') {
    let items: Item[] = item
      .nodesCollection!.items.map((x?: Item) => {
        if (!x) {
          return null;
        }
        const item =
          dapps[x.sys.id] ??
          collections[x.sys.id] ??
          blogPosts[x.sys.id] ??
          integrations[x.sys.id];
        return item;
      })
      .filter(isPresent);
    if (!nftsEnabled) {
      items = items.filter((item) => {
        const isNftRelated =
          item.title?.toLowerCase().includes('nft') ||
          item.subtitle?.toLowerCase().includes('nft');
        return !isNftRelated;
      });
    }

    if (item.displayType === 'feature') {
      return (
        <FeaturedCollectionItem collection={item} onPress={onPressFeatured} />
      );
    }

    if (!items.length) {
      return null;
    }

    if (item.displayType === 'carousel') {
      return (
        <Carousel
          title={item.title!}
          items={items}
          onPress={onPressItemRef.current}
          estimatedItemSize={274}
        />
      );
    }

    if (item.displayType === 'list') {
      return (
        <List
          title={item.title!}
          items={items}
          onPress={onPressItemRef.current}
          yields={yields}
        />
      );
    }
  }

  return null;
};

const styles = StyleSheet.create({
  hack: {
    height: 260,
  },
});
