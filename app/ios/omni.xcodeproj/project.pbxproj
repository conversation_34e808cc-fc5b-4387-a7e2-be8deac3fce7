// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 46;
	objects = {

/* Begin PBXBuildFile section */
		0709949DCAF60F55FDF2BF2D /* libPods-App-omni staging.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 4FDCFE57264F3299FA612474 /* libPods-App-omni staging.a */; };
		13B07FBC1A68108700A75B9A /* AppDelegate.m in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB01A68108700A75B9A /* AppDelegate.m */; };
		13B07FBD1A68108700A75B9A /* LaunchScreen.xib in Resources */ = {isa = PBXBuildFile; fileRef = 13B07FB11A68108700A75B9A /* LaunchScreen.xib */; };
		13B07FBF1A68108700A75B9A /* Images.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 13B07FB51A68108700A75B9A /* Images.xcassets */; };
		13B07FC11A68108700A75B9A /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB71A68108700A75B9A /* main.m */; };
		227AC8581ADF4A51B108700B /* GTPlanar-Medium.otf in Resources */ = {isa = PBXBuildFile; fileRef = 39D32990DAF64BB68F5030F5 /* GTPlanar-Medium.otf */; };
		2E9FFBABBD431230517815C3 /* libPods-App-omni.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 227E2357FB34FD39F83A3ADC /* libPods-App-omni.a */; };
		334C9672B1B118273AB13462 /* libPods-App-omni storybook.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 892C68BDFE8B7096993769BA /* libPods-App-omni storybook.a */; };
		3E461D99554A48A4959DE609 /* SplashScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = AA286B85B6C04FC6940260E9 /* SplashScreen.storyboard */; };
		41A17D7647FB1AC2B973AAA6 /* ExpoModulesProvider.swift in Sources */ = {isa = PBXBuildFile; fileRef = 5E4D2300A7100654C8180E19 /* ExpoModulesProvider.swift */; };
		430A1CF6A064404397F618BF /* GTPlanar-Bold.otf in Resources */ = {isa = PBXBuildFile; fileRef = A3ACB3AA8C824F6DB450C19F /* GTPlanar-Bold.otf */; };
		610F6EB7B39B9C922C1F30A6 /* libPods-App-omni dev.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 0E1B47572CD4392EC442FEB8 /* libPods-App-omni dev.a */; };
		6EE797FC741360B37928E602 /* ExpoModulesProvider.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0FEBE62F2AF906D3BEBA3DA1 /* ExpoModulesProvider.swift */; };
		756C36EA29A8231F007E9AFD /* RNMinimizer.m in Sources */ = {isa = PBXBuildFile; fileRef = 756C36E929A8231F007E9AFD /* RNMinimizer.m */; };
		756C36EB29A8231F007E9AFD /* RNMinimizer.m in Sources */ = {isa = PBXBuildFile; fileRef = 756C36E929A8231F007E9AFD /* RNMinimizer.m */; };
		756C36EC29A8231F007E9AFD /* RNMinimizer.m in Sources */ = {isa = PBXBuildFile; fileRef = 756C36E929A8231F007E9AFD /* RNMinimizer.m */; };
		756C36ED29A8231F007E9AFD /* RNMinimizer.m in Sources */ = {isa = PBXBuildFile; fileRef = 756C36E929A8231F007E9AFD /* RNMinimizer.m */; };
		75BE8E35296DCFB800AD363E /* ape_coin_success.gif in Resources */ = {isa = PBXBuildFile; fileRef = 75BE8E34296DCFB800AD363E /* ape_coin_success.gif */; };
		75BE8E36296DCFB800AD363E /* ape_coin_success.gif in Resources */ = {isa = PBXBuildFile; fileRef = 75BE8E34296DCFB800AD363E /* ape_coin_success.gif */; };
		75BE8E37296DCFB800AD363E /* ape_coin_success.gif in Resources */ = {isa = PBXBuildFile; fileRef = 75BE8E34296DCFB800AD363E /* ape_coin_success.gif */; };
		75BE8E38296DCFB800AD363E /* ape_coin_success.gif in Resources */ = {isa = PBXBuildFile; fileRef = 75BE8E34296DCFB800AD363E /* ape_coin_success.gif */; };
		75BE8E3A296DCFCA00AD363E /* onboard1.mp4 in Resources */ = {isa = PBXBuildFile; fileRef = 75BE8E39296DCFCA00AD363E /* onboard1.mp4 */; };
		75BE8E3B296DCFCA00AD363E /* onboard1.mp4 in Resources */ = {isa = PBXBuildFile; fileRef = 75BE8E39296DCFCA00AD363E /* onboard1.mp4 */; };
		75BE8E3C296DCFCA00AD363E /* onboard1.mp4 in Resources */ = {isa = PBXBuildFile; fileRef = 75BE8E39296DCFCA00AD363E /* onboard1.mp4 */; };
		75BE8E3D296DCFCA00AD363E /* onboard1.mp4 in Resources */ = {isa = PBXBuildFile; fileRef = 75BE8E39296DCFCA00AD363E /* onboard1.mp4 */; };
		8E158F9529115A9C004CFF60 /* noop-file.swift in Sources */ = {isa = PBXBuildFile; fileRef = B85753802785D458002C097B /* noop-file.swift */; };
		8E158F9629115A9C004CFF60 /* AppDelegate.m in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB01A68108700A75B9A /* AppDelegate.m */; };
		8E158F9729115A9C004CFF60 /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB71A68108700A75B9A /* main.m */; };
		8E158F9F29115A9C004CFF60 /* Expo.plist in Resources */ = {isa = PBXBuildFile; fileRef = BB2F792C24A3F905000567C9 /* Expo.plist */; };
		8E158FA029115A9C004CFF60 /* Images.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 13B07FB51A68108700A75B9A /* Images.xcassets */; };
		8E158FA129115A9C004CFF60 /* LaunchScreen.xib in Resources */ = {isa = PBXBuildFile; fileRef = 13B07FB11A68108700A75B9A /* LaunchScreen.xib */; };
		8E158FA229115A9C004CFF60 /* SplashScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = AA286B85B6C04FC6940260E9 /* SplashScreen.storyboard */; };
		8E158FA329115A9C004CFF60 /* GTPlanar-Bold.otf in Resources */ = {isa = PBXBuildFile; fileRef = A3ACB3AA8C824F6DB450C19F /* GTPlanar-Bold.otf */; };
		8E158FA429115A9C004CFF60 /* GTPlanar-Italic.otf in Resources */ = {isa = PBXBuildFile; fileRef = 58FF9F43984645F585FB199D /* GTPlanar-Italic.otf */; };
		8E158FA529115A9C004CFF60 /* GTPlanar-Medium.otf in Resources */ = {isa = PBXBuildFile; fileRef = 39D32990DAF64BB68F5030F5 /* GTPlanar-Medium.otf */; };
		8E158FA629115A9C004CFF60 /* GTPlanar-Regular.otf in Resources */ = {isa = PBXBuildFile; fileRef = F3D68F3E0DD0470F91040792 /* GTPlanar-Regular.otf */; };
		8E91BB21292E6B3700E450D9 /* AdServices.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 8E91BB1F292E68FC00E450D9 /* AdServices.framework */; };
		8E9EAB712865C546000BA6A8 /* noop-file.swift in Sources */ = {isa = PBXBuildFile; fileRef = B85753802785D458002C097B /* noop-file.swift */; };
		8E9EAB722865C546000BA6A8 /* AppDelegate.m in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB01A68108700A75B9A /* AppDelegate.m */; };
		8E9EAB732865C546000BA6A8 /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB71A68108700A75B9A /* main.m */; };
		8E9EAB7A2865C546000BA6A8 /* Expo.plist in Resources */ = {isa = PBXBuildFile; fileRef = BB2F792C24A3F905000567C9 /* Expo.plist */; };
		8E9EAB7B2865C546000BA6A8 /* Images.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 13B07FB51A68108700A75B9A /* Images.xcassets */; };
		8E9EAB7C2865C546000BA6A8 /* LaunchScreen.xib in Resources */ = {isa = PBXBuildFile; fileRef = 13B07FB11A68108700A75B9A /* LaunchScreen.xib */; };
		8E9EAB7D2865C546000BA6A8 /* SplashScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = AA286B85B6C04FC6940260E9 /* SplashScreen.storyboard */; };
		8E9EAB7E2865C546000BA6A8 /* GTPlanar-Bold.otf in Resources */ = {isa = PBXBuildFile; fileRef = A3ACB3AA8C824F6DB450C19F /* GTPlanar-Bold.otf */; };
		8E9EAB7F2865C546000BA6A8 /* GTPlanar-Italic.otf in Resources */ = {isa = PBXBuildFile; fileRef = 58FF9F43984645F585FB199D /* GTPlanar-Italic.otf */; };
		8E9EAB802865C546000BA6A8 /* GTPlanar-Medium.otf in Resources */ = {isa = PBXBuildFile; fileRef = 39D32990DAF64BB68F5030F5 /* GTPlanar-Medium.otf */; };
		8E9EAB812865C546000BA6A8 /* GTPlanar-Regular.otf in Resources */ = {isa = PBXBuildFile; fileRef = F3D68F3E0DD0470F91040792 /* GTPlanar-Regular.otf */; };
		8EAD60E9285CB8A500279901 /* GTPlanar-Bold.otf in Resources */ = {isa = PBXBuildFile; fileRef = A3ACB3AA8C824F6DB450C19F /* GTPlanar-Bold.otf */; };
		8EAD60EA285CB8A700279901 /* GTPlanar-Italic.otf in Resources */ = {isa = PBXBuildFile; fileRef = 58FF9F43984645F585FB199D /* GTPlanar-Italic.otf */; };
		8EAD60EB285CB8AC00279901 /* GTPlanar-Medium.otf in Resources */ = {isa = PBXBuildFile; fileRef = 39D32990DAF64BB68F5030F5 /* GTPlanar-Medium.otf */; };
		8EAD60EC285CB8B100279901 /* GTPlanar-Regular.otf in Resources */ = {isa = PBXBuildFile; fileRef = F3D68F3E0DD0470F91040792 /* GTPlanar-Regular.otf */; };
		8EBC685C27D1FD3F00C0F5EF /* noop-file.swift in Sources */ = {isa = PBXBuildFile; fileRef = B85753802785D458002C097B /* noop-file.swift */; };
		8EBC685D27D1FD3F00C0F5EF /* AppDelegate.m in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB01A68108700A75B9A /* AppDelegate.m */; };
		8EBC685E27D1FD3F00C0F5EF /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB71A68108700A75B9A /* main.m */; };
		8EBC686327D1FD3F00C0F5EF /* Expo.plist in Resources */ = {isa = PBXBuildFile; fileRef = BB2F792C24A3F905000567C9 /* Expo.plist */; };
		8EBC686527D1FD3F00C0F5EF /* Images.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 13B07FB51A68108700A75B9A /* Images.xcassets */; };
		8EBC686627D1FD3F00C0F5EF /* LaunchScreen.xib in Resources */ = {isa = PBXBuildFile; fileRef = 13B07FB11A68108700A75B9A /* LaunchScreen.xib */; };
		8EBC686727D1FD3F00C0F5EF /* SplashScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = AA286B85B6C04FC6940260E9 /* SplashScreen.storyboard */; };
		B69F0D74F4D14016B11F0E38 /* GTPlanar-Regular.otf in Resources */ = {isa = PBXBuildFile; fileRef = F3D68F3E0DD0470F91040792 /* GTPlanar-Regular.otf */; };
		B85753812785D458002C097B /* noop-file.swift in Sources */ = {isa = PBXBuildFile; fileRef = B85753802785D458002C097B /* noop-file.swift */; };
		BB2F792D24A3F905000567C9 /* Expo.plist in Resources */ = {isa = PBXBuildFile; fileRef = BB2F792C24A3F905000567C9 /* Expo.plist */; };
		DDD253E92A3B3B7FED32E250 /* ExpoModulesProvider.swift in Sources */ = {isa = PBXBuildFile; fileRef = A3C292E0A8A7925D60FD18C0 /* ExpoModulesProvider.swift */; };
		E65053EDBCD14D09875BB60D /* GTPlanar-Italic.otf in Resources */ = {isa = PBXBuildFile; fileRef = 58FF9F43984645F585FB199D /* GTPlanar-Italic.otf */; };
		FEE0C013DDBA25BD39209CC4 /* ExpoModulesProvider.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3F56AF4190277C9289535730 /* ExpoModulesProvider.swift */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		0E1B47572CD4392EC442FEB8 /* libPods-App-omni dev.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-App-omni dev.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		0FEBE62F2AF906D3BEBA3DA1 /* ExpoModulesProvider.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ExpoModulesProvider.swift; path = "Pods/Target Support Files/Pods-App-omni/ExpoModulesProvider.swift"; sourceTree = "<group>"; };
		13B07F961A680F5B00A75B9A /* Omni (Debug).app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = "Omni (Debug).app"; sourceTree = BUILT_PRODUCTS_DIR; };
		13B07FAF1A68108700A75B9A /* AppDelegate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = AppDelegate.h; path = omni/AppDelegate.h; sourceTree = "<group>"; };
		13B07FB01A68108700A75B9A /* AppDelegate.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = AppDelegate.m; path = omni/AppDelegate.m; sourceTree = "<group>"; };
		13B07FB21A68108700A75B9A /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.xib; name = Base; path = Base.lproj/LaunchScreen.xib; sourceTree = "<group>"; };
		13B07FB51A68108700A75B9A /* Images.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; name = Images.xcassets; path = omni/Images.xcassets; sourceTree = "<group>"; };
		13B07FB61A68108700A75B9A /* Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; name = Info.plist; path = omni/Info.plist; sourceTree = "<group>"; };
		13B07FB71A68108700A75B9A /* main.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = main.m; path = omni/main.m; sourceTree = "<group>"; };
		1891E13E0DA3F15EF1CB1994 /* Pods-App-omni storybook.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-App-omni storybook.release.xcconfig"; path = "Target Support Files/Pods-App-omni storybook/Pods-App-omni storybook.release.xcconfig"; sourceTree = "<group>"; };
		227E2357FB34FD39F83A3ADC /* libPods-App-omni.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-App-omni.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		363DD28EB1CFBFDEB1D50DEE /* Pods-App-omni staging.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-App-omni staging.debug.xcconfig"; path = "Target Support Files/Pods-App-omni staging/Pods-App-omni staging.debug.xcconfig"; sourceTree = "<group>"; };
		39D32990DAF64BB68F5030F5 /* GTPlanar-Medium.otf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "GTPlanar-Medium.otf"; path = "../src/assets/fonts/GTPlanar-Medium.otf"; sourceTree = "<group>"; };
		3F56AF4190277C9289535730 /* ExpoModulesProvider.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ExpoModulesProvider.swift; path = "Pods/Target Support Files/Pods-App-omni storybook/ExpoModulesProvider.swift"; sourceTree = "<group>"; };
		4FDCFE57264F3299FA612474 /* libPods-App-omni staging.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-App-omni staging.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		505A245B8D3B696BE6C656CB /* Pods-App-omni dev.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-App-omni dev.release.xcconfig"; path = "Target Support Files/Pods-App-omni dev/Pods-App-omni dev.release.xcconfig"; sourceTree = "<group>"; };
		58FF9F43984645F585FB199D /* GTPlanar-Italic.otf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "GTPlanar-Italic.otf"; path = "../src/assets/fonts/GTPlanar-Italic.otf"; sourceTree = "<group>"; };
		5E4D2300A7100654C8180E19 /* ExpoModulesProvider.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ExpoModulesProvider.swift; path = "Pods/Target Support Files/Pods-App-omni dev/ExpoModulesProvider.swift"; sourceTree = "<group>"; };
		756C36E929A8231F007E9AFD /* RNMinimizer.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RNMinimizer.m; sourceTree = "<group>"; };
		756C36EE29A8B827007E9AFD /* RNMinimizer.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RNMinimizer.h; sourceTree = "<group>"; };
		75BE8E34296DCFB800AD363E /* ape_coin_success.gif */ = {isa = PBXFileReference; lastKnownFileType = image.gif; name = ape_coin_success.gif; path = ../android/app/src/main/res/drawable/ape_coin_success.gif; sourceTree = "<group>"; };
		75BE8E39296DCFCA00AD363E /* onboard1.mp4 */ = {isa = PBXFileReference; lastKnownFileType = file; name = onboard1.mp4; path = ../android/app/src/main/res/raw/onboard1.mp4; sourceTree = "<group>"; };
		892C68BDFE8B7096993769BA /* libPods-App-omni storybook.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-App-omni storybook.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		8E158FAE29115A9C004CFF60 /* Omni Dev (Debug).app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = "Omni Dev (Debug).app"; sourceTree = BUILT_PRODUCTS_DIR; };
		8E158FAF29115A9D004CFF60 /* DevInfo.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; name = DevInfo.plist; path = omni/DevInfo.plist; sourceTree = "<group>"; };
		8E91BB1F292E68FC00E450D9 /* AdServices.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AdServices.framework; path = System/Library/Frameworks/AdServices.framework; sourceTree = SDKROOT; };
		8E9EAB892865C546000BA6A8 /* Omni Storybook (Debug).app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = "Omni Storybook (Debug).app"; sourceTree = BUILT_PRODUCTS_DIR; };
		8E9EAB8A2865C547000BA6A8 /* StorybookInfo.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; name = StorybookInfo.plist; path = omni/StorybookInfo.plist; sourceTree = "<group>"; };
		8EBC687127D1FD3F00C0F5EF /* Omni Staging (Debug).app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = "Omni Staging (Debug).app"; sourceTree = BUILT_PRODUCTS_DIR; };
		8EBC687227D1FD3F00C0F5EF /* StagingInfo.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; name = StagingInfo.plist; path = omni/StagingInfo.plist; sourceTree = "<group>"; };
		8EE6B8CE27E488A900F50DF5 /* Firebase */ = {isa = PBXFileReference; lastKnownFileType = folder; path = Firebase; sourceTree = "<group>"; };
		9B775429A4E119F04F36C57C /* Pods-App-omni dev.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-App-omni dev.debug.xcconfig"; path = "Target Support Files/Pods-App-omni dev/Pods-App-omni dev.debug.xcconfig"; sourceTree = "<group>"; };
		A3ACB3AA8C824F6DB450C19F /* GTPlanar-Bold.otf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "GTPlanar-Bold.otf"; path = "../src/assets/fonts/GTPlanar-Bold.otf"; sourceTree = "<group>"; };
		A3C292E0A8A7925D60FD18C0 /* ExpoModulesProvider.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ExpoModulesProvider.swift; path = "Pods/Target Support Files/Pods-App-omni staging/ExpoModulesProvider.swift"; sourceTree = "<group>"; };
		A5833FBE2B79388BBBAF268E /* Pods-App-omni.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-App-omni.release.xcconfig"; path = "Target Support Files/Pods-App-omni/Pods-App-omni.release.xcconfig"; sourceTree = "<group>"; };
		A65BE3DB6B1F4C469B0535C0 /* Ionicons.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = Ionicons.ttf; path = "../node_modules/react-native-vector-icons/Fonts/Ionicons.ttf"; sourceTree = "<group>"; };
		AA286B85B6C04FC6940260E9 /* SplashScreen.storyboard */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.storyboard; name = SplashScreen.storyboard; path = omni/SplashScreen.storyboard; sourceTree = "<group>"; };
		AFD03BC89F9D704FFF311EB0 /* Pods-App-omni storybook.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-App-omni storybook.debug.xcconfig"; path = "Target Support Files/Pods-App-omni storybook/Pods-App-omni storybook.debug.xcconfig"; sourceTree = "<group>"; };
		B857537F2785D457002C097B /* Omni-Bridging-Header.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "Omni-Bridging-Header.h"; sourceTree = "<group>"; };
		B85753802785D458002C097B /* noop-file.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "noop-file.swift"; sourceTree = "<group>"; };
		BB2F792C24A3F905000567C9 /* Expo.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; path = Expo.plist; sourceTree = "<group>"; };
		CED356A21FCDA8AD41EA9D07 /* Pods-App-omni.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-App-omni.debug.xcconfig"; path = "Target Support Files/Pods-App-omni/Pods-App-omni.debug.xcconfig"; sourceTree = "<group>"; };
		ED297162215061F000B7C4FE /* JavaScriptCore.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = JavaScriptCore.framework; path = System/Library/Frameworks/JavaScriptCore.framework; sourceTree = SDKROOT; };
		ED2971642150620600B7C4FE /* JavaScriptCore.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = JavaScriptCore.framework; path = Platforms/AppleTVOS.platform/Developer/SDKs/AppleTVOS12.0.sdk/System/Library/Frameworks/JavaScriptCore.framework; sourceTree = DEVELOPER_DIR; };
		F3D68F3E0DD0470F91040792 /* GTPlanar-Regular.otf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "GTPlanar-Regular.otf"; path = "../src/assets/fonts/GTPlanar-Regular.otf"; sourceTree = "<group>"; };
		F5C05F2BFE1A886ADC3AD9ED /* Pods-App-omni staging.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-App-omni staging.release.xcconfig"; path = "Target Support Files/Pods-App-omni staging/Pods-App-omni staging.release.xcconfig"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		13B07F8C1A680F5B00A75B9A /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				8E91BB21292E6B3700E450D9 /* AdServices.framework in Frameworks */,
				2E9FFBABBD431230517815C3 /* libPods-App-omni.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		8E158F9B29115A9C004CFF60 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				610F6EB7B39B9C922C1F30A6 /* libPods-App-omni dev.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		8E9EAB772865C546000BA6A8 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				334C9672B1B118273AB13462 /* libPods-App-omni storybook.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		8EBC686027D1FD3F00C0F5EF /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				0709949DCAF60F55FDF2BF2D /* libPods-App-omni staging.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		1253F6922E6792C8E0C0A1C9 /* ExpoModulesProviders */ = {
			isa = PBXGroup;
			children = (
				2A085F72EC8F4800B2B9BA1B /* omni */,
				28C0C8FCF4E473A4997E5636 /* omni staging */,
				8A1533F9910764FCB467BE4B /* omni storybook */,
				E0FFE162F04C7BA587EA8C2A /* omni dev */,
			);
			name = ExpoModulesProviders;
			sourceTree = "<group>";
		};
		13B07FAE1A68108700A75B9A /* omni */ = {
			isa = PBXGroup;
			children = (
				756C36F029A8C070007E9AFD /* NativeModules */,
				BB2F792B24A3F905000567C9 /* Supporting */,
				13B07FAF1A68108700A75B9A /* AppDelegate.h */,
				13B07FB01A68108700A75B9A /* AppDelegate.m */,
				13B07FB51A68108700A75B9A /* Images.xcassets */,
				13B07FB61A68108700A75B9A /* Info.plist */,
				8EBC687227D1FD3F00C0F5EF /* StagingInfo.plist */,
				8E158FAF29115A9D004CFF60 /* DevInfo.plist */,
				8E9EAB8A2865C547000BA6A8 /* StorybookInfo.plist */,
				13B07FB11A68108700A75B9A /* LaunchScreen.xib */,
				13B07FB71A68108700A75B9A /* main.m */,
				AA286B85B6C04FC6940260E9 /* SplashScreen.storyboard */,
				B85753802785D458002C097B /* noop-file.swift */,
				B857537F2785D457002C097B /* Omni-Bridging-Header.h */,
			);
			name = omni;
			sourceTree = "<group>";
		};
		28C0C8FCF4E473A4997E5636 /* omni staging */ = {
			isa = PBXGroup;
			children = (
				A3C292E0A8A7925D60FD18C0 /* ExpoModulesProvider.swift */,
			);
			name = "omni staging";
			sourceTree = "<group>";
		};
		2A085F72EC8F4800B2B9BA1B /* omni */ = {
			isa = PBXGroup;
			children = (
				0FEBE62F2AF906D3BEBA3DA1 /* ExpoModulesProvider.swift */,
			);
			name = omni;
			sourceTree = "<group>";
		};
		2D16E6871FA4F8E400B85C8A /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				8E91BB1F292E68FC00E450D9 /* AdServices.framework */,
				ED297162215061F000B7C4FE /* JavaScriptCore.framework */,
				ED2971642150620600B7C4FE /* JavaScriptCore.framework */,
				227E2357FB34FD39F83A3ADC /* libPods-App-omni.a */,
				0E1B47572CD4392EC442FEB8 /* libPods-App-omni dev.a */,
				4FDCFE57264F3299FA612474 /* libPods-App-omni staging.a */,
				892C68BDFE8B7096993769BA /* libPods-App-omni storybook.a */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		756C36EF29A8B8B7007E9AFD /* RNMinimizer */ = {
			isa = PBXGroup;
			children = (
				756C36EE29A8B827007E9AFD /* RNMinimizer.h */,
				756C36E929A8231F007E9AFD /* RNMinimizer.m */,
			);
			path = RNMinimizer;
			sourceTree = "<group>";
		};
		756C36F029A8C070007E9AFD /* NativeModules */ = {
			isa = PBXGroup;
			children = (
				756C36EF29A8B8B7007E9AFD /* RNMinimizer */,
			);
			path = NativeModules;
			sourceTree = "<group>";
		};
		832341AE1AAA6A7D00B99B32 /* Libraries */ = {
			isa = PBXGroup;
			children = (
			);
			name = Libraries;
			sourceTree = "<group>";
		};
		83CBB9F61A601CBA00E9B192 = {
			isa = PBXGroup;
			children = (
				8EE6B8CE27E488A900F50DF5 /* Firebase */,
				13B07FAE1A68108700A75B9A /* omni */,
				832341AE1AAA6A7D00B99B32 /* Libraries */,
				83CBBA001A601CBA00E9B192 /* Products */,
				2D16E6871FA4F8E400B85C8A /* Frameworks */,
				A2BF86E4EBEA4BEAAD4464BF /* Resources */,
				1253F6922E6792C8E0C0A1C9 /* ExpoModulesProviders */,
				CE75CF08B243354587E1091C /* Pods */,
			);
			indentWidth = 2;
			sourceTree = "<group>";
			tabWidth = 2;
			usesTabs = 0;
		};
		83CBBA001A601CBA00E9B192 /* Products */ = {
			isa = PBXGroup;
			children = (
				13B07F961A680F5B00A75B9A /* Omni (Debug).app */,
				8EBC687127D1FD3F00C0F5EF /* Omni Staging (Debug).app */,
				8E9EAB892865C546000BA6A8 /* Omni Storybook (Debug).app */,
				8E158FAE29115A9C004CFF60 /* Omni Dev (Debug).app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		8A1533F9910764FCB467BE4B /* omni storybook */ = {
			isa = PBXGroup;
			children = (
				3F56AF4190277C9289535730 /* ExpoModulesProvider.swift */,
			);
			name = "omni storybook";
			sourceTree = "<group>";
		};
		A2BF86E4EBEA4BEAAD4464BF /* Resources */ = {
			isa = PBXGroup;
			children = (
				A3ACB3AA8C824F6DB450C19F /* GTPlanar-Bold.otf */,
				58FF9F43984645F585FB199D /* GTPlanar-Italic.otf */,
				39D32990DAF64BB68F5030F5 /* GTPlanar-Medium.otf */,
				F3D68F3E0DD0470F91040792 /* GTPlanar-Regular.otf */,
				A65BE3DB6B1F4C469B0535C0 /* Ionicons.ttf */,
				75BE8E39296DCFCA00AD363E /* onboard1.mp4 */,
				75BE8E34296DCFB800AD363E /* ape_coin_success.gif */,
			);
			name = Resources;
			sourceTree = "<group>";
		};
		BB2F792B24A3F905000567C9 /* Supporting */ = {
			isa = PBXGroup;
			children = (
				BB2F792C24A3F905000567C9 /* Expo.plist */,
			);
			name = Supporting;
			path = omni/Supporting;
			sourceTree = "<group>";
		};
		CE75CF08B243354587E1091C /* Pods */ = {
			isa = PBXGroup;
			children = (
				CED356A21FCDA8AD41EA9D07 /* Pods-App-omni.debug.xcconfig */,
				A5833FBE2B79388BBBAF268E /* Pods-App-omni.release.xcconfig */,
				9B775429A4E119F04F36C57C /* Pods-App-omni dev.debug.xcconfig */,
				505A245B8D3B696BE6C656CB /* Pods-App-omni dev.release.xcconfig */,
				363DD28EB1CFBFDEB1D50DEE /* Pods-App-omni staging.debug.xcconfig */,
				F5C05F2BFE1A886ADC3AD9ED /* Pods-App-omni staging.release.xcconfig */,
				AFD03BC89F9D704FFF311EB0 /* Pods-App-omni storybook.debug.xcconfig */,
				1891E13E0DA3F15EF1CB1994 /* Pods-App-omni storybook.release.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
		E0FFE162F04C7BA587EA8C2A /* omni dev */ = {
			isa = PBXGroup;
			children = (
				5E4D2300A7100654C8180E19 /* ExpoModulesProvider.swift */,
			);
			name = "omni dev";
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		13B07F861A680F5B00A75B9A /* omni */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 13B07F931A680F5B00A75B9A /* Build configuration list for PBXNativeTarget "omni" */;
			buildPhases = (
				227EF052156CA603BC1B4180 /* [CP] Check Pods Manifest.lock */,
				FD10A7F022414F080027D42C /* Start Packager */,
				13B07F871A680F5B00A75B9A /* Sources */,
				13B07F8C1A680F5B00A75B9A /* Frameworks */,
				13B07F8E1A680F5B00A75B9A /* Resources */,
				00DD1BFF1BD5951E006B06BC /* Bundle React Native code and images */,
				8E47754527E37C7100DF16FB /* Setup Firebase Environment GoogleService-Info.plist */,
				E990DAC6D5AA4DBFBDD2248A /* Upload Debug Symbols to Sentry */,
				1D0E257327194A2260B939A7 /* [CP] Embed Pods Frameworks */,
				2BCFE453E593735040F22E70 /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = omni;
			productName = omni;
			productReference = 13B07F961A680F5B00A75B9A /* Omni (Debug).app */;
			productType = "com.apple.product-type.application";
		};
		8E158F9129115A9C004CFF60 /* omni dev */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 8E158FAB29115A9C004CFF60 /* Build configuration list for PBXNativeTarget "omni dev" */;
			buildPhases = (
				505EDB606BEFA0D6FF084B0B /* [CP] Check Pods Manifest.lock */,
				8E158F9329115A9C004CFF60 /* Start Packager */,
				8E158F9429115A9C004CFF60 /* Sources */,
				8E158F9B29115A9C004CFF60 /* Frameworks */,
				8E158F9E29115A9C004CFF60 /* Resources */,
				8E158FA729115A9C004CFF60 /* Bundle React Native code and images */,
				8E158FAA29115A9C004CFF60 /* Setup Firebase Environment GoogleService-Info.plist */,
				2518D9329AFE0473FE263690 /* [CP] Embed Pods Frameworks */,
				EC64790511D11681968D735B /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "omni dev";
			productName = omni;
			productReference = 8E158FAE29115A9C004CFF60 /* Omni Dev (Debug).app */;
			productType = "com.apple.product-type.application";
		};
		8E9EAB6D2865C546000BA6A8 /* omni storybook */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 8E9EAB862865C546000BA6A8 /* Build configuration list for PBXNativeTarget "omni storybook" */;
			buildPhases = (
				851909CED026CF0582B168C1 /* [CP] Check Pods Manifest.lock */,
				8E9EAB6F2865C546000BA6A8 /* Start Packager */,
				8E9EAB702865C546000BA6A8 /* Sources */,
				8E9EAB772865C546000BA6A8 /* Frameworks */,
				8E9EAB792865C546000BA6A8 /* Resources */,
				8E9EAB822865C546000BA6A8 /* Bundle React Native code and images */,
				8E9EAB852865C546000BA6A8 /* Setup Firebase Environment GoogleService-Info.plist */,
				7D64523866555516F3B156F1 /* [CP] Embed Pods Frameworks */,
				44BA7093888DAAC86F98E635 /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "omni storybook";
			productName = omni;
			productReference = 8E9EAB892865C546000BA6A8 /* Omni Storybook (Debug).app */;
			productType = "com.apple.product-type.application";
		};
		8EBC685827D1FD3F00C0F5EF /* omni staging */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 8EBC686E27D1FD3F00C0F5EF /* Build configuration list for PBXNativeTarget "omni staging" */;
			buildPhases = (
				FD7BB73B25B13263EDA99D74 /* [CP] Check Pods Manifest.lock */,
				8EBC685A27D1FD3F00C0F5EF /* Start Packager */,
				8EBC685B27D1FD3F00C0F5EF /* Sources */,
				8EBC686027D1FD3F00C0F5EF /* Frameworks */,
				8EBC686227D1FD3F00C0F5EF /* Resources */,
				8EBC686B27D1FD3F00C0F5EF /* Bundle React Native code and images */,
				8E47754727E37E0800DF16FB /* Setup Firebase Environment GoogleService-Info.plist */,
				51E203BB553B32945B357EC6 /* [CP] Embed Pods Frameworks */,
				35371E06FE39CEA4E2DD7DA6 /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "omni staging";
			productName = omni;
			productReference = 8EBC687127D1FD3F00C0F5EF /* Omni Staging (Debug).app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		83CBB9F71A601CBA00E9B192 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastUpgradeCheck = 1340;
				TargetAttributes = {
					13B07F861A680F5B00A75B9A = {
						DevelopmentTeam = WX7M2WRQM3;
						LastSwiftMigration = 1320;
						ProvisioningStyle = Manual;
					};
					8E158F9129115A9C004CFF60 = {
						DevelopmentTeam = WX7M2WRQM3;
						ProvisioningStyle = Manual;
					};
					8E9EAB6D2865C546000BA6A8 = {
						DevelopmentTeam = WX7M2WRQM3;
						ProvisioningStyle = Manual;
					};
					8EBC685827D1FD3F00C0F5EF = {
						DevelopmentTeam = WX7M2WRQM3;
						ProvisioningStyle = Manual;
					};
				};
			};
			buildConfigurationList = 83CBB9FA1A601CBA00E9B192 /* Build configuration list for PBXProject "omni" */;
			compatibilityVersion = "Xcode 3.2";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 83CBB9F61A601CBA00E9B192;
			productRefGroup = 83CBBA001A601CBA00E9B192 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				13B07F861A680F5B00A75B9A /* omni */,
				8EBC685827D1FD3F00C0F5EF /* omni staging */,
				8E158F9129115A9C004CFF60 /* omni dev */,
				8E9EAB6D2865C546000BA6A8 /* omni storybook */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		13B07F8E1A680F5B00A75B9A /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				BB2F792D24A3F905000567C9 /* Expo.plist in Resources */,
				13B07FBF1A68108700A75B9A /* Images.xcassets in Resources */,
				13B07FBD1A68108700A75B9A /* LaunchScreen.xib in Resources */,
				3E461D99554A48A4959DE609 /* SplashScreen.storyboard in Resources */,
				430A1CF6A064404397F618BF /* GTPlanar-Bold.otf in Resources */,
				75BE8E3A296DCFCA00AD363E /* onboard1.mp4 in Resources */,
				75BE8E35296DCFB800AD363E /* ape_coin_success.gif in Resources */,
				E65053EDBCD14D09875BB60D /* GTPlanar-Italic.otf in Resources */,
				227AC8581ADF4A51B108700B /* GTPlanar-Medium.otf in Resources */,
				B69F0D74F4D14016B11F0E38 /* GTPlanar-Regular.otf in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		8E158F9E29115A9C004CFF60 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				8E158F9F29115A9C004CFF60 /* Expo.plist in Resources */,
				8E158FA029115A9C004CFF60 /* Images.xcassets in Resources */,
				8E158FA129115A9C004CFF60 /* LaunchScreen.xib in Resources */,
				8E158FA229115A9C004CFF60 /* SplashScreen.storyboard in Resources */,
				8E158FA329115A9C004CFF60 /* GTPlanar-Bold.otf in Resources */,
				75BE8E3C296DCFCA00AD363E /* onboard1.mp4 in Resources */,
				75BE8E37296DCFB800AD363E /* ape_coin_success.gif in Resources */,
				8E158FA429115A9C004CFF60 /* GTPlanar-Italic.otf in Resources */,
				8E158FA529115A9C004CFF60 /* GTPlanar-Medium.otf in Resources */,
				8E158FA629115A9C004CFF60 /* GTPlanar-Regular.otf in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		8E9EAB792865C546000BA6A8 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				8E9EAB7A2865C546000BA6A8 /* Expo.plist in Resources */,
				8E9EAB7B2865C546000BA6A8 /* Images.xcassets in Resources */,
				8E9EAB7C2865C546000BA6A8 /* LaunchScreen.xib in Resources */,
				8E9EAB7D2865C546000BA6A8 /* SplashScreen.storyboard in Resources */,
				8E9EAB7E2865C546000BA6A8 /* GTPlanar-Bold.otf in Resources */,
				75BE8E3D296DCFCA00AD363E /* onboard1.mp4 in Resources */,
				75BE8E38296DCFB800AD363E /* ape_coin_success.gif in Resources */,
				8E9EAB7F2865C546000BA6A8 /* GTPlanar-Italic.otf in Resources */,
				8E9EAB802865C546000BA6A8 /* GTPlanar-Medium.otf in Resources */,
				8E9EAB812865C546000BA6A8 /* GTPlanar-Regular.otf in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		8EBC686227D1FD3F00C0F5EF /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				8EBC686327D1FD3F00C0F5EF /* Expo.plist in Resources */,
				8EBC686527D1FD3F00C0F5EF /* Images.xcassets in Resources */,
				8EBC686627D1FD3F00C0F5EF /* LaunchScreen.xib in Resources */,
				8EBC686727D1FD3F00C0F5EF /* SplashScreen.storyboard in Resources */,
				8EAD60E9285CB8A500279901 /* GTPlanar-Bold.otf in Resources */,
				75BE8E3B296DCFCA00AD363E /* onboard1.mp4 in Resources */,
				75BE8E36296DCFB800AD363E /* ape_coin_success.gif in Resources */,
				8EAD60EA285CB8A700279901 /* GTPlanar-Italic.otf in Resources */,
				8EAD60EB285CB8AC00279901 /* GTPlanar-Medium.otf in Resources */,
				8EAD60EC285CB8B100279901 /* GTPlanar-Regular.otf in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		00DD1BFF1BD5951E006B06BC /* Bundle React Native code and images */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
			);
			name = "Bundle React Native code and images";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "export SENTRY_PROPERTIES=sentry.properties\nexport SENTRY_LOG_LEVEL=debug\nexport EXTRA_PACKAGER_ARGS=\"--sourcemap-output $DERIVED_FILE_DIR/main.jsbundle.map\"\nexport NODE_BINARY=node\nexport PROJECT_ROOT=\"$PROJECT_DIR\"/..\n\nSENTRY_CLI_PATH=`node --print \"require('path').join(require('path').dirname(require.resolve('@sentry/cli/package.json')), 'bin', 'sentry-cli')\"`\n\nRN_XCODE_SH_PATH=`node --print \"require('path').join(require('path').dirname(require.resolve('react-native/package.json')), 'scripts', 'react-native-xcode.sh')\"`\n\n$RN_XCODE_SH_PATH\n\nEXPO_CONSTANTS_SH_PATH=`node --print \"require('path').join(require('path').dirname(require.resolve('expo-constants/package.json')), 'scripts', 'get-app-config-ios.sh')\"`\n\n$EXPO_CONSTANTS_SH_PATH\n";
		};
		1D0E257327194A2260B939A7 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-App-omni/Pods-App-omni-frameworks.sh",
				"${PODS_XCFRAMEWORKS_BUILD_DIR}/hermes-engine/hermes.framework/hermes",
				"${PODS_XCFRAMEWORKS_BUILD_DIR}/react-native-themis/themis.framework/themis",
			);
			name = "[CP] Embed Pods Frameworks";
			outputPaths = (
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/hermes.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/themis.framework",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-App-omni/Pods-App-omni-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		227EF052156CA603BC1B4180 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-App-omni-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		2518D9329AFE0473FE263690 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-App-omni dev/Pods-App-omni dev-frameworks.sh",
				"${PODS_XCFRAMEWORKS_BUILD_DIR}/hermes-engine/hermes.framework/hermes",
				"${PODS_XCFRAMEWORKS_BUILD_DIR}/react-native-themis/themis.framework/themis",
			);
			name = "[CP] Embed Pods Frameworks";
			outputPaths = (
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/hermes.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/themis.framework",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-App-omni dev/Pods-App-omni dev-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		2BCFE453E593735040F22E70 /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-App-omni/Pods-App-omni-resources.sh",
				"${PODS_CONFIGURATION_BUILD_DIR}/EXConstants/EXConstants.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/Ramp/Ramp.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/React-Core/AccessibilityResources.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/Sentry/Sentry.bundle",
				"${PODS_ROOT}/../../../node_modules/react-native-vector-icons/Fonts/Ionicons.ttf",
				"${PODS_CONFIGURATION_BUILD_DIR}/TrustKit/TrustKit.bundle",
			);
			name = "[CP] Copy Pods Resources";
			outputPaths = (
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/EXConstants.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/Ramp.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/AccessibilityResources.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/Sentry.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/Ionicons.ttf",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/TrustKit.bundle",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-App-omni/Pods-App-omni-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		35371E06FE39CEA4E2DD7DA6 /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-App-omni staging/Pods-App-omni staging-resources.sh",
				"${PODS_CONFIGURATION_BUILD_DIR}/EXConstants/EXConstants.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/Ramp/Ramp.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/React-Core/AccessibilityResources.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/Sentry/Sentry.bundle",
				"${PODS_ROOT}/../../../node_modules/react-native-vector-icons/Fonts/Ionicons.ttf",
				"${PODS_CONFIGURATION_BUILD_DIR}/TrustKit/TrustKit.bundle",
			);
			name = "[CP] Copy Pods Resources";
			outputPaths = (
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/EXConstants.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/Ramp.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/AccessibilityResources.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/Sentry.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/Ionicons.ttf",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/TrustKit.bundle",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-App-omni staging/Pods-App-omni staging-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		44BA7093888DAAC86F98E635 /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-App-omni storybook/Pods-App-omni storybook-resources.sh",
				"${PODS_CONFIGURATION_BUILD_DIR}/EXConstants/EXConstants.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/Ramp/Ramp.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/React-Core/AccessibilityResources.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/Sentry/Sentry.bundle",
				"${PODS_ROOT}/../../../node_modules/react-native-vector-icons/Fonts/Ionicons.ttf",
				"${PODS_CONFIGURATION_BUILD_DIR}/TrustKit/TrustKit.bundle",
			);
			name = "[CP] Copy Pods Resources";
			outputPaths = (
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/EXConstants.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/Ramp.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/AccessibilityResources.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/Sentry.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/Ionicons.ttf",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/TrustKit.bundle",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-App-omni storybook/Pods-App-omni storybook-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		505EDB606BEFA0D6FF084B0B /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-App-omni dev-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		51E203BB553B32945B357EC6 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-App-omni staging/Pods-App-omni staging-frameworks.sh",
				"${PODS_XCFRAMEWORKS_BUILD_DIR}/hermes-engine/hermes.framework/hermes",
				"${PODS_XCFRAMEWORKS_BUILD_DIR}/react-native-themis/themis.framework/themis",
			);
			name = "[CP] Embed Pods Frameworks";
			outputPaths = (
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/hermes.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/themis.framework",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-App-omni staging/Pods-App-omni staging-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		7D64523866555516F3B156F1 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-App-omni storybook/Pods-App-omni storybook-frameworks.sh",
				"${PODS_XCFRAMEWORKS_BUILD_DIR}/hermes-engine/hermes.framework/hermes",
				"${PODS_XCFRAMEWORKS_BUILD_DIR}/react-native-themis/themis.framework/themis",
			);
			name = "[CP] Embed Pods Frameworks";
			outputPaths = (
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/hermes.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/themis.framework",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-App-omni storybook/Pods-App-omni storybook-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		851909CED026CF0582B168C1 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-App-omni storybook-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		8E158F9329115A9C004CFF60 /* Start Packager */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
			);
			name = "Start Packager";
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "export PROJECT_ROOT=\"$PROJECT_DIR\"/..\nexport RCT_METRO_PORT=\"${RCT_METRO_PORT:=8081}\"\n\nPACKAGER_ENV_PATH=`node --print \"require('path').join(require('path').dirname(require.resolve('react-native/package.json')), 'scripts', '.packager.env')\"`\n\nLAUNCH_PACKAGER_CMD_PATH=`node --print \"require('path').join(require('path').dirname(require.resolve('react-native/package.json')), 'scripts', 'launchPackager.command')\"`\n\necho \"export RCT_METRO_PORT=${RCT_METRO_PORT}\" > $PACKAGER_ENV_PATH\nif [ -z \"${RCT_NO_LAUNCH_PACKAGER+xxx}\" ] ; then\n  if nc -w 5 -z localhost ${RCT_METRO_PORT} ; then\n    if ! curl -s \"http://localhost:${RCT_METRO_PORT}/status\" | grep -q \"packager-status:running\" ; then\n      echo \"Port ${RCT_METRO_PORT} already in use, packager is either not running or not running correctly\"\n      exit 2\n    fi\n  else\n    open $LAUNCH_PACKAGER_CMD_PATH\n  fi\nfi\n";
			showEnvVarsInLog = 0;
		};
		8E158FA729115A9C004CFF60 /* Bundle React Native code and images */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
			);
			name = "Bundle React Native code and images";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "export SENTRY_PROPERTIES=sentry.properties\nexport SENTRY_LOG_LEVEL=debug\nexport EXTRA_PACKAGER_ARGS=\"--sourcemap-output $DERIVED_FILE_DIR/main.jsbundle.map\"\nexport NODE_BINARY=node\nexport PROJECT_ROOT=\"$PROJECT_DIR\"/..\n\nSENTRY_CLI_PATH=`node --print \"require('path').join(require('path').dirname(require.resolve('@sentry/cli/package.json')), 'bin', 'sentry-cli')\"`\n\nRN_XCODE_SH_PATH=`node --print \"require('path').join(require('path').dirname(require.resolve('react-native/package.json')), 'scripts', 'react-native-xcode.sh')\"`\n\n$SENTRY_CLI_PATH react-native xcode $RN_XCODE_SH_PATH --force-foreground\n\nEXPO_CONSTANTS_SH_PATH=`node --print \"require('path').join(require('path').dirname(require.resolve('expo-constants/package.json')), 'scripts', 'get-app-config-ios.sh')\"`\n\n$EXPO_CONSTANTS_SH_PATH\n";
		};
		8E158FAA29115A9C004CFF60 /* Setup Firebase Environment GoogleService-Info.plist */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
			);
			name = "Setup Firebase Environment GoogleService-Info.plist";
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\n# NOTE: These should only live on the file system and should NOT be part of the target (since we'll be adding them to the target manually)\nGOOGLESERVICE_INFO=${PROJECT_DIR}/Firebase/omnidev/GoogleService-Info.plist\n\n# Make sure the staging version of GoogleService-Info.plist exists\necho \"Looking for GoogleService-Info.plist in ${GOOGLESERVICE_INFO}\"\nif [ ! -f $GOOGLESERVICE_INFO ]\nthen\n    echo \"No omnidev GoogleService-Info.plist found. Please ensure it's in the proper directory.\"\n    exit 1\nfi\n\n# Get a reference to the destination location for the GoogleService-Info.plist\nPLIST_DESTINATION=${BUILT_PRODUCTS_DIR}/${PRODUCT_NAME}.app\necho \"Will copy GoogleService-Info.plist to final destination: ${PLIST_DESTINATION}\"\n\n# Copy over the prod GoogleService-Info.plist for steakwallet builds\ncp \"${GOOGLESERVICE_INFO}\" \"${PLIST_DESTINATION}\"\n";
		};
		8E47754527E37C7100DF16FB /* Setup Firebase Environment GoogleService-Info.plist */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
			);
			name = "Setup Firebase Environment GoogleService-Info.plist";
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\n# NOTE: These should only live on the file system and should NOT be part of the target (since we'll be adding them to the target manually)\nGOOGLESERVICE_INFO=${PROJECT_DIR}/Firebase/omni/GoogleService-Info.plist\n\n# Make sure the prod version GoogleService-Info.plist exists\necho \"Looking for GoogleService-Info.plist in ${GOOGLESERVICE_INFO}\"\nif [ ! -f $GOOGLESERVICE_INFO ]\nthen\n    echo \"No omni GoogleService-Info.plist found. Please ensure it's in the proper directory.\"\n    exit 1\nfi\n\n# Get a reference to the destination location for the GoogleService-Info.plist\nPLIST_DESTINATION=${BUILT_PRODUCTS_DIR}/${PRODUCT_NAME}.app\necho \"Will copy GoogleService-Info.plist to final destination: ${PLIST_DESTINATION}\"\n\n# Copy over the prod GoogleService-Info.plist for steakwallet builds\ncp \"${GOOGLESERVICE_INFO}\" \"${PLIST_DESTINATION}\"\n";
		};
		8E47754727E37E0800DF16FB /* Setup Firebase Environment GoogleService-Info.plist */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
			);
			name = "Setup Firebase Environment GoogleService-Info.plist";
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\n# NOTE: These should only live on the file system and should NOT be part of the target (since we'll be adding them to the target manually)\nGOOGLESERVICE_INFO=${PROJECT_DIR}/Firebase/omnistaging/GoogleService-Info.plist\n\n# Make sure the staging version of GoogleService-Info.plist exists\necho \"Looking for GoogleService-Info.plist in ${GOOGLESERVICE_INFO}\"\nif [ ! -f $GOOGLESERVICE_INFO ]\nthen\n    echo \"No omnistaging GoogleService-Info.plist found. Please ensure it's in the proper directory.\"\n    exit 1\nfi\n\n# Get a reference to the destination location for the GoogleService-Info.plist\nPLIST_DESTINATION=${BUILT_PRODUCTS_DIR}/${PRODUCT_NAME}.app\necho \"Will copy GoogleService-Info.plist to final destination: ${PLIST_DESTINATION}\"\n\n# Copy over the prod GoogleService-Info.plist for steakwallet builds\ncp \"${GOOGLESERVICE_INFO}\" \"${PLIST_DESTINATION}\"\n";
		};
		8E9EAB6F2865C546000BA6A8 /* Start Packager */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
			);
			name = "Start Packager";
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "export PROJECT_ROOT=\"$PROJECT_DIR\"/..\nexport RCT_METRO_PORT=\"${RCT_METRO_PORT:=8081}\"\n\nPACKAGER_ENV_PATH=`node --print \"require('path').join(require('path').dirname(require.resolve('react-native/package.json')), 'scripts', '.packager.env')\"`\n\nLAUNCH_PACKAGER_CMD_PATH=`node --print \"require('path').join(require('path').dirname(require.resolve('react-native/package.json')), 'scripts', 'launchPackager.command')\"`\n\necho \"export RCT_METRO_PORT=${RCT_METRO_PORT}\" > $PACKAGER_ENV_PATH\nif [ -z \"${RCT_NO_LAUNCH_PACKAGER+xxx}\" ] ; then\n  if nc -w 5 -z localhost ${RCT_METRO_PORT} ; then\n    if ! curl -s \"http://localhost:${RCT_METRO_PORT}/status\" | grep -q \"packager-status:running\" ; then\n      echo \"Port ${RCT_METRO_PORT} already in use, packager is either not running or not running correctly\"\n      exit 2\n    fi\n  else\n    open $LAUNCH_PACKAGER_CMD_PATH\n  fi\nfi\n";
			showEnvVarsInLog = 0;
		};
		8E9EAB822865C546000BA6A8 /* Bundle React Native code and images */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
			);
			name = "Bundle React Native code and images";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "export SENTRY_PROPERTIES=sentry.properties\nexport SENTRY_LOG_LEVEL=debug\nexport EXTRA_PACKAGER_ARGS=\"--sourcemap-output $DERIVED_FILE_DIR/main.jsbundle.map\"\nexport NODE_BINARY=node\nexport PROJECT_ROOT=\"$PROJECT_DIR\"/..\n\nSENTRY_CLI_PATH=`node --print \"require('path').join(require('path').dirname(require.resolve('@sentry/cli/package.json')), 'bin', 'sentry-cli')\"`\n\nRN_XCODE_SH_PATH=`node --print \"require('path').join(require('path').dirname(require.resolve('react-native/package.json')), 'scripts', 'react-native-xcode.sh')\"`\n\n$SENTRY_CLI_PATH react-native xcode $RN_XCODE_SH_PATH --force-foreground\n\nEXPO_CONSTANTS_SH_PATH=`node --print \"require('path').join(require('path').dirname(require.resolve('expo-constants/package.json')), 'scripts', 'get-app-config-ios.sh')\"`\n\n$EXPO_CONSTANTS_SH_PATH\n";
		};
		8E9EAB852865C546000BA6A8 /* Setup Firebase Environment GoogleService-Info.plist */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
			);
			name = "Setup Firebase Environment GoogleService-Info.plist";
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\n# NOTE: These should only live on the file system and should NOT be part of the target (since we'll be adding them to the target manually)\nGOOGLESERVICE_INFO=${PROJECT_DIR}/Firebase/omnistorybook/GoogleService-Info.plist\n\n# Make sure the staging version of GoogleService-Info.plist exists\necho \"Looking for GoogleService-Info.plist in ${GOOGLESERVICE_INFO}\"\nif [ ! -f $GOOGLESERVICE_INFO ]\nthen\n    echo \"No omnistorybook GoogleService-Info.plist found. Please ensure it's in the proper directory.\"\n    exit 1\nfi\n\n# Get a reference to the destination location for the GoogleService-Info.plist\nPLIST_DESTINATION=${BUILT_PRODUCTS_DIR}/${PRODUCT_NAME}.app\necho \"Will copy GoogleService-Info.plist to final destination: ${PLIST_DESTINATION}\"\n\n# Copy over the prod GoogleService-Info.plist for steakwallet builds\ncp \"${GOOGLESERVICE_INFO}\" \"${PLIST_DESTINATION}\"\n";
		};
		8EBC685A27D1FD3F00C0F5EF /* Start Packager */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
			);
			name = "Start Packager";
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "export PROJECT_ROOT=\"$PROJECT_DIR\"/..\nexport RCT_METRO_PORT=\"${RCT_METRO_PORT:=8081}\"\n\nPACKAGER_ENV_PATH=`node --print \"require('path').join(require('path').dirname(require.resolve('react-native/package.json')), 'scripts', '.packager.env')\"`\n\nLAUNCH_PACKAGER_CMD_PATH=`node --print \"require('path').join(require('path').dirname(require.resolve('react-native/package.json')), 'scripts', 'launchPackager.command')\"`\n\necho \"export RCT_METRO_PORT=${RCT_METRO_PORT}\" > $PACKAGER_ENV_PATH\nif [ -z \"${RCT_NO_LAUNCH_PACKAGER+xxx}\" ] ; then\n  if nc -w 5 -z localhost ${RCT_METRO_PORT} ; then\n    if ! curl -s \"http://localhost:${RCT_METRO_PORT}/status\" | grep -q \"packager-status:running\" ; then\n      echo \"Port ${RCT_METRO_PORT} already in use, packager is either not running or not running correctly\"\n      exit 2\n    fi\n  else\n    open $LAUNCH_PACKAGER_CMD_PATH\n  fi\nfi\n";
			showEnvVarsInLog = 0;
		};
		8EBC686B27D1FD3F00C0F5EF /* Bundle React Native code and images */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
			);
			name = "Bundle React Native code and images";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "export SENTRY_PROPERTIES=sentry.properties\nexport SENTRY_LOG_LEVEL=debug\nexport EXTRA_PACKAGER_ARGS=\"--sourcemap-output $DERIVED_FILE_DIR/main.jsbundle.map\"\nexport NODE_BINARY=node\nexport PROJECT_ROOT=\"$PROJECT_DIR\"/..\n\nSENTRY_CLI_PATH=`node --print \"require('path').join(require('path').dirname(require.resolve('@sentry/cli/package.json')), 'bin', 'sentry-cli')\"`\n\nRN_XCODE_SH_PATH=`node --print \"require('path').join(require('path').dirname(require.resolve('react-native/package.json')), 'scripts', 'react-native-xcode.sh')\"`\n\n$SENTRY_CLI_PATH react-native xcode $RN_XCODE_SH_PATH --force-foreground\n\nEXPO_CONSTANTS_SH_PATH=`node --print \"require('path').join(require('path').dirname(require.resolve('expo-constants/package.json')), 'scripts', 'get-app-config-ios.sh')\"`\n\n$EXPO_CONSTANTS_SH_PATH\n";
		};
		E990DAC6D5AA4DBFBDD2248A /* Upload Debug Symbols to Sentry */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
			);
			name = "Upload Debug Symbols to Sentry";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "export SENTRY_PROPERTIES=sentry.properties\nexport SENTRY_LOG_LEVEL=debug\n../../node_modules/@sentry/cli/bin/sentry-cli upload-dsym\n";
		};
		EC64790511D11681968D735B /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-App-omni dev/Pods-App-omni dev-resources.sh",
				"${PODS_CONFIGURATION_BUILD_DIR}/EXConstants/EXConstants.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/Ramp/Ramp.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/React-Core/AccessibilityResources.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/Sentry/Sentry.bundle",
				"${PODS_ROOT}/../../../node_modules/react-native-vector-icons/Fonts/Ionicons.ttf",
				"${PODS_CONFIGURATION_BUILD_DIR}/TrustKit/TrustKit.bundle",
			);
			name = "[CP] Copy Pods Resources";
			outputPaths = (
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/EXConstants.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/Ramp.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/AccessibilityResources.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/Sentry.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/Ionicons.ttf",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/TrustKit.bundle",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-App-omni dev/Pods-App-omni dev-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		FD10A7F022414F080027D42C /* Start Packager */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
			);
			name = "Start Packager";
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "export PROJECT_ROOT=\"$PROJECT_DIR\"/..\nexport RCT_METRO_PORT=\"${RCT_METRO_PORT:=8081}\"\n\nPACKAGER_ENV_PATH=`node --print \"require('path').join(require('path').dirname(require.resolve('react-native/package.json')), 'scripts', '.packager.env')\"`\n\nLAUNCH_PACKAGER_CMD_PATH=`node --print \"require('path').join(require('path').dirname(require.resolve('react-native/package.json')), 'scripts', 'launchPackager.command')\"`\n\necho \"export RCT_METRO_PORT=${RCT_METRO_PORT}\" > $PACKAGER_ENV_PATH\nif [ -z \"${RCT_NO_LAUNCH_PACKAGER+xxx}\" ] ; then\n  if nc -w 5 -z localhost ${RCT_METRO_PORT} ; then\n    if ! curl -s \"http://localhost:${RCT_METRO_PORT}/status\" | grep -q \"packager-status:running\" ; then\n      echo \"Port ${RCT_METRO_PORT} already in use, packager is either not running or not running correctly\"\n      exit 2\n    fi\n  else\n    open $LAUNCH_PACKAGER_CMD_PATH\n  fi\nfi\n";
			showEnvVarsInLog = 0;
		};
		FD7BB73B25B13263EDA99D74 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-App-omni staging-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		13B07F871A680F5B00A75B9A /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				B85753812785D458002C097B /* noop-file.swift in Sources */,
				13B07FBC1A68108700A75B9A /* AppDelegate.m in Sources */,
				756C36EA29A8231F007E9AFD /* RNMinimizer.m in Sources */,
				13B07FC11A68108700A75B9A /* main.m in Sources */,
				6EE797FC741360B37928E602 /* ExpoModulesProvider.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		8E158F9429115A9C004CFF60 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				8E158F9529115A9C004CFF60 /* noop-file.swift in Sources */,
				8E158F9629115A9C004CFF60 /* AppDelegate.m in Sources */,
				756C36EC29A8231F007E9AFD /* RNMinimizer.m in Sources */,
				8E158F9729115A9C004CFF60 /* main.m in Sources */,
				41A17D7647FB1AC2B973AAA6 /* ExpoModulesProvider.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		8E9EAB702865C546000BA6A8 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				8E9EAB712865C546000BA6A8 /* noop-file.swift in Sources */,
				8E9EAB722865C546000BA6A8 /* AppDelegate.m in Sources */,
				756C36ED29A8231F007E9AFD /* RNMinimizer.m in Sources */,
				8E9EAB732865C546000BA6A8 /* main.m in Sources */,
				FEE0C013DDBA25BD39209CC4 /* ExpoModulesProvider.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		8EBC685B27D1FD3F00C0F5EF /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				8EBC685C27D1FD3F00C0F5EF /* noop-file.swift in Sources */,
				8EBC685D27D1FD3F00C0F5EF /* AppDelegate.m in Sources */,
				756C36EB29A8231F007E9AFD /* RNMinimizer.m in Sources */,
				8EBC685E27D1FD3F00C0F5EF /* main.m in Sources */,
				DDD253E92A3B3B7FED32E250 /* ExpoModulesProvider.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXVariantGroup section */
		13B07FB11A68108700A75B9A /* LaunchScreen.xib */ = {
			isa = PBXVariantGroup;
			children = (
				13B07FB21A68108700A75B9A /* Base */,
			);
			name = LaunchScreen.xib;
			path = omni;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		13B07F941A680F5B00A75B9A /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = CED356A21FCDA8AD41EA9D07 /* Pods-App-omni.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = "omni icon";
				ASSETCATALOG_COMPILER_INCLUDE_ALL_APPICON_ASSETS = NO;
				ASSETCATALOG_COMPILER_OPTIMIZATION = time;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = omni/Omni.entitlements;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = WX7M2WRQM3;
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = WX7M2WRQM3;
				ENABLE_BITCODE = NO;
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"$(inherited)",
					"FB_SONARKIT_ENABLED=1",
				);
				GCC_UNROLL_LOOPS = YES;
				INFOPLIST_FILE = omni/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				LIBRARY_SEARCH_PATHS = (
					"$(SDKROOT)/usr/lib/swift",
					"$(inherited)",
					"$(PROJECT_DIR)/Pods/Branch",
				);
				LLVM_LTO = YES;
				MARKETING_VERSION = 3.1.0;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.thesteakwallet.app;
				PRODUCT_NAME = "Omni (Debug)";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = "match Development com.thesteakwallet.app";
				SWIFT_OBJC_BRIDGING_HEADER = "omni-Bridging-Header.h";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.2;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Debug;
		};
		13B07F951A680F5B00A75B9A /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = A5833FBE2B79388BBBAF268E /* Pods-App-omni.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = "omni icon";
				ASSETCATALOG_COMPILER_INCLUDE_ALL_APPICON_ASSETS = NO;
				ASSETCATALOG_COMPILER_OPTIMIZATION = space;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = omni/OmniRelease.entitlements;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Distribution";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = WX7M2WRQM3;
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = WX7M2WRQM3;
				GCC_OPTIMIZATION_LEVEL = 3;
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_UNROLL_LOOPS = YES;
				INFOPLIST_FILE = omni/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				LIBRARY_SEARCH_PATHS = (
					"$(SDKROOT)/usr/lib/swift",
					"$(inherited)",
					"$(PROJECT_DIR)/Pods/Branch",
				);
				LLVM_LTO = YES;
				MARKETING_VERSION = 3.1.0;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.thesteakwallet.app;
				PRODUCT_NAME = Omni;
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = "match AppStore com.thesteakwallet.app";
				SWIFT_OBJC_BRIDGING_HEADER = "omni-Bridging-Header.h";
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				SWIFT_VERSION = 5.2;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Release;
		};
		83CBBA201A601CBA00E9B192 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = i386;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = "/usr/lib/swift $(inherited)";
				LIBRARY_SEARCH_PATHS = (
					"$(SDKROOT)/usr/lib/swift",
					"\"$(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME)\"",
					"\"$(inherited)\"",
				);
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
			};
			name = Debug;
		};
		83CBBA211A601CBA00E9B192 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = YES;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = i386;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = "/usr/lib/swift $(inherited)";
				LIBRARY_SEARCH_PATHS = (
					"$(SDKROOT)/usr/lib/swift",
					"\"$(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME)\"",
					"\"$(inherited)\"",
				);
				MTL_ENABLE_DEBUG_INFO = NO;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		8E158FAC29115A9C004CFF60 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 9B775429A4E119F04F36C57C /* Pods-App-omni dev.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = "omni dev icon";
				ASSETCATALOG_COMPILER_OPTIMIZATION = time;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = omni/Omni.entitlements;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = WX7M2WRQM3;
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = WX7M2WRQM3;
				ENABLE_BITCODE = NO;
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"$(inherited)",
					"FB_SONARKIT_ENABLED=1",
				);
				GCC_UNROLL_LOOPS = YES;
				INFOPLIST_FILE = omni/DevInfo.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				LIBRARY_SEARCH_PATHS = (
					"$(SDKROOT)/usr/lib/swift",
					"$(inherited)",
					"$(PROJECT_DIR)/Pods/Branch",
				);
				LLVM_LTO = YES;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.thesteakwallet.app.dev;
				PRODUCT_NAME = "Omni Dev (Debug)";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = "match Development com.thesteakwallet.app.dev";
				SWIFT_OBJC_BRIDGING_HEADER = "omni-Bridging-Header.h";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.2;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Debug;
		};
		8E158FAD29115A9C004CFF60 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 505A245B8D3B696BE6C656CB /* Pods-App-omni dev.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = "omni dev icon";
				ASSETCATALOG_COMPILER_OPTIMIZATION = space;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = omni/OmniRelease.entitlements;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = WX7M2WRQM3;
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = WX7M2WRQM3;
				GCC_OPTIMIZATION_LEVEL = 3;
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_UNROLL_LOOPS = YES;
				INFOPLIST_FILE = omni/DevInfo.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				LIBRARY_SEARCH_PATHS = (
					"$(SDKROOT)/usr/lib/swift",
					"$(inherited)",
					"$(PROJECT_DIR)/Pods/Branch",
				);
				LLVM_LTO = YES;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.thesteakwallet.app.dev;
				PRODUCT_NAME = "Omni Dev";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = "match Development com.thesteakwallet.app.dev";
				SWIFT_OBJC_BRIDGING_HEADER = "omni-Bridging-Header.h";
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				SWIFT_VERSION = 5.2;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Release;
		};
		8E9EAB872865C546000BA6A8 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = AFD03BC89F9D704FFF311EB0 /* Pods-App-omni storybook.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = "omni storybook icon";
				ASSETCATALOG_COMPILER_OPTIMIZATION = time;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = omni/Omni.entitlements;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = WX7M2WRQM3;
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = WX7M2WRQM3;
				ENABLE_BITCODE = NO;
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"$(inherited)",
					"FB_SONARKIT_ENABLED=1",
				);
				GCC_UNROLL_LOOPS = YES;
				INFOPLIST_FILE = omni/StorybookInfo.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				LIBRARY_SEARCH_PATHS = (
					"$(SDKROOT)/usr/lib/swift",
					"$(inherited)",
					"$(PROJECT_DIR)/Pods/Branch",
				);
				LLVM_LTO = YES;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.thesteakwallet.app.storybook;
				PRODUCT_NAME = "Omni Storybook (Debug)";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = "match Development com.thesteakwallet.app.storybook";
				SWIFT_OBJC_BRIDGING_HEADER = "omni-Bridging-Header.h";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.2;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Debug;
		};
		8E9EAB882865C546000BA6A8 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 1891E13E0DA3F15EF1CB1994 /* Pods-App-omni storybook.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = "omni storybook icon";
				ASSETCATALOG_COMPILER_OPTIMIZATION = space;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = omni/OmniRelease.entitlements;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = WX7M2WRQM3;
				GCC_OPTIMIZATION_LEVEL = 3;
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_UNROLL_LOOPS = YES;
				INFOPLIST_FILE = omni/StorybookInfo.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				LIBRARY_SEARCH_PATHS = (
					"$(SDKROOT)/usr/lib/swift",
					"$(inherited)",
					"$(PROJECT_DIR)/Pods/Branch",
				);
				LLVM_LTO = YES;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.thesteakwallet.app.storybook;
				PRODUCT_NAME = "Omni Storybook";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = "match Development com.thesteakwallet.app.storybook";
				SWIFT_OBJC_BRIDGING_HEADER = "omni-Bridging-Header.h";
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				SWIFT_VERSION = 5.2;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Release;
		};
		8EBC686F27D1FD3F00C0F5EF /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 363DD28EB1CFBFDEB1D50DEE /* Pods-App-omni staging.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = "omni staging icon";
				ASSETCATALOG_COMPILER_OPTIMIZATION = time;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = omni/Omni.entitlements;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = WX7M2WRQM3;
				ENABLE_BITCODE = NO;
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"$(inherited)",
					"FB_SONARKIT_ENABLED=1",
				);
				GCC_UNROLL_LOOPS = YES;
				INFOPLIST_FILE = omni/StagingInfo.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				LIBRARY_SEARCH_PATHS = (
					"$(SDKROOT)/usr/lib/swift",
					"$(inherited)",
					"$(PROJECT_DIR)/Pods/Branch",
				);
				LLVM_LTO = YES;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.thesteakwallet.app.staging;
				PRODUCT_NAME = "Omni Staging (Debug)";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = "match Development com.thesteakwallet.app.staging";
				SWIFT_OBJC_BRIDGING_HEADER = "omni-Bridging-Header.h";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.2;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Debug;
		};
		8EBC687027D1FD3F00C0F5EF /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = F5C05F2BFE1A886ADC3AD9ED /* Pods-App-omni staging.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = "omni staging icon";
				ASSETCATALOG_COMPILER_OPTIMIZATION = space;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = omni/OmniRelease.entitlements;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = WX7M2WRQM3;
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = WX7M2WRQM3;
				GCC_OPTIMIZATION_LEVEL = 3;
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_UNROLL_LOOPS = YES;
				INFOPLIST_FILE = omni/StagingInfo.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				LIBRARY_SEARCH_PATHS = (
					"$(SDKROOT)/usr/lib/swift",
					"$(inherited)",
					"$(PROJECT_DIR)/Pods/Branch",
				);
				LLVM_LTO = YES;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.thesteakwallet.app.staging;
				PRODUCT_NAME = "Omni Staging";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = "match Development com.thesteakwallet.app.staging";
				SWIFT_OBJC_BRIDGING_HEADER = "omni-Bridging-Header.h";
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				SWIFT_VERSION = 5.2;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		13B07F931A680F5B00A75B9A /* Build configuration list for PBXNativeTarget "omni" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				13B07F941A680F5B00A75B9A /* Debug */,
				13B07F951A680F5B00A75B9A /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		83CBB9FA1A601CBA00E9B192 /* Build configuration list for PBXProject "omni" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				83CBBA201A601CBA00E9B192 /* Debug */,
				83CBBA211A601CBA00E9B192 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		8E158FAB29115A9C004CFF60 /* Build configuration list for PBXNativeTarget "omni dev" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				8E158FAC29115A9C004CFF60 /* Debug */,
				8E158FAD29115A9C004CFF60 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		8E9EAB862865C546000BA6A8 /* Build configuration list for PBXNativeTarget "omni storybook" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				8E9EAB872865C546000BA6A8 /* Debug */,
				8E9EAB882865C546000BA6A8 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		8EBC686E27D1FD3F00C0F5EF /* Build configuration list for PBXNativeTarget "omni staging" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				8EBC686F27D1FD3F00C0F5EF /* Debug */,
				8EBC687027D1FD3F00C0F5EF /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 83CBB9F71A601CBA00E9B192 /* Project object */;
}
