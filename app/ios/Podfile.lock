PODS:
  - Base64 (1.1.2)
  - BCrypt (1.0.0)
  - bcrypt-react-native (1.1.1):
    - BCrypt
    - React
  - boost (1.76.0)
  - Branch (1.43.2)
  - CocoaAsyncSocket (7.6.5)
  - CodePush (7.0.4):
    - Base64 (~> 1.1)
    - JWT (~> 3.0.0-beta.12)
    - React-Core
    - SSZipArchive (~> 2.2.2)
  - DoubleConversion (1.1.6)
  - EXApplication (4.0.2):
    - ExpoModulesCore
  - EXConstants (13.2.4):
    - ExpoModulesCore
  - EXFileSystem (13.2.0):
    - ExpoModulesCore
  - EXFont (10.2.1):
    - ExpoModulesCore
  - EXKeepAwake (10.0.2):
    - ExpoModulesCore
  - EXLocalAuthentication (12.1.1):
    - ExpoModulesCore
  - EXNotifications (0.14.1):
    - ExpoModulesCore
  - Expo (44.0.6):
    - ExpoModulesCore
  - ExpoLinearGradient (11.0.3):
    - ExpoModulesCore
  - ExpoModulesCore (0.6.5):
    - React-Core
    - ReactCommon/turbomodule/core
  - EXRandom (12.1.2):
    - React-Core
  - EXScreenCapture (4.1.1):
    - ExpoModulesCore
  - EXSecureStore (11.1.1):
    - ExpoModulesCore
  - EXSplashScreen (0.16.2):
    - ExpoModulesCore
    - React-Core
  - FBLazyVector (0.67.5)
  - FBReactNativeSpec (0.67.5):
    - RCT-Folly (= 2021.06.28.00-v2)
    - RCTRequired (= 0.67.5)
    - RCTTypeSafety (= 0.67.5)
    - React-Core (= 0.67.5)
    - React-jsi (= 0.67.5)
    - ReactCommon/turbomodule/core (= 0.67.5)
  - Flipper (0.182.0):
    - Flipper-Folly (~> 2.6)
  - Flipper-Boost-iOSX (********.11)
  - Flipper-DoubleConversion (3.1.7)
  - Flipper-Fmt (7.1.7)
  - Flipper-Folly (2.6.7):
    - Flipper-Boost-iOSX
    - Flipper-DoubleConversion
    - Flipper-Fmt (= 7.1.7)
    - Flipper-Glog
    - libevent (~> 2.1.12)
    - OpenSSL-Universal (= 1.1.180)
  - Flipper-Glog (0.3.6)
  - Flipper-PeerTalk (0.0.4)
  - Flipper-RSocket (1.4.3):
    - Flipper-Folly (~> 2.6)
  - FlipperKit (0.182.0):
    - FlipperKit/Core (= 0.182.0)
  - FlipperKit/Core (0.182.0):
    - Flipper (~> 0.182.0)
    - FlipperKit/CppBridge
    - FlipperKit/FBCxxFollyDynamicConvert
    - FlipperKit/FBDefines
    - FlipperKit/FKPortForwarding
    - SocketRocket (~> 0.6.0)
  - FlipperKit/CppBridge (0.182.0):
    - Flipper (~> 0.182.0)
  - FlipperKit/FBCxxFollyDynamicConvert (0.182.0):
    - Flipper-Folly (~> 2.6)
  - FlipperKit/FBDefines (0.182.0)
  - FlipperKit/FKPortForwarding (0.182.0):
    - CocoaAsyncSocket (~> 7.6)
    - Flipper-PeerTalk (~> 0.0.4)
  - FlipperKit/FlipperKitHighlightOverlay (0.182.0)
  - FlipperKit/FlipperKitLayoutHelpers (0.182.0):
    - FlipperKit/Core
    - FlipperKit/FlipperKitHighlightOverlay
    - FlipperKit/FlipperKitLayoutTextSearchable
  - FlipperKit/FlipperKitLayoutIOSDescriptors (0.182.0):
    - FlipperKit/Core
    - FlipperKit/FlipperKitHighlightOverlay
    - FlipperKit/FlipperKitLayoutHelpers
    - YogaKit (~> 1.18)
  - FlipperKit/FlipperKitLayoutPlugin (0.182.0):
    - FlipperKit/Core
    - FlipperKit/FlipperKitHighlightOverlay
    - FlipperKit/FlipperKitLayoutHelpers
    - FlipperKit/FlipperKitLayoutIOSDescriptors
    - FlipperKit/FlipperKitLayoutTextSearchable
    - YogaKit (~> 1.18)
  - FlipperKit/FlipperKitLayoutTextSearchable (0.182.0)
  - FlipperKit/FlipperKitNetworkPlugin (0.182.0):
    - FlipperKit/Core
  - FlipperKit/FlipperKitReactPlugin (0.182.0):
    - FlipperKit/Core
  - FlipperKit/FlipperKitUserDefaultsPlugin (0.182.0):
    - FlipperKit/Core
  - FlipperKit/SKIOSNetworkPlugin (0.182.0):
    - FlipperKit/Core
    - FlipperKit/FlipperKitNetworkPlugin
  - fmt (6.2.1)
  - glog (0.3.5)
  - hermes-engine (0.9.0)
  - jail-monkey (2.7.0):
    - React-Core
  - JWT (3.0.0-beta.14):
    - Base64 (~> 1.1.2)
  - KTVCocoaHTTPServer (1.0.0):
    - CocoaAsyncSocket
  - KTVHTTPCache (2.0.1):
    - KTVCocoaHTTPServer
  - libevent (2.1.12)
  - libwebp (1.3.2):
    - libwebp/demux (= 1.3.2)
    - libwebp/mux (= 1.3.2)
    - libwebp/sharpyuv (= 1.3.2)
    - libwebp/webp (= 1.3.2)
  - libwebp/demux (1.3.2):
    - libwebp/webp
  - libwebp/mux (1.3.2):
    - libwebp/demux
  - libwebp/sharpyuv (1.3.2)
  - libwebp/webp (1.3.2):
    - libwebp/sharpyuv
  - lottie-ios (3.2.3)
  - lottie-react-native (5.0.1):
    - lottie-ios (~> 3.2.3)
    - React-Core
  - Mixpanel-swift (3.1.2):
    - Mixpanel-swift/Complete (= 3.1.2)
  - Mixpanel-swift/Complete (3.1.2)
  - MixpanelReactNative (1.3.7):
    - Mixpanel-swift (= 3.1.2)
    - React
  - MMKV (1.3.9):
    - MMKVCore (~> 1.3.9)
  - MMKVCore (1.3.9)
  - OpenSSL-Universal (1.1.180)
  - Permission-Camera (3.2.0):
    - RNPermissions
  - Permission-Notifications (3.2.0):
    - RNPermissions
  - Ramp (4.0.1)
  - ramp-network-react-native-sdk (1.0.2):
    - Ramp
    - React-Core
  - RCT-Folly (2021.06.28.00-v2):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - RCT-Folly/Default (= 2021.06.28.00-v2)
  - RCT-Folly/Default (2021.06.28.00-v2):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
  - RCT-Folly/Futures (2021.06.28.00-v2):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - libevent
  - RCTRequired (0.67.5)
  - RCTTypeSafety (0.67.5):
    - FBLazyVector (= 0.67.5)
    - RCT-Folly (= 2021.06.28.00-v2)
    - RCTRequired (= 0.67.5)
    - React-Core (= 0.67.5)
  - React (0.67.5):
    - React-Core (= 0.67.5)
    - React-Core/DevSupport (= 0.67.5)
    - React-Core/RCTWebSocket (= 0.67.5)
    - React-RCTActionSheet (= 0.67.5)
    - React-RCTAnimation (= 0.67.5)
    - React-RCTBlob (= 0.67.5)
    - React-RCTImage (= 0.67.5)
    - React-RCTLinking (= 0.67.5)
    - React-RCTNetwork (= 0.67.5)
    - React-RCTSettings (= 0.67.5)
    - React-RCTText (= 0.67.5)
    - React-RCTVibration (= 0.67.5)
  - React-callinvoker (0.67.5)
  - React-Core (0.67.5):
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-Core/Default (= 0.67.5)
    - React-cxxreact (= 0.67.5)
    - React-jsi (= 0.67.5)
    - React-jsiexecutor (= 0.67.5)
    - React-perflogger (= 0.67.5)
    - Yoga
  - React-Core/CoreModulesHeaders (0.67.5):
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-Core/Default
    - React-cxxreact (= 0.67.5)
    - React-jsi (= 0.67.5)
    - React-jsiexecutor (= 0.67.5)
    - React-perflogger (= 0.67.5)
    - Yoga
  - React-Core/Default (0.67.5):
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-cxxreact (= 0.67.5)
    - React-jsi (= 0.67.5)
    - React-jsiexecutor (= 0.67.5)
    - React-perflogger (= 0.67.5)
    - Yoga
  - React-Core/DevSupport (0.67.5):
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-Core/Default (= 0.67.5)
    - React-Core/RCTWebSocket (= 0.67.5)
    - React-cxxreact (= 0.67.5)
    - React-jsi (= 0.67.5)
    - React-jsiexecutor (= 0.67.5)
    - React-jsinspector (= 0.67.5)
    - React-perflogger (= 0.67.5)
    - Yoga
  - React-Core/RCTActionSheetHeaders (0.67.5):
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-Core/Default
    - React-cxxreact (= 0.67.5)
    - React-jsi (= 0.67.5)
    - React-jsiexecutor (= 0.67.5)
    - React-perflogger (= 0.67.5)
    - Yoga
  - React-Core/RCTAnimationHeaders (0.67.5):
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-Core/Default
    - React-cxxreact (= 0.67.5)
    - React-jsi (= 0.67.5)
    - React-jsiexecutor (= 0.67.5)
    - React-perflogger (= 0.67.5)
    - Yoga
  - React-Core/RCTBlobHeaders (0.67.5):
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-Core/Default
    - React-cxxreact (= 0.67.5)
    - React-jsi (= 0.67.5)
    - React-jsiexecutor (= 0.67.5)
    - React-perflogger (= 0.67.5)
    - Yoga
  - React-Core/RCTImageHeaders (0.67.5):
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-Core/Default
    - React-cxxreact (= 0.67.5)
    - React-jsi (= 0.67.5)
    - React-jsiexecutor (= 0.67.5)
    - React-perflogger (= 0.67.5)
    - Yoga
  - React-Core/RCTLinkingHeaders (0.67.5):
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-Core/Default
    - React-cxxreact (= 0.67.5)
    - React-jsi (= 0.67.5)
    - React-jsiexecutor (= 0.67.5)
    - React-perflogger (= 0.67.5)
    - Yoga
  - React-Core/RCTNetworkHeaders (0.67.5):
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-Core/Default
    - React-cxxreact (= 0.67.5)
    - React-jsi (= 0.67.5)
    - React-jsiexecutor (= 0.67.5)
    - React-perflogger (= 0.67.5)
    - Yoga
  - React-Core/RCTSettingsHeaders (0.67.5):
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-Core/Default
    - React-cxxreact (= 0.67.5)
    - React-jsi (= 0.67.5)
    - React-jsiexecutor (= 0.67.5)
    - React-perflogger (= 0.67.5)
    - Yoga
  - React-Core/RCTTextHeaders (0.67.5):
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-Core/Default
    - React-cxxreact (= 0.67.5)
    - React-jsi (= 0.67.5)
    - React-jsiexecutor (= 0.67.5)
    - React-perflogger (= 0.67.5)
    - Yoga
  - React-Core/RCTVibrationHeaders (0.67.5):
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-Core/Default
    - React-cxxreact (= 0.67.5)
    - React-jsi (= 0.67.5)
    - React-jsiexecutor (= 0.67.5)
    - React-perflogger (= 0.67.5)
    - Yoga
  - React-Core/RCTWebSocket (0.67.5):
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-Core/Default (= 0.67.5)
    - React-cxxreact (= 0.67.5)
    - React-jsi (= 0.67.5)
    - React-jsiexecutor (= 0.67.5)
    - React-perflogger (= 0.67.5)
    - Yoga
  - React-CoreModules (0.67.5):
    - FBReactNativeSpec (= 0.67.5)
    - RCT-Folly (= 2021.06.28.00-v2)
    - RCTTypeSafety (= 0.67.5)
    - React-Core/CoreModulesHeaders (= 0.67.5)
    - React-jsi (= 0.67.5)
    - React-RCTImage (= 0.67.5)
    - ReactCommon/turbomodule/core (= 0.67.5)
  - React-cxxreact (0.67.5):
    - boost (= 1.76.0)
    - DoubleConversion
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-callinvoker (= 0.67.5)
    - React-jsi (= 0.67.5)
    - React-jsinspector (= 0.67.5)
    - React-logger (= 0.67.5)
    - React-perflogger (= 0.67.5)
    - React-runtimeexecutor (= 0.67.5)
  - React-hermes (0.67.5):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.06.28.00-v2)
    - RCT-Folly/Futures (= 2021.06.28.00-v2)
    - React-cxxreact (= 0.67.5)
    - React-jsi (= 0.67.5)
    - React-jsiexecutor (= 0.67.5)
    - React-jsinspector (= 0.67.5)
    - React-perflogger (= 0.67.5)
  - React-jsi (0.67.5):
    - boost (= 1.76.0)
    - DoubleConversion
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-jsi/Default (= 0.67.5)
  - React-jsi/Default (0.67.5):
    - boost (= 1.76.0)
    - DoubleConversion
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
  - React-jsiexecutor (0.67.5):
    - DoubleConversion
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-cxxreact (= 0.67.5)
    - React-jsi (= 0.67.5)
    - React-perflogger (= 0.67.5)
  - React-jsinspector (0.67.5)
  - React-logger (0.67.5):
    - glog
  - react-native-animateable-text (0.9.1):
    - React-Core
  - react-native-blur (4.2.0):
    - React-Core
  - react-native-blurhash (1.1.11):
    - React-Core
  - react-native-branch (5.6.2):
    - Branch (= 1.43.2)
    - React-Core
  - react-native-camera (3.44.3):
    - React-Core
    - react-native-camera/RCT (= 3.44.3)
    - react-native-camera/RN (= 3.44.3)
  - react-native-camera/RCT (3.44.3):
    - React-Core
  - react-native-camera/RN (3.44.3):
    - React-Core
  - react-native-config (1.4.5):
    - react-native-config/App (= 1.4.5)
  - react-native-config/App (1.4.5):
    - React-Core
  - react-native-context-menu-view (1.5.2):
    - React
  - react-native-flipper (0.131.1):
    - React-Core
  - react-native-get-random-values (1.8.0):
    - React-Core
  - react-native-in-app-review (4.3.3):
    - React-Core
  - react-native-mmkv (2.4.1):
    - MMKV (>= 1.2.13)
    - React-Core
  - react-native-netinfo (9.4.1):
    - React-Core
  - react-native-performance (2.1.0):
    - React-Core
  - react-native-quick-base64 (1.0.0):
    - React-Core
  - react-native-randombytes (3.6.1):
    - React-Core
  - react-native-safe-area-context (4.1.2):
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React
    - ReactCommon/turbomodule/core
  - react-native-sensitive-info (6.0.0-alpha.9):
    - React-Core
  - react-native-skia (0.1.130):
    - React
    - React-callinvoker
    - React-Core
    - react-native-skia/Api (= 0.1.130)
    - react-native-skia/Jsi (= 0.1.130)
    - react-native-skia/RNSkia (= 0.1.130)
    - react-native-skia/SkiaHeaders (= 0.1.130)
    - react-native-skia/Utils (= 0.1.130)
  - react-native-skia/Api (0.1.130):
    - React
    - React-callinvoker
    - React-Core
  - react-native-skia/Jsi (0.1.130):
    - React
    - React-callinvoker
    - React-Core
  - react-native-skia/RNSkia (0.1.130):
    - React
    - React-callinvoker
    - React-Core
  - react-native-skia/SkiaHeaders (0.1.130):
    - React
    - React-callinvoker
    - React-Core
  - react-native-skia/Utils (0.1.130):
    - React
    - React-callinvoker
    - React-Core
  - react-native-slider (4.2.0):
    - React-Core
  - react-native-sodium-jsi (1.0.0):
    - React-Core
  - react-native-tcp-socket (5.6.2):
    - CocoaAsyncSocket
    - React-Core
  - react-native-themis (0.14.7):
    - React-Core
  - react-native-video (5.2.1):
    - React-Core
    - react-native-video/Video (= 5.2.1)
  - react-native-video-cache (2.7.2):
    - KTVHTTPCache (~> 2.0.0)
    - React
  - react-native-video/Video (5.2.1):
    - React-Core
  - react-native-webview (11.21.1):
    - React-Core
  - React-perflogger (0.67.5)
  - React-RCTActionSheet (0.67.5):
    - React-Core/RCTActionSheetHeaders (= 0.67.5)
  - React-RCTAnimation (0.67.5):
    - FBReactNativeSpec (= 0.67.5)
    - RCT-Folly (= 2021.06.28.00-v2)
    - RCTTypeSafety (= 0.67.5)
    - React-Core/RCTAnimationHeaders (= 0.67.5)
    - React-jsi (= 0.67.5)
    - ReactCommon/turbomodule/core (= 0.67.5)
  - React-RCTBlob (0.67.5):
    - FBReactNativeSpec (= 0.67.5)
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-Core/RCTBlobHeaders (= 0.67.5)
    - React-Core/RCTWebSocket (= 0.67.5)
    - React-jsi (= 0.67.5)
    - React-RCTNetwork (= 0.67.5)
    - ReactCommon/turbomodule/core (= 0.67.5)
  - React-RCTImage (0.67.5):
    - FBReactNativeSpec (= 0.67.5)
    - RCT-Folly (= 2021.06.28.00-v2)
    - RCTTypeSafety (= 0.67.5)
    - React-Core/RCTImageHeaders (= 0.67.5)
    - React-jsi (= 0.67.5)
    - React-RCTNetwork (= 0.67.5)
    - ReactCommon/turbomodule/core (= 0.67.5)
  - React-RCTLinking (0.67.5):
    - FBReactNativeSpec (= 0.67.5)
    - React-Core/RCTLinkingHeaders (= 0.67.5)
    - React-jsi (= 0.67.5)
    - ReactCommon/turbomodule/core (= 0.67.5)
  - React-RCTNetwork (0.67.5):
    - FBReactNativeSpec (= 0.67.5)
    - RCT-Folly (= 2021.06.28.00-v2)
    - RCTTypeSafety (= 0.67.5)
    - React-Core/RCTNetworkHeaders (= 0.67.5)
    - React-jsi (= 0.67.5)
    - ReactCommon/turbomodule/core (= 0.67.5)
  - React-RCTSettings (0.67.5):
    - FBReactNativeSpec (= 0.67.5)
    - RCT-Folly (= 2021.06.28.00-v2)
    - RCTTypeSafety (= 0.67.5)
    - React-Core/RCTSettingsHeaders (= 0.67.5)
    - React-jsi (= 0.67.5)
    - ReactCommon/turbomodule/core (= 0.67.5)
  - React-RCTText (0.67.5):
    - React-Core/RCTTextHeaders (= 0.67.5)
  - React-RCTVibration (0.67.5):
    - FBReactNativeSpec (= 0.67.5)
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-Core/RCTVibrationHeaders (= 0.67.5)
    - React-jsi (= 0.67.5)
    - ReactCommon/turbomodule/core (= 0.67.5)
  - React-runtimeexecutor (0.67.5):
    - React-jsi (= 0.67.5)
  - ReactCommon/turbomodule/core (0.67.5):
    - DoubleConversion
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-callinvoker (= 0.67.5)
    - React-Core (= 0.67.5)
    - React-cxxreact (= 0.67.5)
    - React-jsi (= 0.67.5)
    - React-logger (= 0.67.5)
    - React-perflogger (= 0.67.5)
  - RNCAsyncStorage (1.17.11):
    - React-Core
  - RNCCheckbox (0.5.9):
    - React-Core
  - RNCClipboard (1.5.1):
    - React-Core
  - RNCMaskedView (0.2.6):
    - React-Core
  - RNCPicker (1.15.0):
    - React-Core
  - RNCPushNotificationIOS (1.10.1):
    - React-Core
  - RNDateTimePicker (5.1.0):
    - React-Core
  - RNDeviceInfo (8.4.8):
    - React-Core
  - RNFastImage (8.6.3):
    - React-Core
    - SDWebImage (~> 5.11.1)
    - SDWebImageWebPCoder (~> 0.8.4)
  - RNFlashList (1.4.1):
    - React-Core
  - RNFS (2.18.0):
    - React
  - RNGestureHandler (2.4.2):
    - React-Core
  - RNInAppBrowser (3.7.0):
    - React-Core
  - RNPermissions (3.2.0):
    - React-Core
  - RNReactNativeHapticFeedback (1.13.1):
    - React-Core
  - RNReanimated (2.14.1):
    - DoubleConversion
    - FBLazyVector
    - FBReactNativeSpec
    - glog
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-callinvoker
    - React-Core
    - React-Core/DevSupport
    - React-Core/RCTWebSocket
    - React-CoreModules
    - React-cxxreact
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-RCTActionSheet
    - React-RCTAnimation
    - React-RCTBlob
    - React-RCTImage
    - React-RCTLinking
    - React-RCTNetwork
    - React-RCTSettings
    - React-RCTText
    - ReactCommon/turbomodule/core
    - Yoga
  - RNScreens (3.10.1):
    - React-Core
    - React-RCTImage
  - RNSentry (5.32.0):
    - React-Core
    - Sentry/HybridSDK (= 8.36.0)
  - RNSha256 (1.4.10):
    - React-Core
  - RNSVG (12.5.1):
    - React-Core
  - RNVectorIcons (8.1.0):
    - React-Core
  - SDWebImage (5.11.1):
    - SDWebImage/Core (= 5.11.1)
  - SDWebImage/Core (5.11.1)
  - SDWebImageWebPCoder (0.8.5):
    - libwebp (~> 1.0)
    - SDWebImage/Core (~> 5.10)
  - Sentry/HybridSDK (8.36.0)
  - SocketRocket (0.6.1)
  - SSZipArchive (2.2.3)
  - TrustKit (3.0.4)
  - UMTaskManagerInterface (7.1.1):
    - ExpoModulesCore
  - Yoga (1.14.0)
  - YogaKit (1.18.1):
    - Yoga (~> 1.14)

DEPENDENCIES:
  - bcrypt-react-native (from `../../node_modules/bcrypt-react-native`)
  - boost (from `../../node_modules/react-native/third-party-podspecs/boost.podspec`)
  - CodePush (from `../../node_modules/react-native-code-push`)
  - DoubleConversion (from `../../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec`)
  - EXApplication (from `../../node_modules/expo-application/ios`)
  - EXConstants (from `../../node_modules/expo-constants/ios`)
  - EXFileSystem (from `../../node_modules/expo-file-system/ios`)
  - EXFont (from `../../node_modules/expo-font/ios`)
  - EXKeepAwake (from `../../node_modules/expo-keep-awake/ios`)
  - EXLocalAuthentication (from `../../node_modules/expo-local-authentication/ios`)
  - EXNotifications (from `../../node_modules/expo-notifications/ios`)
  - Expo (from `../../node_modules/expo/ios`)
  - ExpoLinearGradient (from `../../node_modules/expo-linear-gradient/ios`)
  - ExpoModulesCore (from `../../node_modules/expo-modules-core/ios`)
  - EXRandom (from `../../node_modules/expo-random/ios`)
  - EXScreenCapture (from `../../node_modules/expo-screen-capture/ios`)
  - EXSecureStore (from `../../node_modules/expo-secure-store/ios`)
  - EXSplashScreen (from `../../node_modules/expo-splash-screen/ios`)
  - FBLazyVector (from `../../node_modules/react-native/Libraries/FBLazyVector`)
  - FBReactNativeSpec (from `../../node_modules/react-native/React/FBReactNativeSpec`)
  - Flipper (= 0.182.0)
  - Flipper-Boost-iOSX (= ********.11)
  - Flipper-DoubleConversion (= 3.1.7)
  - Flipper-Fmt (= 7.1.7)
  - Flipper-Folly (= 2.6.7)
  - Flipper-Glog (= 0.3.6)
  - Flipper-PeerTalk (= 0.0.4)
  - Flipper-RSocket (= 1.4.3)
  - FlipperKit (= 0.182.0)
  - FlipperKit/Core (= 0.182.0)
  - FlipperKit/CppBridge (= 0.182.0)
  - FlipperKit/FBCxxFollyDynamicConvert (= 0.182.0)
  - FlipperKit/FBDefines (= 0.182.0)
  - FlipperKit/FKPortForwarding (= 0.182.0)
  - FlipperKit/FlipperKitHighlightOverlay (= 0.182.0)
  - FlipperKit/FlipperKitLayoutPlugin (= 0.182.0)
  - FlipperKit/FlipperKitLayoutTextSearchable (= 0.182.0)
  - FlipperKit/FlipperKitNetworkPlugin (= 0.182.0)
  - FlipperKit/FlipperKitReactPlugin (= 0.182.0)
  - FlipperKit/FlipperKitUserDefaultsPlugin (= 0.182.0)
  - FlipperKit/SKIOSNetworkPlugin (= 0.182.0)
  - glog (from `../../node_modules/react-native/third-party-podspecs/glog.podspec`)
  - hermes-engine (~> 0.9.0)
  - jail-monkey (from `../../node_modules/jail-monkey`)
  - libevent (~> 2.1.12)
  - lottie-ios (from `../../node_modules/lottie-ios`)
  - lottie-react-native (from `../../node_modules/lottie-react-native`)
  - MixpanelReactNative (from `../../node_modules/mixpanel-react-native`)
  - OpenSSL-Universal (= 1.1.180)
  - Permission-Camera (from `../../node_modules/react-native-permissions/ios/Camera`)
  - Permission-Notifications (from `../../node_modules/react-native-permissions/ios/Notifications`)
  - Ramp (from `https://github.com/RampNetwork/ramp-sdk-ios`, tag `4.0.1`)
  - "ramp-network-react-native-sdk (from `../../node_modules/@ramp-network/react-native-sdk`)"
  - RCT-Folly (from `../../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec`)
  - RCTRequired (from `../../node_modules/react-native/Libraries/RCTRequired`)
  - RCTTypeSafety (from `../../node_modules/react-native/Libraries/TypeSafety`)
  - React (from `../../node_modules/react-native/`)
  - React-callinvoker (from `../../node_modules/react-native/ReactCommon/callinvoker`)
  - React-Core (from `../../node_modules/react-native/`)
  - React-Core/DevSupport (from `../../node_modules/react-native/`)
  - React-Core/RCTWebSocket (from `../../node_modules/react-native/`)
  - React-CoreModules (from `../../node_modules/react-native/React/CoreModules`)
  - React-cxxreact (from `../../node_modules/react-native/ReactCommon/cxxreact`)
  - React-hermes (from `../../node_modules/react-native/ReactCommon/hermes`)
  - React-jsi (from `../../node_modules/react-native/ReactCommon/jsi`)
  - React-jsiexecutor (from `../../node_modules/react-native/ReactCommon/jsiexecutor`)
  - React-jsinspector (from `../../node_modules/react-native/ReactCommon/jsinspector`)
  - React-logger (from `../../node_modules/react-native/ReactCommon/logger`)
  - react-native-animateable-text (from `../../node_modules/react-native-animateable-text`)
  - "react-native-blur (from `../../node_modules/@react-native-community/blur`)"
  - react-native-blurhash (from `../../node_modules/react-native-blurhash`)
  - react-native-branch (from `../../node_modules/react-native-branch`)
  - react-native-camera (from `../../node_modules/react-native-camera`)
  - react-native-config (from `../../node_modules/react-native-config`)
  - react-native-context-menu-view (from `../../node_modules/react-native-context-menu-view`)
  - react-native-flipper (from `../../node_modules/react-native-flipper`)
  - react-native-get-random-values (from `../../node_modules/react-native-get-random-values`)
  - react-native-in-app-review (from `../../node_modules/react-native-in-app-review`)
  - react-native-mmkv (from `../../node_modules/react-native-mmkv`)
  - "react-native-netinfo (from `../../node_modules/@react-native-community/netinfo`)"
  - react-native-performance (from `../../node_modules/react-native-performance/ios`)
  - react-native-quick-base64 (from `../../node_modules/react-native-quick-base64`)
  - react-native-randombytes (from `../../node_modules/react-native-randombytes`)
  - react-native-safe-area-context (from `../../node_modules/react-native-safe-area-context`)
  - react-native-sensitive-info (from `../../node_modules/react-native-sensitive-info`)
  - "react-native-skia (from `../../node_modules/@shopify/react-native-skia`)"
  - "react-native-slider (from `../../node_modules/@react-native-community/slider`)"
  - react-native-sodium-jsi (from `../../node_modules/react-native-sodium-jsi`)
  - react-native-tcp-socket (from `../../node_modules/react-native-tcp-socket`)
  - react-native-themis (from `../../node_modules/react-native-themis`)
  - react-native-video (from `../../node_modules/react-native-video`)
  - react-native-video-cache (from `../../node_modules/react-native-video-cache`)
  - react-native-webview (from `../../node_modules/react-native-webview`)
  - React-perflogger (from `../../node_modules/react-native/ReactCommon/reactperflogger`)
  - React-RCTActionSheet (from `../../node_modules/react-native/Libraries/ActionSheetIOS`)
  - React-RCTAnimation (from `../../node_modules/react-native/Libraries/NativeAnimation`)
  - React-RCTBlob (from `../../node_modules/react-native/Libraries/Blob`)
  - React-RCTImage (from `../../node_modules/react-native/Libraries/Image`)
  - React-RCTLinking (from `../../node_modules/react-native/Libraries/LinkingIOS`)
  - React-RCTNetwork (from `../../node_modules/react-native/Libraries/Network`)
  - React-RCTSettings (from `../../node_modules/react-native/Libraries/Settings`)
  - React-RCTText (from `../../node_modules/react-native/Libraries/Text`)
  - React-RCTVibration (from `../../node_modules/react-native/Libraries/Vibration`)
  - React-runtimeexecutor (from `../../node_modules/react-native/ReactCommon/runtimeexecutor`)
  - ReactCommon/turbomodule/core (from `../../node_modules/react-native/ReactCommon`)
  - "RNCAsyncStorage (from `../../node_modules/@react-native-async-storage/async-storage`)"
  - "RNCCheckbox (from `../../node_modules/@react-native-community/checkbox`)"
  - "RNCClipboard (from `../../node_modules/@react-native-community/clipboard`)"
  - "RNCMaskedView (from `../../node_modules/@react-native-masked-view/masked-view`)"
  - "RNCPicker (from `../../node_modules/@react-native-picker/picker`)"
  - "RNCPushNotificationIOS (from `../../node_modules/@react-native-community/push-notification-ios`)"
  - "RNDateTimePicker (from `../../node_modules/@react-native-community/datetimepicker`)"
  - RNDeviceInfo (from `../../node_modules/react-native-device-info`)
  - RNFastImage (from `../../node_modules/react-native-fast-image`)
  - "RNFlashList (from `../../node_modules/@shopify/flash-list`)"
  - RNFS (from `../../node_modules/react-native-fs`)
  - RNGestureHandler (from `../../node_modules/react-native-gesture-handler`)
  - RNInAppBrowser (from `../../node_modules/react-native-inappbrowser-reborn`)
  - RNPermissions (from `../../node_modules/react-native-permissions`)
  - RNReactNativeHapticFeedback (from `../../node_modules/react-native-haptic-feedback`)
  - RNReanimated (from `../../node_modules/react-native-reanimated`)
  - RNScreens (from `../../node_modules/react-native-screens`)
  - "RNSentry (from `../../node_modules/@sentry/react-native`)"
  - RNSha256 (from `../../node_modules/react-native-sha256`)
  - RNSVG (from `../../node_modules/react-native-svg`)
  - RNVectorIcons (from `../../node_modules/react-native-vector-icons`)
  - TrustKit
  - UMTaskManagerInterface (from `../../node_modules/unimodules-task-manager-interface/ios`)
  - Yoga (from `../../node_modules/react-native/ReactCommon/yoga`)

SPEC REPOS:
  https://github.com/CocoaPods/Specs.git:
    - Base64
    - BCrypt
    - Branch
    - CocoaAsyncSocket
    - Flipper
    - Flipper-Boost-iOSX
    - Flipper-DoubleConversion
    - Flipper-Fmt
    - Flipper-Folly
    - Flipper-Glog
    - Flipper-PeerTalk
    - Flipper-RSocket
    - FlipperKit
    - fmt
    - hermes-engine
    - JWT
    - KTVCocoaHTTPServer
    - KTVHTTPCache
    - libevent
    - libwebp
    - Mixpanel-swift
    - MMKV
    - MMKVCore
    - OpenSSL-Universal
    - SDWebImage
    - SDWebImageWebPCoder
    - Sentry
    - SocketRocket
    - SSZipArchive
    - TrustKit
    - YogaKit

EXTERNAL SOURCES:
  bcrypt-react-native:
    :path: "../../node_modules/bcrypt-react-native"
  boost:
    :podspec: "../../node_modules/react-native/third-party-podspecs/boost.podspec"
  CodePush:
    :path: "../../node_modules/react-native-code-push"
  DoubleConversion:
    :podspec: "../../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec"
  EXApplication:
    :path: "../../node_modules/expo-application/ios"
  EXConstants:
    :path: "../../node_modules/expo-constants/ios"
  EXFileSystem:
    :path: "../../node_modules/expo-file-system/ios"
  EXFont:
    :path: "../../node_modules/expo-font/ios"
  EXKeepAwake:
    :path: "../../node_modules/expo-keep-awake/ios"
  EXLocalAuthentication:
    :path: "../../node_modules/expo-local-authentication/ios"
  EXNotifications:
    :path: "../../node_modules/expo-notifications/ios"
  Expo:
    :path: "../../node_modules/expo/ios"
  ExpoLinearGradient:
    :path: "../../node_modules/expo-linear-gradient/ios"
  ExpoModulesCore:
    :path: "../../node_modules/expo-modules-core/ios"
  EXRandom:
    :path: "../../node_modules/expo-random/ios"
  EXScreenCapture:
    :path: "../../node_modules/expo-screen-capture/ios"
  EXSecureStore:
    :path: "../../node_modules/expo-secure-store/ios"
  EXSplashScreen:
    :path: "../../node_modules/expo-splash-screen/ios"
  FBLazyVector:
    :path: "../../node_modules/react-native/Libraries/FBLazyVector"
  FBReactNativeSpec:
    :path: "../../node_modules/react-native/React/FBReactNativeSpec"
  glog:
    :podspec: "../../node_modules/react-native/third-party-podspecs/glog.podspec"
  jail-monkey:
    :path: "../../node_modules/jail-monkey"
  lottie-ios:
    :path: "../../node_modules/lottie-ios"
  lottie-react-native:
    :path: "../../node_modules/lottie-react-native"
  MixpanelReactNative:
    :path: "../../node_modules/mixpanel-react-native"
  Permission-Camera:
    :path: "../../node_modules/react-native-permissions/ios/Camera"
  Permission-Notifications:
    :path: "../../node_modules/react-native-permissions/ios/Notifications"
  Ramp:
    :git: https://github.com/RampNetwork/ramp-sdk-ios
    :tag: 4.0.1
  ramp-network-react-native-sdk:
    :path: "../../node_modules/@ramp-network/react-native-sdk"
  RCT-Folly:
    :podspec: "../../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec"
  RCTRequired:
    :path: "../../node_modules/react-native/Libraries/RCTRequired"
  RCTTypeSafety:
    :path: "../../node_modules/react-native/Libraries/TypeSafety"
  React:
    :path: "../../node_modules/react-native/"
  React-callinvoker:
    :path: "../../node_modules/react-native/ReactCommon/callinvoker"
  React-Core:
    :path: "../../node_modules/react-native/"
  React-CoreModules:
    :path: "../../node_modules/react-native/React/CoreModules"
  React-cxxreact:
    :path: "../../node_modules/react-native/ReactCommon/cxxreact"
  React-hermes:
    :path: "../../node_modules/react-native/ReactCommon/hermes"
  React-jsi:
    :path: "../../node_modules/react-native/ReactCommon/jsi"
  React-jsiexecutor:
    :path: "../../node_modules/react-native/ReactCommon/jsiexecutor"
  React-jsinspector:
    :path: "../../node_modules/react-native/ReactCommon/jsinspector"
  React-logger:
    :path: "../../node_modules/react-native/ReactCommon/logger"
  react-native-animateable-text:
    :path: "../../node_modules/react-native-animateable-text"
  react-native-blur:
    :path: "../../node_modules/@react-native-community/blur"
  react-native-blurhash:
    :path: "../../node_modules/react-native-blurhash"
  react-native-branch:
    :path: "../../node_modules/react-native-branch"
  react-native-camera:
    :path: "../../node_modules/react-native-camera"
  react-native-config:
    :path: "../../node_modules/react-native-config"
  react-native-context-menu-view:
    :path: "../../node_modules/react-native-context-menu-view"
  react-native-flipper:
    :path: "../../node_modules/react-native-flipper"
  react-native-get-random-values:
    :path: "../../node_modules/react-native-get-random-values"
  react-native-in-app-review:
    :path: "../../node_modules/react-native-in-app-review"
  react-native-mmkv:
    :path: "../../node_modules/react-native-mmkv"
  react-native-netinfo:
    :path: "../../node_modules/@react-native-community/netinfo"
  react-native-performance:
    :path: "../../node_modules/react-native-performance/ios"
  react-native-quick-base64:
    :path: "../../node_modules/react-native-quick-base64"
  react-native-randombytes:
    :path: "../../node_modules/react-native-randombytes"
  react-native-safe-area-context:
    :path: "../../node_modules/react-native-safe-area-context"
  react-native-sensitive-info:
    :path: "../../node_modules/react-native-sensitive-info"
  react-native-skia:
    :path: "../../node_modules/@shopify/react-native-skia"
  react-native-slider:
    :path: "../../node_modules/@react-native-community/slider"
  react-native-sodium-jsi:
    :path: "../../node_modules/react-native-sodium-jsi"
  react-native-tcp-socket:
    :path: "../../node_modules/react-native-tcp-socket"
  react-native-themis:
    :path: "../../node_modules/react-native-themis"
  react-native-video:
    :path: "../../node_modules/react-native-video"
  react-native-video-cache:
    :path: "../../node_modules/react-native-video-cache"
  react-native-webview:
    :path: "../../node_modules/react-native-webview"
  React-perflogger:
    :path: "../../node_modules/react-native/ReactCommon/reactperflogger"
  React-RCTActionSheet:
    :path: "../../node_modules/react-native/Libraries/ActionSheetIOS"
  React-RCTAnimation:
    :path: "../../node_modules/react-native/Libraries/NativeAnimation"
  React-RCTBlob:
    :path: "../../node_modules/react-native/Libraries/Blob"
  React-RCTImage:
    :path: "../../node_modules/react-native/Libraries/Image"
  React-RCTLinking:
    :path: "../../node_modules/react-native/Libraries/LinkingIOS"
  React-RCTNetwork:
    :path: "../../node_modules/react-native/Libraries/Network"
  React-RCTSettings:
    :path: "../../node_modules/react-native/Libraries/Settings"
  React-RCTText:
    :path: "../../node_modules/react-native/Libraries/Text"
  React-RCTVibration:
    :path: "../../node_modules/react-native/Libraries/Vibration"
  React-runtimeexecutor:
    :path: "../../node_modules/react-native/ReactCommon/runtimeexecutor"
  ReactCommon:
    :path: "../../node_modules/react-native/ReactCommon"
  RNCAsyncStorage:
    :path: "../../node_modules/@react-native-async-storage/async-storage"
  RNCCheckbox:
    :path: "../../node_modules/@react-native-community/checkbox"
  RNCClipboard:
    :path: "../../node_modules/@react-native-community/clipboard"
  RNCMaskedView:
    :path: "../../node_modules/@react-native-masked-view/masked-view"
  RNCPicker:
    :path: "../../node_modules/@react-native-picker/picker"
  RNCPushNotificationIOS:
    :path: "../../node_modules/@react-native-community/push-notification-ios"
  RNDateTimePicker:
    :path: "../../node_modules/@react-native-community/datetimepicker"
  RNDeviceInfo:
    :path: "../../node_modules/react-native-device-info"
  RNFastImage:
    :path: "../../node_modules/react-native-fast-image"
  RNFlashList:
    :path: "../../node_modules/@shopify/flash-list"
  RNFS:
    :path: "../../node_modules/react-native-fs"
  RNGestureHandler:
    :path: "../../node_modules/react-native-gesture-handler"
  RNInAppBrowser:
    :path: "../../node_modules/react-native-inappbrowser-reborn"
  RNPermissions:
    :path: "../../node_modules/react-native-permissions"
  RNReactNativeHapticFeedback:
    :path: "../../node_modules/react-native-haptic-feedback"
  RNReanimated:
    :path: "../../node_modules/react-native-reanimated"
  RNScreens:
    :path: "../../node_modules/react-native-screens"
  RNSentry:
    :path: "../../node_modules/@sentry/react-native"
  RNSha256:
    :path: "../../node_modules/react-native-sha256"
  RNSVG:
    :path: "../../node_modules/react-native-svg"
  RNVectorIcons:
    :path: "../../node_modules/react-native-vector-icons"
  UMTaskManagerInterface:
    :path: "../../node_modules/unimodules-task-manager-interface/ios"
  Yoga:
    :path: "../../node_modules/react-native/ReactCommon/yoga"

CHECKOUT OPTIONS:
  Ramp:
    :git: https://github.com/RampNetwork/ramp-sdk-ios
    :tag: 4.0.1

SPEC CHECKSUMS:
  Base64: cecfb41a004124895a7bcee567a89bae5a89d49b
  BCrypt: 712b656110e5020d319c547e4d8f3053ded82b2a
  bcrypt-react-native: 399775585257ae6c8717370a1119c7da0113e3bf
  boost: 1a9a5419b960ff07b22de8efdefa0ba3a61f2631
  Branch: 4ac024cb3c29b0ef628048694db3c4cfa679beb0
  CocoaAsyncSocket: 065fd1e645c7abab64f7a6a2007a48038fdc6a99
  CodePush: b51b7ac64c07d4eacfc8cc5750a1dd28adbf2528
  DoubleConversion: 831926d9b8bf8166fd87886c4abab286c2422662
  EXApplication: 54fe5bd6268d697771645e8f1aef8b806a65247a
  EXConstants: 7c44785d41d8e959d527d23d29444277a4d1ee73
  EXFileSystem: 7bcd3c1428698150d5c8ca140c8183f2ee204048
  EXFont: 06df627203afcb8a3b3152ec06eb2f11f46f0cff
  EXKeepAwake: bf48d7f740a5cd2befed6cf9a49911d385c6c47d
  EXLocalAuthentication: 3c5f368ee954b79c3778158eb8000cbce4e2f8a2
  EXNotifications: a7d582fa800d77f4a75bd22d52e84e2fbcee26df
  Expo: 534e51e607aba8229293297da5585f4b26f50fa1
  ExpoLinearGradient: fd591c223c81f6779036ed4cef4deb20ecb87c50
  ExpoModulesCore: 32c0ccb47f477d330ee93db72505380adf0de09a
  EXRandom: 1d00fd04d7d874c0c9d1ff87cee59ffe0b638a3d
  EXScreenCapture: 9bfdadf17f0b1dd082edb704764f1724176b2f27
  EXSecureStore: b80c74c5ee29d0160c2aace3fd9a24a5edc20015
  EXSplashScreen: 799bece80089219b2c989c1082d70f3b00995cda
  FBLazyVector: d2db9d00883282819d03bbd401b2ad4360d47580
  FBReactNativeSpec: 94da4d84ba3b1acf459103320882daa481a2b62d
  Flipper: 6edb735e6c3e332975d1b17956bcc584eccf5818
  Flipper-Boost-iOSX: fd1e2b8cbef7e662a122412d7ac5f5bea715403c
  Flipper-DoubleConversion: 57ffbe81ef95306cc9e69c4aa3aeeeeb58a6a28c
  Flipper-Fmt: 60cbdd92fc254826e61d669a5d87ef7015396a9b
  Flipper-Folly: 83af37379faa69497529e414bd43fbfc7cae259a
  Flipper-Glog: 1dfd6abf1e922806c52ceb8701a3599a79a200a6
  Flipper-PeerTalk: 116d8f857dc6ef55c7a5a75ea3ceaafe878aadc9
  Flipper-RSocket: d9d9ade67cbecf6ac10730304bf5607266dd2541
  FlipperKit: 2efad7007d6745a3f95e4034d547be637f89d3f6
  fmt: ff9d55029c625d3757ed641535fd4a75fedc7ce9
  glog: 85ecdd10ee8d8ec362ef519a6a45ff9aa27b2e85
  hermes-engine: bf7577d12ac6ccf53ab8b5af3c6ccf0dd8458c5c
  jail-monkey: de7eab0c9cbe4e7cdd2f80d04f812337ae94b2e0
  JWT: ef71dfb03e1f842081e64dc42eef0e164f35d251
  KTVCocoaHTTPServer: df8d7b861e603ff8037e9b2138aca2563a6b768d
  KTVHTTPCache: 588c3eb16f6bd1e6fde1e230dabfb7bd4e490a4d
  libevent: 4049cae6c81cdb3654a443be001fb9bdceff7913
  libwebp: 1786c9f4ff8a279e4dac1e8f385004d5fc253009
  lottie-ios: c058aeafa76daa4cf64d773554bccc8385d0150e
  lottie-react-native: a029a86e1689c86a07169c520ae770e84348cd20
  Mixpanel-swift: fda407112b9e908f20550ab6ceaa4d05b09141d7
  MixpanelReactNative: 8b805c04ec10bdc26eb072e95f8d01a6b6132868
  MMKV: 817ba1eea17421547e01e087285606eb270a8dcb
  MMKVCore: af055b00e27d88cd92fad301c5fecd1ff9b26dd9
  OpenSSL-Universal: 1aa4f6a6ee7256b83db99ec1ccdaa80d10f9af9b
  Permission-Camera: 53efcbb755b0e8bdf253dbb27cc7559ccfce8480
  Permission-Notifications: bb420c3d28328df24de1b476b41ed8249ccf2537
  Ramp: 3f843fb75cac12ad40842afa3226bae36dc93521
  ramp-network-react-native-sdk: 31e897c8773097a8d34253a4fc09164f92665500
  RCT-Folly: 803a9cfd78114b2ec0f140cfa6fa2a6bafb2d685
  RCTRequired: 412e994c1e570cf35378a32c18fd46e50634938b
  RCTTypeSafety: ef27340c728e6d673af345ed69e479a010c8a2d8
  React: 36b9f5116572e5b80f01e562bb1f1451e8848e47
  React-callinvoker: 91e62870884d3db3a0db33bbb1ba4e53fa5210ca
  React-Core: 765ccc3861be1b93c7d5ca37f6b06e2efd6e7999
  React-CoreModules: da2ddff50a92576b6d58fbfc80a62ba3f81d8a4e
  React-cxxreact: b54cffd4feb550c3213cd38db4a2a4bdd896f715
  React-hermes: f12c1cf82cd839e98e59c84018b15d9ba0d4300a
  React-jsi: 103674913e4159a07df20ef214c6b563e90e7b2e
  React-jsiexecutor: e9895ccae253323ca70f693945fecbba091f0abd
  React-jsinspector: ec4fe4f65ccf9d67c8429dda955d3412db8a25ef
  React-logger: 85f4ef06b9723714b2dfa5b0e5502b673b271b58
  react-native-animateable-text: 6da2987d323c39d0a0d5bfb3a3f805c69628904a
  react-native-blur: 3e9c8e8e9f7d17fa1b94e1a0ae9fd816675f5382
  react-native-blurhash: a59e6bf8117a0304488ed576abd440f4b0777a8c
  react-native-branch: 4e42fda662d96893afbbd02839806931398e3d2e
  react-native-camera: b8cc03e2feec0c04403d0998e37cf519d8fd4c6f
  react-native-config: 6502b1879f97ed5ac570a029961fc35ea606cd14
  react-native-context-menu-view: c72d4120597f5cf3917dcb348cf819f215ed9207
  react-native-flipper: 8d0ada062fbc2c7460cc0d343aded6fc3f9c90dc
  react-native-get-random-values: a6ea6a8a65dc93e96e24a11105b1a9c8cfe1d72a
  react-native-in-app-review: db8bb167a5f238e7ceca5c242d6b36ce8c4404a4
  react-native-mmkv: b5c7f9bc369eef2b8a2aa36e8a15949989fa823f
  react-native-netinfo: fefd4e98d75cbdd6e85fc530f7111a8afdf2b0c5
  react-native-performance: f4b6604a9d5a8a7407e34a82fab6c641d9a3ec12
  react-native-quick-base64: da3c9a708403c79450acbc15008b3f64f275d900
  react-native-randombytes: 421f1c7d48c0af8dbcd471b0324393ebf8fe7846
  react-native-safe-area-context: 114e9a74df2643012b9d6cc1460b94f3b6a90780
  react-native-sensitive-info: d44e909d065f9c0e15734245e5dd6a24b82e3dcd
  react-native-skia: ded7ec890d19a75f17b3cbd5ebf0d6459701daa9
  react-native-slider: 37e1cf275dab20d3c015e40969aa9f5c309645a8
  react-native-sodium-jsi: 546d6caf98889940311af86752e97f3757c75263
  react-native-tcp-socket: c1b7297619616b4c9caae6889bcb0aba78086989
  react-native-themis: 3af762b1873825e0e55ac65b7c02ecc11010a0de
  react-native-video: c26780b224543c62d5e1b2a7244a5cd1b50e8253
  react-native-video-cache: cae790b459bf82e83181a24aea25a2316395a4f4
  react-native-webview: e6125da6d1cb245ff0692f545b0e5eb3572e12ab
  React-perflogger: d32ee13196f4ae2e4098fb7f8e7ed4f864c6fb0f
  React-RCTActionSheet: 81779c09e34a6a3d6b15783407ba9016a774f535
  React-RCTAnimation: b778eaaa42a884abcc5719035a7a0b2f54672658
  React-RCTBlob: 8edfc04c117decb0e7d4e6ab32bec91707e63ecb
  React-RCTImage: 2022097f1291bfebd0003e477318c72b07853578
  React-RCTLinking: bd8d889c65695181342541ce4420e9419845084c
  React-RCTNetwork: eae64b805d967bf3ece2cec3ad09218eeb32cb74
  React-RCTSettings: 0645af8aec5f40726e98d434a07ff58e75a81aa9
  React-RCTText: e55de507cda263ff58404c3e7d75bf76c2b80813
  React-RCTVibration: c3b8a3245267a3849b0c7cb91a37606bf5f3aa65
  React-runtimeexecutor: 434efc9e5b6d0f14f49867f130b39376c971c1aa
  ReactCommon: a30c2448e5a88bae6fcb0e3da124c14ae493dac1
  RNCAsyncStorage: 8616bd5a58af409453ea4e1b246521bb76578d60
  RNCCheckbox: 6369808337432cdbd5025d62ceced2480b4c51df
  RNCClipboard: 41d8d918092ae8e676f18adada19104fa3e68495
  RNCMaskedView: c298b644a10c0c142055b3ae24d83879ecb13ccd
  RNCPicker: 5256da29a92406cac439ac6605719e233714a86a
  RNCPushNotificationIOS: 87b8d16d3ede4532745e05b03c42cff33a36cc45
  RNDateTimePicker: 1dd15d7ed1ab7d999056bc77879a42920d139c12
  RNDeviceInfo: 0400a6d0c94186d1120c3cbd97b23abc022187a9
  RNFastImage: 5c9c9fed9c076e521b3f509fe79e790418a544e8
  RNFlashList: 8ec7f7454721145fe84566bb9e88bcf58981c9fe
  RNFS: 3ab21fa6c56d65566d1fb26c2228e2b6132e5e32
  RNGestureHandler: 61628a2c859172551aa2100d3e73d1e57878392f
  RNInAppBrowser: e36d6935517101ccba0e875bac8ad7b0cb655364
  RNPermissions: f7ebe52db07c00901127966ca080b4ec6a6ceb0a
  RNReactNativeHapticFeedback: 4085973f5a38b40d3c6793a3ee5724773eae045e
  RNReanimated: 00f33927e607386e5082412df6f69858d515310b
  RNScreens: 522705f2e5c9d27efb17f24aceb2bf8335bc7b8e
  RNSentry: 20fba8a7ad235e06660202e27bd8d297c7303c5e
  RNSha256: e1bc64e9e50b293d5282bb4caa1b2043931f1c9d
  RNSVG: d7d7bc8229af3842c9cfc3a723c815a52cdd1105
  RNVectorIcons: 3bc8be3c5e84f57150b3b556e30e44b91fd5a353
  SDWebImage: a7f831e1a65eb5e285e3fb046a23fcfbf08e696d
  SDWebImageWebPCoder: 908b83b6adda48effe7667cd2b7f78c897e5111d
  Sentry: f8374b5415bc38dfb5645941b3ae31230fbeae57
  SocketRocket: f32cd54efbe0f095c4d7594881e52619cfe80b17
  SSZipArchive: 62d4947b08730e4cda640473b0066d209ff033c9
  TrustKit: 403e8d2cfbff2abfc37cd41acc558dab8ec78e59
  UMTaskManagerInterface: 3184c93ecc290bd422c6e344badc89b601e9c29b
  Yoga: 099a946cbf84c9b32ffdc4278d72db26710ecf92
  YogaKit: f782866e155069a2cca2517aafea43200b01fd5a

PODFILE CHECKSUM: 6631014896711bcfacae231184ee9d239a6f540d

COCOAPODS: 1.15.2
