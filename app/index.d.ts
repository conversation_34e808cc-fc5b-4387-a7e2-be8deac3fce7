declare module '*.gif';
declare module '*.png';
declare module '*.svg';
declare module '*.mp4';

declare module 'net' {
  import TcpSockets from 'react-native-tcp-socket';

  export = TcpSockets;
}

declare module 'react-native-config' {
  export enum ENV {
    PROD = 'production',
    STAGING = 'staging',
    DEV = 'development',
    STORYBOOK = 'storybook',
  }

  export interface NativeConfig {
    ENV: ENV;
    TITLE: string;
    API_URL: string;
    WALLET_API_URL: string;
    STAKEKIT_API_URL: string;
    [name: string]: string | boolean;
  }
  export const Config: NativeConfig;
}
