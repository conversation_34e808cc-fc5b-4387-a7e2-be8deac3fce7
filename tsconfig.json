{
  "compilerOptions": {
    "moduleResolution": "node",
    "module": "es2022",
    "removeComments": true,
    "resolveJsonModule": true,
    "noImplicitAny": true, 
    "allowJs": false,
    "strict": true,
    "incremental": true,
    "composite": false,
    "declarationMap": true,
    "declaration": true,
    "noEmit": true,
    "skipLibCheck": true,
    "target": "ES6",
    "outDir": "lib",
    "lib": ["ES2022"],
    "esModuleInterop": true,
    "downlevelIteration": true,
    "strictPropertyInitialization": false,
    "emitDecoratorMetadata": true,
    "experimentalDecorators": true,
    "allowSyntheticDefaultImports": true,
    "typeRoots": ["./types", "node_modules/@types", "packages/**/node_modules/@types"],
  },
  "include": ["packages"],
  "exclude": ["node_modules", "packages/*/lib"]
}