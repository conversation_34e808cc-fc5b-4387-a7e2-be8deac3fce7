import {
  BatchGetSecretValueCommand,
  SecretsManagerClient,
} from '@aws-sdk/client-secrets-manager';

interface GetSecretsOptions {
  region?: string;
}

export const fetchSecrets = async (
  secretIds: string | string[],
  options?: GetSecretsOptions,
) => {
  if (!secretIds.length || secretIds.length > 20) {
    throw new Error('Secret IDs must be an array of 1 to 20 strings.');
  }

  const client = new SecretsManagerClient({
    region: options?.region || process.env.AWS_REGION || 'us-east-1',
  });

  const response = await client.send(
    new BatchGetSecretValueCommand({
      SecretIdList: typeof secretIds === 'string' ? [secretIds] : secretIds,
    }),
  );

  if (response.Errors?.length) {
    console.error('Errors fetching secrets:', response.Errors);
    throw new Error(
      `Error fetching secrets: ${response.Errors[0].Message} (and potentially more errors)`,
    );
  }

  const secrets: { [key: string]: string } = {};

  if (response.SecretValues) {
    response.SecretValues.forEach((secret) => {
      if (secret.Name && secret.SecretString) {
        secrets[secret.Name] = secret.SecretString;
      }
    });
  } else {
    console.warn('No SecretValues found in the response.');
  }

  console.log(secrets);

  return secrets;
};
