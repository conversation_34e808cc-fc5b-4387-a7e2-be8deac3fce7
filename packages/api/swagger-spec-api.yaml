openapi: 3.0.0
paths:
  /health:
    get:
      operationId: AppController_healthCheck
      parameters: []
      responses:
        '200':
          description: ''
      tags:
        - App
  /network/gas/{network}:
    get:
      operationId: NetworkController_getGasParameters
      parameters:
        - name: network
          required: true
          in: path
          schema:
            type: string
      responses:
        '200':
          description: Fetches gas information for a given network in gwei
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GasDto'
        '400':
          description: ''
      tags:
        - Network
  /chains/submit:
    post:
      description: Submit the signed transactions for broadcasting
      operationId: ChainsController_submit
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SubmitRequestDto'
      responses:
        '200':
          description: Submits the transaction to the given network
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SubmitResponseDto'
        '400':
          description: ''
      security: &a1
        - X-API-KEY: []
      summary: Submit
      tags:
        - broadcast
        - transaction
  /chains/token_balances:
    post:
      description: Given addresses, returns the available balance for each address
      operationId: ChainsController_getTokenBalances
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BalancesRequestDto'
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/TokenBalanceDto'
        '400':
          description: ''
        '500':
          description: ''
      security: *a1
      summary: Token balances
      tags:
        - balances
info:
  title: API Reference
  description: Documentation for the Omni REST API
  version: '1.0'
  contact: {}
tags: []
servers:
  - url: https://api-server.steakwallet.fi
components:
  securitySchemes:
    X-API-KEY:
      type: apiKey
      in: header
      name: X-API-KEY
  schemas:
    AppVersionDto:
      type: object
      properties:
        current:
          type: string
        stable:
          type: string
      required:
        - current
        - stable
    FeatureFlags:
      type: object
      properties:
        nfts:
          type: boolean
        swaps:
          type: boolean
        fiatOnRamp:
          type: boolean
        walletConnect:
          type: boolean
        sweepstakesV2:
          type: boolean
      required:
        - nfts
        - swaps
        - fiatOnRamp
        - walletConnect
        - sweepstakesV2
    FeatureFlagsDto:
      type: object
      properties:
        ios:
          $ref: '#/components/schemas/FeatureFlags'
        android:
          $ref: '#/components/schemas/FeatureFlags'
      required:
        - ios
        - android
    Networks:
      type: string
      enum:
        - avalanche-c
        - arbitrum
        - binance
        - celo
        - ethereum
        - ethereum-goerli
        - fantom
        - gnosis
        - harmony
        - moonriver
        - optimism
        - okc
        - polygon
        - zksync
        - akash
        - cosmos
        - juno
        - kava
        - osmosis
        - persistence
        - stargaze
        - near
        - solana
        - tezos
        - terra
        - bitcoin
    AddressDto:
      type: object
      properties:
        network:
          example: avalanche-c
          allOf:
            - $ref: '#/components/schemas/Networks'
        address:
          type: string
          example: '******************************************'
        tokenAddress:
          type: string
          example: '******************************************'
        additionalAddresses:
          type: object
      required:
        - network
        - address
    SupportedWallets:
      type: string
      enum:
        - MetaMask
        - Keplr
        - Phantom
        - Steakwallet
        - Omni
        - Temple
    AddressesRequestDto:
      type: object
      properties:
        deviceId:
          type: string
        addresses:
          type: array
          items:
            $ref: '#/components/schemas/AddressDto'
        walletType:
          allOf:
            - $ref: '#/components/schemas/SupportedWallets'
      required:
        - deviceId
        - addresses
    NftDto:
      type: object
      properties:
        network:
          allOf:
            - $ref: '#/components/schemas/Networks'
        tokenAddress:
          type: string
        tokenId:
          type: string
      required:
        - network
        - tokenAddress
    HideNftsRequestDto:
      type: object
      properties:
        deviceId:
          type: string
        nfts:
          type: array
          items:
            $ref: '#/components/schemas/NftDto'
      required:
        - deviceId
        - nfts
    TransactionStatus:
      type: string
      enum:
        - BROADCASTED
        - CREATED
        - CONFIRMED
        - FAILED
        - SIGNED
    TransactionsDto:
      type: object
      properties:
        network:
          allOf:
            - $ref: '#/components/schemas/Networks'
        signedTransaction:
          type: string
        unsignedTransaction:
          type: string
        approvalTx:
          type: boolean
        status:
          allOf:
            - $ref: '#/components/schemas/TransactionStatus'
        swapId:
          type: string
        txLink:
          type: string
      required:
        - network
        - signedTransaction
        - unsignedTransaction
        - approvalTx
        - status
        - swapId
        - txLink
    HistoricalTransactionStatus:
      type: string
      enum:
        - pending
        - success
        - error
        - not found
    TransactionStatusDto:
      type: object
      properties:
        status:
          allOf:
            - $ref: '#/components/schemas/HistoricalTransactionStatus'
        url:
          type: string
        blockNumber:
          type: string
        network:
          allOf:
            - $ref: '#/components/schemas/Networks'
        hash:
          type: string
        raw:
          type: object
      required:
        - status
        - url
        - network
        - hash
        - raw
    Transactions:
      type: object
      properties:
        network:
          allOf:
            - $ref: '#/components/schemas/Networks'
        signedTransaction:
          type: string
        unsignedTransaction:
          type: string
        approvalTx:
          type: boolean
      required:
        - network
        - signedTransaction
        - unsignedTransaction
        - approvalTx
    BroadcastTransactionsRequest:
      type: object
      properties:
        swapId:
          type: string
        walletId:
          type: string
        transactions:
          type: array
          items:
            $ref: '#/components/schemas/Transactions'
      required:
        - swapId
        - walletId
        - transactions
    RunnableTxErrorType:
      type: string
      enum:
        - InsufficientFunds
        - InsufficientGas
        - Unknown
        - UnsupportedOperation
        - BalanceError
    RunnableTxErrorDto:
      type: object
      properties:
        success:
          type: boolean
        links:
          type: array
          items:
            type: string
        rawLog:
          type: string
        type:
          allOf:
            - $ref: '#/components/schemas/RunnableTxErrorType'
      required:
        - success
        - rawLog
        - type
    RunnableTxSuccessDto:
      type: object
      properties:
        success:
          type: boolean
        links:
          type: array
          items:
            type: string
      required:
        - success
        - links
    BroadcastTransactionsResponse:
      type: object
      properties:
        response:
          type: array
          items:
            oneOf:
              - $ref: '#/components/schemas/RunnableTxErrorDto'
              - $ref: '#/components/schemas/RunnableTxSuccessDto'
      required:
        - response
    AwaitTransactionDto:
      type: object
      properties:
        network:
          allOf:
            - $ref: '#/components/schemas/Networks'
        transactionHash:
          type: string
      required:
        - network
        - transactionHash
    TokenDto:
      type: object
      properties:
        network:
          allOf:
            - $ref: '#/components/schemas/Networks'
        address:
          type: string
        coinGeckoId:
          type: string
      required:
        - network
    GraphPoints:
      type: object
      properties:
        value:
          type: number
        date:
          type: number
      required:
        - value
        - date
    ChartDataDto:
      type: object
      properties:
        day:
          type: array
          items:
            $ref: '#/components/schemas/GraphPoints'
        week:
          type: array
          items:
            $ref: '#/components/schemas/GraphPoints'
        month:
          type: array
          items:
            $ref: '#/components/schemas/GraphPoints'
        year:
          type: array
          items:
            $ref: '#/components/schemas/GraphPoints'
        all:
          type: array
          items:
            $ref: '#/components/schemas/GraphPoints'
      required:
        - day
        - week
        - month
        - year
        - all
    PriceChartsDto:
      type: object
      properties:
        data:
          nullable: true
          allOf:
            - $ref: '#/components/schemas/ChartDataDto'
      required:
        - data
    Priorities:
      type: string
      enum:
        - CHEAPEST
        - FASTEST
        - RECOMMENDED
    SwapProvider:
      type: string
      enum:
        - Jupiter
        - LiFi
        - Socket
        - Axelar
    SwapRoutesRequest:
      type: object
      properties:
        fromToken:
          type: string
        toToken:
          type: string
        fromNetwork:
          allOf:
            - $ref: '#/components/schemas/Networks'
        toNetwork:
          allOf:
            - $ref: '#/components/schemas/Networks'
        fromAmount:
          type: string
        fromAddress:
          type: string
        toAddress:
          type: string
        options:
          allOf:
            - $ref: '#/components/schemas/Priorities'
        slippage:
          type: string
        aggregrators:
          type: array
          items:
            $ref: '#/components/schemas/SwapProvider'
      required:
        - fromNetwork
        - toNetwork
        - fromAmount
        - fromAddress
        - slippage
    TokensDto:
      type: object
      properties:
        name:
          type: string
        network:
          allOf:
            - $ref: '#/components/schemas/Networks'
        symbol:
          type: string
        decimals:
          type: number
        address:
          type: string
        coinGeckoId:
          type: string
        logoURI:
          type: string
      required:
        - name
        - network
        - symbol
        - decimals
    SwapsGasDto:
      type: object
      properties:
        gasToSpend:
          type: string
        gasToken:
          $ref: '#/components/schemas/TokensDto'
      required:
        - gasToSpend
        - gasToken
    FeeDto:
      type: object
      properties:
        amount:
          type: string
        feeToken:
          $ref: '#/components/schemas/TokensDto'
        omniFeeBps:
          type: string
      required:
        - amount
        - feeToken
        - omniFeeBps
    SwapToolsDto:
      type: object
      properties:
        name:
          type: string
        image:
          type: string
      required:
        - name
        - image
    Tags:
      type: string
      enum:
        - CHEAPEST
        - FASTEST
        - BEST
    SwapRouteDetailsDto:
      type: object
      properties:
        provider:
          allOf:
            - $ref: '#/components/schemas/SwapProvider'
        routeId:
          type: string
        fromAmount:
          type: string
        toAmount:
          type: string
        fromToken:
          $ref: '#/components/schemas/TokensDto'
        toToken:
          $ref: '#/components/schemas/TokensDto'
        gas:
          type: array
          items:
            $ref: '#/components/schemas/SwapsGasDto'
        steps:
          type: number
        minDuration:
          type: string
        fee:
          $ref: '#/components/schemas/FeeDto'
        tools:
          type: array
          items:
            $ref: '#/components/schemas/SwapToolsDto'
        routeObject:
          type: object
        slippage:
          type: string
        tag:
          type: array
          items:
            $ref: '#/components/schemas/Tags'
      required:
        - provider
        - routeId
        - fromAmount
        - toAmount
        - fromToken
        - toToken
        - gas
        - steps
        - minDuration
        - fee
        - tools
        - routeObject
        - slippage
        - tag
    SwapStatus:
      type: string
      enum:
        - PROCESSING
        - WAITING_FOR_NEXT
        - CANCELED
        - FAILED
        - SUCCESS
    SwapRouteDetails:
      type: object
      properties: {}
    SwapTransactionDto:
      type: object
      properties:
        id:
          type: string
        status:
          allOf:
            - $ref: '#/components/schemas/SwapStatus'
        fromAddress:
          type: string
        currentStepIndex:
          type: number
        error:
          type: string
        type:
          type: string
        time:
          type: string
        route:
          $ref: '#/components/schemas/SwapRouteDetails'
      required:
        - id
        - status
        - fromAddress
        - currentStepIndex
        - error
        - type
        - time
        - route
    ExecuteRouteRequestDto:
      type: object
      properties:
        route:
          $ref: '#/components/schemas/SwapRouteDetailsDto'
        address:
          type: string
        index:
          type: number
        walletId:
          type: string
        previousTxHash:
          type: string
        feeAccount:
          type: string
      required:
        - route
        - address
        - index
        - walletId
    TxDetailsDto:
      type: object
      properties:
        txData:
          type: string
        approvalTx:
          type: boolean
      required:
        - txData
        - approvalTx
    ExecuteRoute:
      type: object
      properties:
        network:
          allOf:
            - $ref: '#/components/schemas/Networks'
        unsignedTransactions:
          type: array
          items:
            $ref: '#/components/schemas/TxDetailsDto'
        finalTx:
          type: boolean
        changedRouteId:
          type: string
      required:
        - network
        - unsignedTransactions
        - finalTx
    ExecuteRouteResponseDto:
      type: object
      properties:
        id:
          type: string
        data:
          $ref: '#/components/schemas/ExecuteRoute'
      required:
        - id
        - data
    ReviewRouteRequestDto:
      type: object
      properties:
        route:
          $ref: '#/components/schemas/SwapRouteDetailsDto'
        address:
          type: string
        index:
          type: number
        walletId:
          type: string
        previousTxHash:
          type: string
        feeAccount:
          type: string
      required:
        - route
        - address
        - index
        - walletId
    PrepareTransactionRequestDto:
      type: object
      properties:
        route:
          $ref: '#/components/schemas/SwapRouteDetailsDto'
        userId:
          type: string
        data:
          $ref: '#/components/schemas/ExecuteRoute'
        address:
          type: string
        index:
          type: number
      required:
        - route
        - userId
        - data
        - address
        - index
    PriceRequestDto:
      type: object
      properties:
        currency:
          type: string
        tokenList:
          type: array
          items:
            $ref: '#/components/schemas/TokensDto'
      required:
        - currency
        - tokenList
    PriceResponseDto:
      type: object
      properties: {}
    BalancesRequestDto:
      type: object
      properties:
        addresses:
          type: array
          items:
            $ref: '#/components/schemas/AddressDto'
      required:
        - addresses
    GasMode:
      type: string
      enum:
        - slow
        - average
        - fast
        - custom
    GasModeValueDto:
      type: object
      properties:
        name:
          allOf:
            - $ref: '#/components/schemas/GasMode'
        value:
          type: string
      required:
        - name
        - value
    GasModesDto:
      type: object
      properties:
        denom:
          type: string
          description: The denomination of the token gas is quoted in
        values:
          type: array
          items:
            $ref: '#/components/schemas/GasModeValueDto'
      required:
        - denom
        - values
    SuggestedGasOptionDto:
      type: object
      properties:
        name:
          type: string
        recommendValue:
          type: string
        units:
          type: string
      required:
        - name
        - recommendValue
        - units
    GasDto:
      type: object
      properties:
        customisable:
          type: boolean
        modes:
          $ref: '#/components/schemas/GasModesDto'
        parameters:
          type: object
        suggestedValues:
          type: array
          items:
            $ref: '#/components/schemas/SuggestedGasOptionDto'
      required:
        - customisable
        - modes
        - parameters
        - suggestedValues
    GasPriceStepDto:
      type: object
      properties:
        gasMode:
          description: For simple Omni suggested gas parameter values
          example: average
          allOf:
            - $ref: '#/components/schemas/GasMode'
        gasArgs:
          type: object
          description:
            Custom gas properties to request transaction construction with. Can
            include properties like `gasPrice`, `maxGasPerFee`, etc for EVM
            chains.
      required:
        - gasMode
    TransactionPriceRequestDto:
      type: object
      properties:
        network:
          allOf:
            - $ref: '#/components/schemas/Networks'
        gasLimit:
          type: number
        gasPrice:
          $ref: '#/components/schemas/GasPriceStepDto'
      required:
        - network
        - gasLimit
    TransactionPriceResponseDto:
      type: object
      properties:
        price:
          type: string
      required:
        - price
    RampEventType:
      type: string
      enum:
        - PURCHASE
        - SALE
    RampEvent:
      type: object
      properties:
        deviceId:
          type: string
        rampId:
          type: string
        type:
          allOf:
            - $ref: '#/components/schemas/RampEventType'
      required:
        - deviceId
        - rampId
        - type
    RampPurchaseEventType:
      type: string
      enum:
        - CREATED
        - RELEASED
        - RETURNED
        - ERROR
    RampPurchase:
      type: object
      properties: {}
    OnRampPurchaseEvent:
      type: object
      properties:
        type:
          allOf:
            - $ref: '#/components/schemas/RampPurchaseEventType'
        purchase:
          $ref: '#/components/schemas/RampPurchase'
      required:
        - type
        - purchase
    RampSaleEventType:
      type: string
      enum:
        - CREATED
        - RELEASED
        - EXPIRED
    RampSale:
      type: object
      properties: {}
    OffRampSaleEvent:
      type: object
      properties:
        type:
          allOf:
            - $ref: '#/components/schemas/RampSaleEventType'
        mode:
          type: string
          default: OFFRAMP
        payload:
          $ref: '#/components/schemas/RampSale'
      required:
        - type
        - mode
        - payload
    Object:
      type: object
      properties: {}
    AddressesDto:
      type: object
      properties:
        address:
          type: string
          example: '0x120FA5738751b275aed7F7b46B98beB38679e093'
        additionalAddresses:
          type: object
      required:
        - address
        - additionalAddresses
    SendRequestDto:
      type: object
      properties:
        from:
          $ref: '#/components/schemas/AddressesDto'
        to:
          type: string
        token:
          $ref: '#/components/schemas/TokenDto'
        amount:
          type: string
        memo:
          type: string
        gasPriceStep:
          $ref: '#/components/schemas/GasPriceStepDto'
      required:
        - from
        - to
        - token
        - amount
    GasEstimateDto:
      type: object
      properties:
        amount:
          type: string
        token:
          type: string
        gasLimit:
          type: string
      required:
        - amount
        - token
    UnsignedTransactionDto:
      type: object
      properties:
        network:
          allOf:
            - $ref: '#/components/schemas/Networks'
        tx:
          type: string
        gasEstimate:
          $ref: '#/components/schemas/GasEstimateDto'
        id:
          type: string
      required:
        - network
        - tx
        - gasEstimate
    TransactionsResponseDto:
      type: object
      properties:
        synchronous:
          type: boolean
        txs:
          type: array
          items:
            $ref: '#/components/schemas/UnsignedTransactionDto'
        sessionId:
          type: string
      required:
        - synchronous
        - txs
    SubmitRequestDto:
      type: object
      properties:
        network:
          allOf:
            - $ref: '#/components/schemas/Networks'
        signed:
          type: string
          description: Signed transaction to be broadcast to the network
        id:
          type: string
      required:
        - network
        - signed
    SubmitResponseDto:
      type: object
      properties:
        transactionHash:
          type: string
        link:
          type: string
          description: Link to the blockchain explorer
      required:
        - transactionHash
        - link
    PaginationMetadataDto:
      type: object
      properties:
        itemCount:
          type: number
        totalItems:
          type: number
        itemsPerPage:
          type: number
        totalPages:
          type: number
        currentPage:
          type: number
        first:
          type: string
        previous:
          type: string
        next:
          type: string
        last:
          type: string
      required:
        - itemCount
        - totalItems
        - itemsPerPage
        - totalPages
        - currentPage
        - first
        - previous
        - next
        - last
    PaginatedTokenListReplyDto:
      type: object
      properties:
        tokens:
          type: array
          items:
            $ref: '#/components/schemas/TokensDto'
        paginationData:
          $ref: '#/components/schemas/PaginationMetadataDto'
      required:
        - tokens
        - paginationData
    HighlightedTokensList:
      type: object
      properties:
        name:
          type: string
        icon:
          type: string
        tokens:
          type: array
          items:
            $ref: '#/components/schemas/TokensDto'
        network:
          allOf:
            - $ref: '#/components/schemas/Networks'
      required:
        - name
        - icon
        - tokens
    HighlightedTokenListsResponseDto:
      type: object
      properties:
        tokens:
          type: array
          items:
            $ref: '#/components/schemas/HighlightedTokensList'
      required:
        - tokens
    BalanceDto:
      type: object
      properties:
        amount:
          type: string
      required:
        - amount
    AvailableBalanceDto:
      type: object
      properties:
        available:
          $ref: '#/components/schemas/BalanceDto'
    PortfolioItemBalanceDto:
      type: object
      properties:
        token:
          $ref: '#/components/schemas/TokensDto'
        balances:
          $ref: '#/components/schemas/AvailableBalanceDto'
      required:
        - token
        - balances
    TokenBalanceDto:
      type: object
      properties:
        token:
          $ref: '#/components/schemas/TokensDto'
        amount:
          type: string
      required:
        - token
        - amount
    EvmTransactionDto:
      type: object
      properties:
        value:
          type: string
        data:
          type: string
        to:
          type: string
      required:
        - data
        - to
    DecodeRequestDto:
      type: object
      properties:
        network:
          allOf:
            - $ref: '#/components/schemas/Networks'
        request:
          type: array
          items:
            $ref: '#/components/schemas/EvmTransactionDto'
      required:
        - network
        - request
    DecodedStringDto:
      type: object
      properties:
        value:
          type: string
      required:
        - value
    DecodedStringParameterDto:
      type: object
      properties:
        key:
          type: string
        type:
          type: string
        data:
          $ref: '#/components/schemas/DecodedStringDto'
      required:
        - key
        - type
        - data
    EnrichedAmountDto:
      type: object
      properties:
        tokenAddress:
          type: string
          nullable: true
        amount:
          type: string
        token:
          nullable: true
          allOf:
            - $ref: '#/components/schemas/TokensDto'
      required:
        - tokenAddress
        - amount
        - token
    EnrichedAmountParameterDto:
      type: object
      properties:
        key:
          type: string
        type:
          type: string
        data:
          $ref: '#/components/schemas/EnrichedAmountDto'
      required:
        - key
        - type
        - data
    EnsDto:
      type: object
      properties:
        name:
          type: string
        avatar:
          type: string
          nullable: true
      required:
        - name
        - avatar
    EnrichedAddressDto:
      type: object
      properties:
        address:
          type: string
        name:
          type: string
          nullable: true
        link:
          type: string
          nullable: true
        ens:
          nullable: true
          allOf:
            - $ref: '#/components/schemas/EnsDto'
      required:
        - address
        - name
        - link
        - ens
    EnrichedAddressParameterDto:
      type: object
      properties:
        key:
          type: string
        type:
          type: string
        data:
          $ref: '#/components/schemas/EnrichedAddressDto'
      required:
        - key
        - type
        - data
    EnrichedNftDto:
      type: object
      properties:
        tokenAddress:
          type: string
        tokenId:
          type: string
        image:
          type: string
          nullable: true
        link:
          type: string
          nullable: true
        symbol:
          type: string
          nullable: true
        name:
          type: string
          nullable: true
      required:
        - tokenAddress
        - tokenId
        - image
        - link
        - symbol
        - name
    EnrichedNftParameterDto:
      type: object
      properties:
        key:
          type: string
        type:
          type: string
        data:
          $ref: '#/components/schemas/EnrichedNftDto'
      required:
        - key
        - type
        - data
    DecodeResponseDto:
      type: object
      properties:
        title:
          type: string
        params:
          type: array
          items:
            anyOf:
              - $ref: '#/components/schemas/DecodedStringParameterDto'
              - $ref: '#/components/schemas/EnrichedAmountParameterDto'
              - $ref: '#/components/schemas/EnrichedAddressParameterDto'
              - $ref: '#/components/schemas/EnrichedNftParameterDto'
      required:
        - title
        - params
    ResolveRequestDto:
      type: object
      properties:
        addressOrDomain:
          type: string
        network:
          nullable: true
          allOf:
            - $ref: '#/components/schemas/Networks'
        isAddress:
          type: boolean
      required:
        - addressOrDomain
        - network
        - isAddress
    DomainProvider:
      type: object
      properties:
        name:
          type: string
        image:
          type: string
        link:
          type: string
      required:
        - name
        - image
        - link
    ResolveResponseDto:
      type: object
      properties:
        provider:
          $ref: '#/components/schemas/DomainProvider'
        domain:
          type: string
        address:
          type: string
        network:
          allOf:
            - $ref: '#/components/schemas/Networks'
        avatar:
          type: string
          nullable: true
        link:
          type: string
      required:
        - provider
        - domain
        - address
        - network
        - avatar
        - link
    TokensRequestDto:
      type: object
      properties:
        ethereumAddress:
          type: string
        cosmosAddress:
          type: string
        solanaAddress:
          type: string
        nearAddress:
          type: string
        tezosAddress:
          type: string
    FindNftsDto:
      type: object
      properties:
        addresses:
          $ref: '#/components/schemas/TokensRequestDto'
        testnets:
          type: boolean
      required:
        - addresses
        - testnets
    NftCollectionDto:
      type: object
      properties:
        name:
          type: string
        address:
          type: string
        image:
          type: string
        network:
          allOf:
            - $ref: '#/components/schemas/Networks'
        nfts:
          type: array
          items:
            $ref: '#/components/schemas/NftDto'
      required:
        - name
        - address
        - network
        - nfts
    NftImageDto:
      type: object
      properties:
        standard:
          type: string
        thumbnail:
          type: string
      required:
        - standard
    NftTraitDto:
      type: object
      properties:
        name:
          type: string
        value:
          type: string
      required:
        - name
        - value
    StakedNftsResponseV1Dto:
      type: object
      properties:
        name:
          type: string
        description:
          type: string
        image:
          $ref: '#/components/schemas/NftImageDto'
        traits:
          type: array
          items:
            $ref: '#/components/schemas/NftTraitDto'
        animationUrl:
          type: string
          nullable: true
        externalLink:
          type: string
        address:
          type: string
        tokenId:
          type: string
        staked:
          type: string
        rewards:
          type: string
      required:
        - name
        - description
        - image
        - traits
        - externalLink
        - address
        - staked
        - rewards
    StakedNftDto:
      type: object
      properties:
        name:
          type: string
        description:
          type: string
        image:
          $ref: '#/components/schemas/NftImageDto'
        traits:
          type: array
          items:
            $ref: '#/components/schemas/NftTraitDto'
        animationUrl:
          type: string
          nullable: true
        externalLink:
          type: string
        address:
          type: string
        tokenId:
          type: string
      required:
        - name
        - description
        - image
        - traits
        - externalLink
        - address
    StakedNftsResponseV2Dto:
      type: object
      properties:
        nft:
          $ref: '#/components/schemas/StakedNftDto'
        secondaryNft:
          $ref: '#/components/schemas/StakedNftDto'
        staked:
          type: string
        rewards:
          type: string
      required:
        - staked
        - rewards
    NftTransferDto:
      type: object
      properties:
        network:
          allOf:
            - $ref: '#/components/schemas/Networks'
        from:
          $ref: '#/components/schemas/AddressesDto'
        to:
          type: string
        tokenAddress:
          type: string
        tokenId:
          type: string
        gas:
          $ref: '#/components/schemas/GasPriceStepDto'
      required:
        - network
        - from
        - to
        - tokenAddress
        - gas
    BranchAttributionRequest:
      type: object
      properties:
        deviceId:
          type: string
        branchCampaign:
          type: string
          nullable: true
        branchChannel:
          type: string
          nullable: true
        branchMedium:
          type: string
          nullable: true
      required:
        - deviceId
        - branchCampaign
        - branchChannel
        - branchMedium
    BranchDataRequest:
      type: object
      properties:
        deviceId:
          type: string
        branchId:
          type: string
          nullable: true
        branchReferralCode:
          type: string
          nullable: true
      required:
        - deviceId
        - branchId
        - branchReferralCode
    BranchReferrerRequest:
      type: object
      properties:
        deviceId:
          type: string
        branchReferrer:
          type: string
          nullable: true
      required:
        - deviceId
        - branchReferrer
    MixpanelIdRequest:
      type: object
      properties:
        deviceId:
          type: string
        mixpanelId:
          type: string
          nullable: true
      required:
        - deviceId
        - mixpanelId
    AppleAttribution:
      type: object
      properties:
        keywordId:
          type: number
        clickDate:
          type: string
        adId:
          type: number
        adGroupId:
          type: number
        attribution:
          type: boolean
        campaignId:
          type: number
        conversionType:
          type: string
        orgId:
          type: number
        countryOrRegion:
          type: string
      required:
        - adGroupId
        - attribution
        - campaignId
        - conversionType
        - orgId
        - countryOrRegion
    AppleAttributionRequest:
      type: object
      properties:
        deviceId:
          type: string
        appleAttribution:
          nullable: true
          allOf:
            - $ref: '#/components/schemas/AppleAttribution'
      required:
        - deviceId
        - appleAttribution
    UpdatePushNotificationTokenRequestDto:
      type: object
      properties:
        deviceId:
          type: string
        token:
          type: string
        platform:
          type: string
      required:
        - deviceId
        - token
        - platform
    DeviceIdAndTokenDto:
      type: object
      properties:
        deviceId:
          type: string
        token:
          type: string
      required:
        - deviceId
        - token
    Notification:
      type: object
      properties:
        title:
          type: string
        body:
          type: string
      required:
        - title
        - body
    RegisterTransactionNotificationDto:
      type: object
      properties:
        deviceId:
          type: string
        hash:
          type: string
        notification:
          nullable: true
          allOf:
            - $ref: '#/components/schemas/Notification'
      required:
        - deviceId
        - hash
        - notification
    GetTransactionsDto:
      type: object
      properties:
        network:
          example: avalanche-c
          allOf:
            - $ref: '#/components/schemas/Networks'
        address:
          type: string
          example: '******************************************'
        tokenAddress:
          type: string
          example: '******************************************'
        additionalAddresses:
          type: object
        fromBlock:
          type: number
      required:
        - network
        - address
        - fromBlock
    Category:
      type: string
      enum:
        - Algo-Stables
        - Bridge
        - CDP
        - CEX
        - Chain
        - Cross Chain
        - Derivatives
        - Dexes
        - Farm
        - Gaming
        - Indexes
        - Insurance
        - Launchpad
        - Lending
        - Leveraged Farming
        - Liquid Staking
        - Liquidity manager
        - NFT Lending
        - NFT Marketplace
        - Options
        - Options Vault
        - Oracle
        - Payments
        - Prediction Market
        - Privacy
        - RWA Lending
        - Reserve Currency
        - RWA
        - Services
        - Staking
        - Staking Pool
        - Synthetics
        - Uncollateralized Lending
        - Yield
        - Yield Aggregator
    OraclesByChain:
      type: object
      properties:
        ethereum:
          nullable: true
          type: array
          items:
            type: string
        optimism:
          nullable: true
          type: array
          items:
            type: string
        bsc:
          nullable: true
          type: array
          items:
            type: string
        polygon:
          nullable: true
          type: array
          items:
            type: string
        celo:
          nullable: true
          type: array
          items:
            type: string
        polygon_zkevm:
          nullable: true
          type: array
          items:
            type: string
      required:
        - ethereum
        - optimism
        - bsc
        - polygon
        - celo
        - polygon_zkevm
    ProtocolsDto:
      type: object
      properties:
        id:
          type: string
        name:
          type: string
        address:
          type: string
          nullable: true
        symbol:
          type: string
        url:
          type: string
        description:
          type: string
          nullable: true
        chain:
          type: string
        logo:
          type: string
        audits:
          type: string
          nullable: true
        audit_note:
          type: string
          nullable: true
        gecko_id:
          type: string
          nullable: true
        cmcId:
          type: string
          nullable: true
        chains:
          type: array
          items:
            type: string
        module:
          type: string
        twitter:
          type: string
          nullable: true
        forkedFrom:
          nullable: true
          type: array
          items:
            type: string
        oracles:
          nullable: true
          type: array
          items:
            type: string
        listedAt:
          type: number
          nullable: true
        slug:
          type: string
        tvl:
          type: number
        change_1h:
          type: number
        change_1d:
          type: number
        change_7d:
          type: number
        referralUrl:
          type: string
          nullable: true
        treasury:
          type: string
          nullable: true
        audit_links:
          nullable: true
          type: array
          items:
            type: string
        openSource:
          type: boolean
          nullable: true
        governanceID:
          nullable: true
          type: array
          items:
            type: string
        mcap:
          type: string
          nullable: true
        language:
          type: string
          nullable: true
        parentProtocol:
          type: string
          nullable: true
        staking:
          type: string
          nullable: true
        pool2:
          type: string
          nullable: true
        category:
          allOf:
            - $ref: '#/components/schemas/Category'
        oraclesByChain:
          $ref: '#/components/schemas/OraclesByChain'
      required:
        - id
        - name
        - address
        - symbol
        - url
        - description
        - chain
        - logo
        - audits
        - audit_note
        - gecko_id
        - cmcId
        - chains
        - module
        - twitter
        - forkedFrom
        - oracles
        - listedAt
        - slug
        - tvl
        - change_1h
        - change_1d
        - change_7d
        - referralUrl
        - treasury
        - audit_links
        - openSource
        - governanceID
        - mcap
        - language
        - parentProtocol
        - staking
        - pool2
        - category
        - oraclesByChain
    ProfileDataDto:
      type: object
      properties:
        name:
          type: string
        background:
          type: string
        avatar:
          type: string
      required:
        - name
        - background
        - avatar
    ProfileDataRequestDto:
      type: object
      properties:
        ethereumAddress:
          type: string
        profileData:
          $ref: '#/components/schemas/ProfileDataDto'
      required:
        - ethereumAddress
        - profileData
    StatusImageDto:
      type: object
      properties:
        name:
          type: string
        image:
          type: string
        creator:
          type: string
      required:
        - name
        - image
        - creator
