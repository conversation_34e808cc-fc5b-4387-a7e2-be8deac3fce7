import { Module } from '@nestjs/common';

import { WalletsController } from './wallets.controller';
import { WalletsService } from './wallets.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { HiddenNfts } from '@steakwallet/backend-common';

@Module({
  controllers: [WalletsController],
  providers: [WalletsService],
  exports: [WalletsService],
  imports: [TypeOrmModule.forFeature([HiddenNfts], 'OmniConnection')],
})
export class WalletsModule {}
