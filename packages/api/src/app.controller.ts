import { <PERSON>, Get, Param } from '@nestjs/common';
import { ApiResponse } from '@nestjs/swagger';
import { InjectRepository } from '@nestjs/typeorm';
import { VersionControl } from '@steakwallet/backend-common';
import { allTokens, domains } from '@steakwallet/chains';
import { Networks } from '@steakwallet/types';
import { gt, parse } from 'semver';
import { Repository } from 'typeorm';

import { AppVersionDto, FeatureFlagsDto } from './types';
import memoize from 'memoizee';

@Controller('')
export class AppController {
  constructor(
    @InjectRepository(VersionControl, 'OmniConnection')
    private versionControlRepository: Repository<VersionControl>,
  ) {}
  @Get('health')
  healthCheck(): string {
    return 'Up and running';
  }

  @Get('app-version')
  @ApiResponse({
    status: 200,
    description: 'Returns current and stable version of the app',
    type: AppVersionDto,
  })
  async appVersion(): Promise<AppVersionDto> {
    const versions = await memoize(
      async () => {
        {
          return await this.versionControlRepository
            .createQueryBuilder('versions')
            .select('versions.type')
            .addSelect('versions.version')
            .where("versions.type = 'current'")
            .orWhere("versions.type = 'stable'")
            .getMany();
        }
      },
      { maxAge: 1000 * 60 * 10 }, // 10 minutes
    )();

    const appVersion = {
      current: versions.find((version) => version.type === 'current')!.version,
      stable: versions.find((version) => version.type === 'stable')!.version,
    };

    return appVersion;
  }

  @Get('featureFlags/:version')
  @ApiResponse({
    status: 200,
    description: 'Returns state of the feature flags',
    type: FeatureFlagsDto,
  })
  async featureFlags(
    @Param('version') version: string,
  ): Promise<FeatureFlagsDto> {
    const versions = await memoize(
      async () => {
        return await this.versionControlRepository
          .createQueryBuilder('versions')
          .select('versions.type')
          .addSelect('versions.version')
          .addSelect('versions.feature_flags')
          .where("versions.type = 'current'")
          .orWhere("versions.type = 'guard'")
          .getMany();
      },
      { maxAge: 1000 * 60 * 10 }, // 10 minutes
    )();

    let featureFlags;
    const guard = versions.find((version) => version.type === 'guard')!;
    const current = versions.find((version) => version.type === 'current')!;

    const guardVersion = parse(guard.version)!;
    const appVersion = parse(version)!;

    // Get guard or current flags if app version is higher or lower than guard version
    if (gt(appVersion, guardVersion)) {
      featureFlags = guard?.feature_flags;
    } else {
      featureFlags = current?.feature_flags;
    }

    return {
      ios: {
        nfts: featureFlags.ios.nfts,
        swaps: featureFlags.ios.nfts,
        fiatOnRamp: featureFlags.ios.fiatOnRamp,
        walletConnect: featureFlags.ios.walletConnect,
        sweepstakesV2: featureFlags.ios.sweepstakesV2,
      },
      android: {
        nfts: featureFlags.android.nfts,
        swaps: featureFlags.android.nfts,
        fiatOnRamp: featureFlags.android.fiatOnRamp,
        walletConnect: featureFlags.android.walletConnect,
        sweepstakesV2: featureFlags.android.sweepstakesV2,
      },
    };
  }

  @Get('stats')
  stats() {
    const data = {
      totals: {
        networks: Object.values(Networks).length,
        tokens: allTokens.length,
      },
      byNetwork: domains.reduce(
        (accum, d) => ({
          ...accum,
          [d.network]: {
            tokens: d.tokens.length,
          },
        }),
        {},
      ),
    };
    return `<pre>${JSON.stringify(data, null, 2)}</pre>`;
  }
}
