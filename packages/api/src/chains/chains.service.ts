import { fromBech32, toBech32 } from '@cosmjs/encoding';
import {
  BadRequestException,
  Injectable,
  NotFoundException,
  OnModuleInit,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import {
  Address as AddressEntity,
  SessionStatus,
  sharedGetInitialisedChains,
  sumBalances,
  Transactions,
  TransactionStatus,
  Transfer,
  TransferSessionTypes,
} from '@steakwallet/backend-common';

import {
  allTokens,
  AvailableBalance,
  Chain,
  deduplicateTokenArray,
  fetchWithRetries,
  getChainIdByNetwork,
  getSendTransactions,
  getTokenByAddress,
  highlightedTokens,
  isPortfolioBalanceErrorResponse,
  PortfolioItemBalances,
  submit,
  TokenBalance,
  withTimeout,
} from '@steakwallet/chains';
import {
  decodeAndEnrich,
  providers,
  reverseResolveProviders,
} from '@steakwallet/decoders';
import {
  Address,
  cosmosChainConfig,
  CosmosNetworks,
  covalentSupportedChains,
  ErrorCode,
  ErrorMessage,
  ErrorType,
  EvmNetworks,
  HistoricalEvent,
  HistoricalTransaction,
  Networks,
  OmniError,
  Token,
} from '@steakwallet/types';
import { isPresent } from 'ts-is-present';
import { Repository } from 'typeorm';

import {
  HistoryRequest,
  PaginatedTokenListReplyDto,
  TokensRequestDto,
} from '../types';
import { TokensDto } from '../v1/types';
import {
  DecodeRequestDto,
  GasEstimateDto,
  ResolveRequestDto,
  ResolveResponseDto,
  SendRequestDto,
  SubmitRequestDto,
} from './types';
import { fetchSecrets } from '@omni/common';

type HistoryResolver = (request: HistoryRequest) => Promise<HistoricalEvent[]>;

export declare type TokenResolver = (
  addresses: TokensRequestDto,
  network: Networks,
) => Promise<Token[]>;

@Injectable()
export class ChainsService implements OnModuleInit {
  private covalentApiKey: string | null = null;

  constructor(
    @InjectRepository(Transactions, 'OmniConnection')
    private transactionsRepository: Repository<Transactions>,
    @InjectRepository(AddressEntity, 'OmniConnection')
    private addressRepository: Repository<AddressEntity>,
    @InjectRepository(Transfer, 'OmniConnection')
    private transferRepository: Repository<Transfer>,
  ) {}

  async onModuleInit() {
    const { covalent } = await fetchSecrets('covalent');
    this.covalentApiKey = covalent;
  }

  private historyResolvers: { [n in Networks]?: HistoryResolver } = {
    [Networks.AvalancheC]: (request) => this.evmHistoryResolver(request),
    [Networks.Ethereum]: (request) => this.evmHistoryResolver(request),
    [Networks.Binance]: (request) => this.evmHistoryResolver(request),
    [Networks.Celo]: (request) => this.evmHistoryResolver(request),
    [Networks.Fantom]: (request) => this.evmHistoryResolver(request),
    [Networks.Gnosis]: (request) => this.evmHistoryResolver(request),
    [Networks.Harmony]: (request) => this.evmHistoryResolver(request),
    [Networks.Moonriver]: (request) => this.evmHistoryResolver(request),
    [Networks.Optimism]: (request) => this.evmHistoryResolver(request),
    [Networks.OKC]: (request) => this.evmHistoryResolver(request),
    [Networks.Polygon]: (request) => this.evmHistoryResolver(request),
    [Networks.zkSync]: (request) => this.evmHistoryResolver(request),
    [Networks.Cosmos]: (request) => this.cosmosHistoryResolver(request),
  };

  private tokenResolvers: { [n in Networks]?: TokenResolver } = {
    ...Object.values(covalentSupportedChains).reduce(
      (accum, n) => ({
        ...accum,
        [n]: (addresses: any, network: Networks) =>
          this.covalentTokenResolver(addresses, network),
      }),
      {},
    ),
    ...Object.values(CosmosNetworks).reduce(
      (accum, n) => ({
        ...accum,
        [n]: (addresses: any, network: Networks) =>
          this.cosmosResolver(addresses, network),
      }),
      {},
    ),
    [Networks.Optimism]: (addresses, network) =>
      this.highlightedTokensResolver(addresses.ethereumAddress, network),
    [Networks.Near]: (addresses, network) =>
      this.highlightedTokensResolver(addresses.nearAddress, network),
    [Networks.Tezos]: (addresses, network) =>
      this.highlightedTokensResolver(addresses.tezosAddress, network),
    [Networks.Celo]: (addresses, network) =>
      this.highlightedTokensResolver(addresses.ethereumAddress, network),
    [Networks.Gnosis]: (addresses, network) =>
      this.highlightedTokensResolver(addresses.ethereumAddress, network),
    [Networks.OKC]: (addresses, network) =>
      this.highlightedTokensResolver(addresses.ethereumAddress, network),
    [Networks.zkSync]: (addresses, network) =>
      this.highlightedTokensResolver(addresses.ethereumAddress, network),
  };

  async evmHistoryResolver(
    request: HistoryRequest,
  ): Promise<HistoricalEvent[]> {
    return this.chainsResolver(request);
  }

  async cosmosHistoryResolver(
    request: HistoryRequest,
  ): Promise<HistoricalEvent[]> {
    return this.chainsResolver(request);
  }

  async getTransactionHistory(
    request: HistoryRequest,
  ): Promise<HistoricalEvent[]> {
    try {
      const resolver = this.historyResolvers[request.network];
      if (!resolver) {
        throw new BadRequestException(
          `Missing history resolver for ${request.network}`,
        );
      }

      return await resolver(request);
    } catch (e) {
      console.log(e);
      return [];
    }
  }

  async getTokens(addresses: TokensRequestDto): Promise<TokensDto[]> {
    return deduplicateTokenArray(
      (
        await Promise.all(
          Object.entries(this.tokenResolvers).map(
            async ([network, resolver]) => {
              if (
                (!addresses.ethereumAddress &&
                  Object.values(EvmNetworks).some((v) => v === network)) ||
                (!addresses.cosmosAddress &&
                  Object.values(CosmosNetworks).some((v) => v === network)) ||
                (!addresses.nearAddress && network === Networks.Near) ||
                (!addresses.solanaAddress && network == Networks.Solana) ||
                (!addresses.tezosAddress && network == Networks.Tezos)
              ) {
                return [];
              }
              try {
                return await resolver(addresses, network as Networks);
              } catch (e) {
                console.log(e);
                return [];
              }
            },
          ),
        )
      ).flat(),
    );
  }

  async cosmosResolver({ cosmosAddress }: TokensRequestDto, network: Networks) {
    if (!cosmosAddress) {
      throw new BadRequestException('Missing Address');
    }

    const { data } = fromBech32(cosmosAddress);
    const address = toBech32(
      cosmosChainConfig[network as CosmosNetworks].bech32Prefix,
      data,
    );

    const response = await this.getBalances([
      {
        address: address,
        network: network,
      },
    ]);

    return response
      .map((x) => {
        if (isPortfolioBalanceErrorResponse(x)) {
          return null;
        }

        const balance = sumBalances(x.balances);
        return balance > 0 ? x.token : null;
      })
      .filter(isPresent);
  }

  async highlightedTokensResolver(
    address: string | undefined,
    network: Networks,
  ) {
    const response = await this.getBalances(
      highlightedTokens[network as keyof typeof highlightedTokens].tokens.map(
        (t) => ({
          address: address!,
          tokenAddress: t.address!,
          network,
        }),
      ),
    );

    return response
      .map((x) => {
        if (isPortfolioBalanceErrorResponse(x)) {
          return null;
        }

        const balance = sumBalances(x.balances);
        return balance > 0 ? x.token : null;
      })
      .filter(isPresent);
  }

  async covalentTokenResolver(
    { ethereumAddress, solanaAddress }: TokensRequestDto,
    network: Networks,
  ): Promise<Token[]> {
    const address =
      network === Networks.Solana ? solanaAddress : ethereumAddress;
    if (!address) {
      throw new BadRequestException('Missing Address');
    }

    if (!this.covalentApiKey) {
      throw new BadRequestException('Covalent API key not available');
    }

    const chainId =
      network === Networks.Solana ? 1399811149 : getChainIdByNetwork(network);

    if (!chainId) {
      throw new BadRequestException(
        `Covalent: cannot find chain ID for network ${network}`,
      );
    }
    const response = await withTimeout(
      async () =>
        fetchWithRetries(
          `https://api.covalenthq.com/v1/${chainId}/address/${address}/balances_v2/?key=${this.covalentApiKey}`,
        ),
      10000, // 10_000
    ).catch(() => ({ status: 400, json: () => null }));
    if (response.status !== 200) {
      throw new BadRequestException(
        `Invalid covalent response ${response.status}`,
      );
    }

    const result = await response.json();
    return (
      result.data.items
        .filter((token: { balance: number }) => token.balance > 0)
        // filtering out dust
        .filter((token: { type: string }) => token.type === 'cryptocurrency')
        .map((token: { contract_address: string }) => {
          return getTokenByAddress(
            network,
            token.contract_address ===
              '******************************************'
              ? undefined
              : token.contract_address,
          );
        })
        .filter(isPresent)
    );
  }

  async solanaBeachTokenResolver(
    address: string,
    network: Networks,
  ): Promise<Token[]> {
    const response = await withTimeout(() =>
      fetchWithRetries(
        `https://public-api.solanabeach.io/v1/account/${address}/tokens`,
        {
          headers: {
            Authorization: 'Bearer f5d572ec-eede-4341-8f33-aefb32d8e7c9',
            Accept: 'application/json',
          },
        },
      ),
    );
    if (response.status >= 400) {
      throw new BadRequestException('invalid solana beach result');
    }

    const result = await response.json();
    return result
      .filter((token: { amount: number }) => token.amount > 0)
      .map((token: { mint: { address: string } }) =>
        getTokenByAddress(network, token.mint.address),
      )
      .filter(isPresent);
  }

  async chainsResolver(
    request: HistoryRequest,
  ): Promise<HistoricalTransaction[]> {
    const integration = await sharedGetInitialisedChains(
      request.network,
      request.tokenAddress,
    );

    if (!integration || !integration.chains[0]) {
      throw new NotFoundException(
        `${request.network} ${request.tokenAddress} integration not found`,
      );
    }

    const transactions = await integration.chains[0].getHistoricalTransactions(
      request.address,
      request.fromBlock,
    );

    // clean any huge properties like traces that have been left behind
    return transactions.map((tx) => ({
      ...tx,
      tx: undefined,
    }));
  }

  getPaginatedTokenList(
    page: number,
    itemsPerPage: number,
    baseUrl: string,
    network?: Networks,
    searchTerm?: string,
  ): PaginatedTokenListReplyDto {
    let tokensToPaginate = allTokens;
    // Guard case for terra tokens
    if (network === Networks.Terra) {
      tokensToPaginate = [];
    } else if (network) {
      tokensToPaginate = tokensToPaginate.filter(
        (token) => token.network === network,
      );
    }

    if (searchTerm) {
      tokensToPaginate = tokensToPaginate.filter(
        (token) =>
          token.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          token.symbol.toLowerCase().includes(searchTerm.toLowerCase()) ||
          token.coinGeckoId?.toLowerCase().includes(searchTerm.toLowerCase()),
      );
    }

    const tokensRequested = tokensToPaginate.slice(
      page * itemsPerPage,
      (page + 1) * itemsPerPage,
    );

    const totalPages = Math.ceil(tokensToPaginate.length / itemsPerPage);
    const networkUrl = network !== null ? `&network=${network}` : '';
    const previousPage = page - 1 > 0 ? page - 1 : 0;
    const nextPage = page + 1 < totalPages - 1 ? page + 1 : totalPages - 1;
    const searchTermUrl = searchTerm ? '&searchTerm=' + searchTerm : '';
    return {
      tokens: tokensRequested,
      paginationData: {
        itemCount: tokensRequested.length,
        totalItems: tokensToPaginate.length,
        itemsPerPage: itemsPerPage,
        totalPages: totalPages,
        currentPage: page,
        first: `${baseUrl}page=0&itemsPerPage=${itemsPerPage}${networkUrl}${searchTermUrl}`,
        previous: `${baseUrl}page=${previousPage}&itemsPerPage=${itemsPerPage}${networkUrl}${searchTermUrl}`,
        next: `${baseUrl}page=${nextPage}&itemsPerPage=${itemsPerPage}${networkUrl}${searchTermUrl}`,
        last: `${baseUrl}page=${
          totalPages - 1
        }&itemsPerPage=${itemsPerPage}${networkUrl}${searchTermUrl}`,
      },
    };
  }

  async getHighlightedTokenLists(testnets: boolean) {
    const response = await fetch('https://api.stakek.it/v1/tokens', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'X-API-KEY': 'b00040c9-182c-4064-ab1e-b7ecdb7da13d',
      },
    });

    const enabledYields: {
      token: {
        name: string;
        symbol: string;
        address: string;
        decimals: number;
        network: string;
        coinGeckoId: string;
        logoURI: string;
      };
    }[] = await response.json();

    const list: {
      name: string;
      icon: string;
      tokens: Token[];
      network?: Networks;
    }[] = [
      {
        name: 'Prime Yields',
        icon: 'https://raw.githubusercontent.com/steakwallet/assets/master/omni/omni.png',
        tokens: deduplicateTokenArray(
          enabledYields
            .map((x) =>
              getTokenByAddress(x.token.network as Networks, x.token.address),
            )
            .filter(isPresent),
        ),
      },
      {
        name: 'Cosmos',
        icon: 'https://raw.githubusercontent.com/steakwallet/assets/master/chain-icons/cosmos.png',
        tokens: highlightedTokens.cosmos.tokens,
      },
      {
        name: 'Ethereum',
        icon: 'https://raw.githubusercontent.com/steakwallet/assets/master/chain-icons/ethereum.png',
        tokens: highlightedTokens.ethereum.tokens,
      },
      {
        name: 'Binance Smart Chain',
        icon: 'https://raw.githubusercontent.com/steakwallet/assets/master/chain-icons/binance.png',
        tokens: highlightedTokens.binance.tokens,
      },
      {
        name: 'Solana',
        icon: 'https://raw.githubusercontent.com/steakwallet/assets/master/chain-icons/solana.png',
        tokens: highlightedTokens.solana.tokens,
      },
      {
        name: 'Avalanche',
        icon: 'https://raw.githubusercontent.com/steakwallet/assets/master/chain-icons/avalanche.png',
        tokens: highlightedTokens.avalanche.tokens,
      },
      {
        name: 'Polygon',
        icon: 'https://raw.githubusercontent.com/steakwallet/assets/master/chain-icons/polygon.png',
        tokens: highlightedTokens.polygon.tokens,
      },
      {
        name: 'NEAR',
        icon: 'https://raw.githubusercontent.com/steakwallet/assets/master/chain-icons/near.png',
        tokens: highlightedTokens.near.tokens,
      },
      {
        name: 'Arbitrum',
        icon: 'https://raw.githubusercontent.com/steakwallet/assets/master/chain-icons/arbitrum.png',
        tokens: highlightedTokens.arbitrum.tokens,
      },
      {
        name: 'Optimism',
        icon: 'https://raw.githubusercontent.com/steakwallet/assets/master/chain-icons/optimism.png',
        tokens: highlightedTokens.optimism.tokens,
      },
      {
        name: 'zkSync',
        icon: 'https://raw.githubusercontent.com/steakwallet/assets/master/chain-icons/zksync.png',
        tokens: highlightedTokens.zksync.tokens,
      },
      {
        name: 'Tezos',
        icon: 'https://raw.githubusercontent.com/steakwallet/assets/master/chain-icons/tezos.png',
        tokens: highlightedTokens.tezos.tokens,
      },
      {
        name: 'Gnosis',
        icon: 'https://raw.githubusercontent.com/steakwallet/assets/master/chain-icons/gnosis.png',
        tokens: highlightedTokens.gnosis.tokens,
      },
      {
        name: 'Celo',
        icon: 'https://raw.githubusercontent.com/steakwallet/assets/master/chain-icons/celo.png',
        tokens: highlightedTokens.celo.tokens,
      },
      {
        name: 'OKExChain',
        icon: 'https://raw.githubusercontent.com/steakwallet/assets/master/chain-icons/okx.jpeg',
        tokens: highlightedTokens.okc.tokens,
      },
      {
        name: 'Harmony',
        icon: 'https://raw.githubusercontent.com/steakwallet/assets/master/chain-icons/harmony.png',
        tokens: highlightedTokens.harmony.tokens,
      },
      {
        name: 'Fantom',
        icon: 'https://raw.githubusercontent.com/steakwallet/assets/master/chain-icons/fantom.png',
        tokens: highlightedTokens.fantom.tokens,
      },
      {
        name: 'Moonriver',
        icon: 'https://raw.githubusercontent.com/steakwallet/assets/master/chain-icons/moonriver.png',
        tokens: highlightedTokens.moonriver.tokens,
      },
      {
        name: 'Bitcoin',
        icon: 'https://assets.coingecko.com/coins/images/1/small/bitcoin.png',
        tokens: highlightedTokens.bitcoin.tokens,
      },
      {
        name: 'Tron',
        icon: 'https://s2.coinmarketcap.com/static/img/coins/128x128/1958.png',
        tokens: highlightedTokens.tron.tokens,
      },
    ].filter(isPresent);

    return list;
  }

  async submit(request: SubmitRequestDto) {
    //In case id is not present in request, submit anyways
    if (!request.id) {
      return await submit(request.network, request.signed);
    }

    const tx = await this.transactionsRepository.findOne({
      relations: {
        transfer: {
          transactions: true,
        },
      },
      where: {
        id: request.id,
      },
    });

    if (!tx) {
      throw new BadRequestException(`No transaction with id ${request.id}`);
    }

    if (!tx.transfer) {
      throw new BadRequestException(
        `No session for transaction with id ${request.id}`,
      );
    }

    tx.signedTransaction = request.signed;
    tx.status = TransactionStatus.SIGNED;
    //Tx submit can now be retried in case of error
    await this.transactionsRepository.save(tx);

    try {
      const submission = await submit(request.network, request.signed);

      tx.status = TransactionStatus.BROADCASTED;
      tx.transactionHash = submission.transactionHash;

      Math.max(...tx.transfer.transactions.map((t) => t.stepIndex)) !==
        tx.stepIndex && tx.transfer.synchronous
        ? (tx.transfer.status = SessionStatus.WAITING_FOR_NEXT)
        : (tx.transfer.status = SessionStatus.PROCESSING);

      await this.transferRepository.save(tx.transfer);

      await this.transactionsRepository.save(tx);

      return submission;
    } catch (e: any) {
      tx.status = TransactionStatus.FAILED;
      tx.error = e.message;
      tx.transfer.status = SessionStatus.FAILED;
      await this.transferRepository.save(tx.transfer);
      await this.transactionsRepository.save(tx);
      throw e;
    }
  }

  async send(request: SendRequestDto) {
    const response = await getSendTransactions(
      request.token,
      request.from,
      request.to,
      request.amount,
      request.memo,
      request.gasPriceStep,
    );

    let address: AddressEntity | null;

    address = await this.addressRepository.findOneBy({
      address: request.from.address,
      network: request.token.network,
    });

    if (!address) {
      const entry = this.addressRepository.create({
        ...request.from,
        network: request.token.network,
      });
      address = await this.addressRepository.save(entry);
    }

    const sendSession = new Transfer({
      type: TransferSessionTypes.SEND,
      to: request.to,
      network: request.token.network,
      tokenAddress: request.token.address,
      amount: request.amount,
      currentStepIndex: 0,
      gasArgs: request.gasPriceStep?.gasArgs,
      gasMode: request.gasPriceStep?.gasMode,
      synchronous: response.synchronous,
      transactions: response.txs.map((tx: any, index: any) => {
        return {
          network: tx.network,
          unsignedTransaction: tx.tx,
          stepIndex: index,
          gasEstimate: tx.gasEstimate,
        };
      }) as Transactions[],
      address: address,
    } as Transfer);

    const entry = await this.transferRepository.save(sendSession);

    return {
      sessionId: entry.id,
      synchronous: response.synchronous,
      txs: entry.transactions.map((tx) => {
        return {
          tx: tx.unsignedTransaction,
          network: tx.network as Networks,
          gasEstimate: tx.gasEstimate as GasEstimateDto,
          id: tx.id,
        };
      }),
    };
  }

  async getBalances(addresses: Address[]): Promise<PortfolioItemBalances[]> {
    return (
      await Promise.all(
        addresses.map(
          async ({ network, tokenAddress, address, additionalAddresses }) => {
            try {
              const integration = await sharedGetInitialisedChains(
                network,
                tokenAddress,
              ).catch((e) => {
                console.log(network, tokenAddress, e.message);
                return null;
              });

              if (!integration || !integration.chains.length) {
                throw new OmniError(
                  ErrorType.INTERNAL_ERROR,
                  ErrorCode.INTERNAL_ERROR,
                  ErrorMessage.INTERNAL_ERROR,
                );
              }

              const token = getTokenByAddress(network, tokenAddress);
              if (!token) {
                throw new OmniError(
                  ErrorType.NOT_FOUND,
                  ErrorCode.BAD_REQUEST,
                  `Token with address ${address} on network ${network} not found`,
                );
              }

              const baseBalances = integration.chains.map((chain: Chain) =>
                withTimeout(() =>
                  chain.getBalancesForAddress(address, additionalAddresses),
                ),
              );

              const balances = await Promise.any(baseBalances);

              return {
                ...balances,
                token,
              } as PortfolioItemBalances;
            } catch (e) {
              console.log('Error fetching balances', e);
              return null;
            }
          },
        ),
      )
    ).filter(isPresent);
  }

  async getTokenBalances(addresses: Address[]): Promise<TokenBalance[]> {
    return (
      await Promise.all(
        addresses.map(async ({ network, tokenAddress, address }) => {
          try {
            const integration = await sharedGetInitialisedChains(
              network,
              tokenAddress,
            ).catch((e) => {
              console.log(network, tokenAddress, e.message);
              return null;
            });

            if (!integration || !integration.chains.length) {
              throw new OmniError(
                ErrorType.INTERNAL_ERROR,
                ErrorCode.INTERNAL_ERROR,
                ErrorMessage.INTERNAL_ERROR,
              );
            }

            const token = getTokenByAddress(network, tokenAddress);
            if (!token) {
              throw new OmniError(
                ErrorType.NOT_FOUND,
                ErrorCode.BAD_REQUEST,
                `Token with address ${address} on network ${network} not found`,
              );
            }

            const { balances } = await Promise.any(
              integration.chains.map((chain: Chain) =>
                withTimeout(() => chain.getBalancesForAddress(address)),
              ),
            );

            const amount =
              (balances as AvailableBalance).available?.amount ?? '0';
            const a: TokenBalance = {
              amount,
              token,
            };

            return a;
          } catch (e) {
            console.log('Error fetching balances', e);
            return null;
          }
        }),
      )
    ).filter(isPresent);
  }

  async decode(request: DecodeRequestDto) {
    return await decodeAndEnrich(request);
  }

  async addressResolver(
    request: ResolveRequestDto[],
  ): Promise<ResolveResponseDto[]> {
    const result = await Promise.all(
      request.map(async (x) => {
        if (x.isAddress) {
          return await Promise.all(
            reverseResolveProviders.map(
              async (p) => await p(x.addressOrDomain),
            ),
          );
        } else {
          return await Promise.all(
            providers.map(async (p) => await p(x.addressOrDomain, x.network!)),
          );
        }
      }),
    );
    return result.flat().filter(isPresent);
  }
}
