import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { TransactionsController } from './transactions.controller';
import { TransactionsService } from './transactions.service';
import { Swaps, Transactions, User } from '@steakwallet/backend-common';

@Module({
  imports: [
    TypeOrmModule.forFeature([Transactions, Swaps, User], 'OmniConnection'),
  ],
  controllers: [TransactionsController],
  providers: [TransactionsService],
  exports: [TransactionsService],
})
export class TransactionsModule {}
