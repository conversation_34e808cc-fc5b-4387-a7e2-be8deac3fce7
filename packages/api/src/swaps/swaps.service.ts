import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';

import {
  ExecuteRouteRequest,
  getTokenByAddress,
  JupiterSwap,
  LiFiSwap,
  Priorities,
  SocketSwap,
  SwapProvider,
  SwapRouteDetails,
  SwapWrapper,
  SWAP_PROVIDERS,
  Tags,
  BridgeStatusRequest,
  BridgeStatus,
  getChainIdByNetwork,
} from '@steakwallet/chains';
import { ethers } from 'ethers';
import hash from 'object-hash';
import { Repository, Not } from 'typeorm';

import {
  Swaps,
  Transactions,
  User,
  OmniLogger,
  sendPushNotification,
} from '@steakwallet/backend-common';
import { TransactionsService } from '../transactions';
import {
  ExecuteRouteRequestDto,
  ExecuteRouteResponseDto,
  SwapRouteDetailsDto,
  SwapRoutesRequest,
  SwapStatus,
  SwapTransactionDto,
  TransactionStatus,
  ReviewRouteRequestDto,
  ReviewRouteResponseDto,
  PrepareTransactionRequestDto,
} from '../types';

const DEFAULT_OPTIONS = Priorities.FASTEST;
const DEFAULT_SLIPPAGE = '100';

const getRouteIdentifier = (
  userId: string,
  route: SwapRouteDetailsDto,
): string => hash([userId, route.routeId]);

@Injectable()
export class SwapsService {
  private readonly logger: OmniLogger;
  constructor(
    @InjectRepository(Swaps, 'OmniConnection')
    private swapRepository: Repository<Swaps>,
    @InjectRepository(User, 'OmniConnection')
    private userRepository: Repository<User>,
    @InjectRepository(Transactions, 'OmniConnection')
    private transactionRepository: Repository<Transactions>,
    private readonly transactionService: TransactionsService,
  ) {
    this.logger = new OmniLogger(SwapsService.name);
  }

  providers: { [x in SwapProvider]?: SwapWrapper } = {
    [SwapProvider.LiFi]: new LiFiSwap(),
    [SwapProvider.Socket]: new SocketSwap(),
    [SwapProvider.Jupiter]: new JupiterSwap(),
    // [SwapProvider.Axelar]: new AxelarSwap(),
  };

  isUnsupportedNetworkCombination = (
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    fromNetwork: string,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    toNetwork: string,
  ): boolean => {
    return false;
  };

  getQuotations = async (
    requestDto: SwapRoutesRequest,
  ): Promise<SwapRouteDetailsDto[]> => {
    const {
      fromToken,
      toToken,
      fromNetwork,
      toNetwork,
      fromAmount,
      fromAddress,
      toAddress,
      options,
      slippage,
      aggregrators,
    } = requestDto;

    if (this.isUnsupportedNetworkCombination(fromNetwork, toNetwork)) {
      return [];
    }

    const from = getTokenByAddress(fromNetwork, fromToken);
    const to = getTokenByAddress(toNetwork, toToken);

    if (!from || !to) {
      this.logger.info('unsupported to/from token combination');
      return [];
    }

    this.logger.debug(
      `swap request from ${from.symbol} in ${from.network} to ${to.symbol} in ${to.network}`,
    );

    if (parseInt(slippage!) > 2000) {
      throw new BadRequestException(
        `Slippage ${slippage} specified is sky high`,
      );
    }

    const quotes = await Promise.all(
      (aggregrators ?? SWAP_PROVIDERS)
        .filter((agg) => Object.keys(this.providers).includes(agg))
        .map(async (agg) => {
          try {
            const eachProviderQuotes = await this.providers[agg]!.getQuotations(
              {
                fromToken: from,
                toToken: to,
                fromAmount: ethers.utils
                  .parseUnits(fromAmount, from.decimals)
                  .toString(),
                fromAddress,
                toAddress: toAddress ?? fromAddress,
                options: {
                  priority: options ?? DEFAULT_OPTIONS,
                  slippage: slippage ?? DEFAULT_SLIPPAGE,
                },
              },
            );
            return eachProviderQuotes;
          } catch (error) {
            this.logger.error(
              `Issues retrieving the routes at provider ${agg}, Error: ${
                (error as any).message
              }`,
            );
            return [];
          }
        }),
    );

    this.logger.debug(`responding with ${quotes.flat().length} quotes`);
    if (quotes.flat().length) {
      const quotations = quotes.flat();
      quotations.sort(
        (a, b) => parseFloat(b.toAmount) - parseFloat(a.toAmount),
      );
      const resp = this.tagQuotes(this.deDuplication(quotes.flat()));
      // filter out multi hop swaps
      return resp.filter((q) => q.steps === 1);
    }
    return [];
  };

  compareToolNames = (A: string[], B: string[]): boolean => {
    if (A.length != B.length) return false;
    for (let i = 0, l = A.length; i < l; i++) {
      if (A[i].toLowerCase() != B[i].toLowerCase()) {
        return false;
      }
    }
    return true;
  };

  deDuplication = (routes: SwapRouteDetails[]): SwapRouteDetailsDto[] => {
    function onlyUnique(value: any, index: any, self: string | any[]) {
      return self.indexOf(value) === index;
    }
    const providers = routes.map((e) => e.provider).filter(onlyUnique);
    if (providers.length < 2) {
      return routes;
    }

    // In future, when socket allows fees, to keep the swap routing random
    // providers = providers
    //   .map((value) => ({ value, sort: Math.random() }))
    //   .sort((a, b) => a.sort - b.sort)
    //   .map(({ value }) => value);

    if (providers.length == 2) {
      const Aroutes = routes.filter((e) => e.provider === providers[0]);
      const Broutes = routes.filter((e) => e.provider === providers[1]);
      const res1 = Broutes.filter(
        (a) =>
          !Aroutes.find((b) =>
            this.compareToolNames(
              a.tools.map((x) => x.name),
              b.tools.map((x) => x.name),
            ),
          ),
      );
      return [...res1, ...Aroutes];
    }

    if (providers.length > 2) {
      // this is not possible, as we dont have more than 2 provider for any particular chain if yes then we will fail as of now
      return [];
    }
    return [];
  };

  tagQuotes = (routes: SwapRouteDetails[]): SwapRouteDetailsDto[] => {
    const quotes: SwapRouteDetailsDto[] = routes.map((eachQuote) =>
      Object.assign(eachQuote, { tag: [] as Tags[] }),
    );

    quotes
      .find(
        (quote) =>
          parseFloat(quote.minDuration) ===
          Math.min(...quotes.map((o) => parseFloat(o.minDuration))),
      )!
      .tag.push(Tags.FASTEST);

    const gasQuotes = quotes.map((eachQuo) =>
      eachQuo.gas.reduce((acc, eachGas) => {
        return parseFloat(eachGas.gasToSpend) * 1 + acc;
      }, 0),
    );
    const cheapestIndex = gasQuotes.findIndex(
      (x) => x === Math.min(...gasQuotes.map((y) => y)),
    );

    quotes[cheapestIndex].tag.push(Tags.CHEAPEST);
    return quotes;
  };

  executeRoute = async (
    request: ExecuteRouteRequestDto,
  ): Promise<ExecuteRouteResponseDto> => {
    let swap: Swaps | null;
    const { route, address, walletId } = request;

    const swapService = this.providers[route.provider];
    if (!route.provider || !swapService) {
      throw new BadRequestException(`provider ${route.provider} not found`);
    }
    const user = await this.getOrCreateUser(walletId);

    this.logger.debug(`Retrieving user for wallet ${walletId}`);

    this.logger.debug(`saving the swap route against the user with ${address}`);

    swap = await this.fetchSwap(request.route, user.id);

    if (swap) {
      if (swap.currentStepIndex === request.index) {
        throw new BadRequestException(
          `transaction of swap step index: ${request.index} is already executed`,
        );
      }
      if (swap.status !== SwapStatus.WAITING_FOR_NEXT) {
        throw new BadRequestException(
          `The Swap status is ${swap.status}, So we cannot move further until resolved `,
        );
      }

      if (request.index > 0 && request.route.provider == SwapProvider.Socket) {
        const previousTransactions = await this.fetchSwapTransactions(swap.id);
        request.previousTxHash = previousTransactions.find(
          (e) =>
            new Date(e.updatedAt).getTime() ===
            Math.max(
              ...previousTransactions.map((e) =>
                new Date(e.updatedAt).getTime(),
              ),
            ),
        )!.transactionHash;
      }
    }

    const solanaFeeAccountMnemonic = process.env.SOLANA_TOKEN_ACCOUNTS_MNEMONIC;

    const unsignedTxs = await swapService.executeRoute(
      request,
      SwapProvider.Jupiter ? solanaFeeAccountMnemonic : undefined,
    );

    if (unsignedTxs!.changedRouteId) {
      request.route.routeId = unsignedTxs!.changedRouteId;
    }

    swap = await this.saveSwap(request, user);

    unsignedTxs!.unsignedTransactions.map(async (eachTx) => {
      this.logger.log(`${(user.id, swap!.id, eachTx, unsignedTxs!.network)}`);
      await this.transactionService.saveTransaction(
        user,
        swap!,
        eachTx.txData,
        eachTx.approvalTx,
        unsignedTxs!.network,
      );
    });

    if (!unsignedTxs) {
      throw new NotFoundException(`Swap Provider returned no routes`);
    }

    await this.updateSwapStatus(swap.id, SwapStatus.PROCESSING);

    return { id: swap.id, data: unsignedTxs };
  };

  reviewRoute = async (
    request: ReviewRouteRequestDto,
  ): Promise<ReviewRouteResponseDto> => {
    const { route, address, walletId, index } = request;

    const user = await this.getOrCreateUser(walletId);

    this.logger.debug(`Retrieving user for wallet ${walletId}`);

    this.logger.debug(`saving the swap route against the user with ${address}`);

    const swap = await this.fetchSwap(request.route, user.id);

    if (swap) {
      if (swap.currentStepIndex === index) {
        throw new BadRequestException(
          `transaction of swap step index: ${index} is already executed`,
        );
      }
      if (swap.status !== SwapStatus.WAITING_FOR_NEXT) {
        throw new BadRequestException(
          `The Swap status is ${swap.status}, So we cannot move further until resolved `,
        );
      }

      if (index > 0 && request.route.provider == SwapProvider.Socket) {
        const previousTransactions = await this.fetchSwapTransactions(swap.id);
        request.previousTxHash = previousTransactions.find(
          (e) =>
            new Date(e.updatedAt).getTime() ===
            Math.max(
              ...previousTransactions.map((e) =>
                new Date(e.updatedAt).getTime(),
              ),
            ),
        )!.transactionHash;
      }
    }
    const swapService = this.providers[route.provider];
    if (!route.provider || !swapService) {
      throw new BadRequestException(`provider ${route.provider} not found`);
    }

    const solanaFeeAccountMnemonic = process.env.SOLANA_TOKEN_ACCOUNTS_MNEMONIC;
    const executableResponse = await swapService.executeRoute(
      request,
      SwapProvider.Jupiter ? solanaFeeAccountMnemonic : undefined,
    );

    if (executableResponse!.changedRouteId) {
      request.route.routeId = executableResponse!.changedRouteId;
    }

    return {
      route,
      address,
      index,
      userId: user.id,
      data: executableResponse!,
    };
  };

  prepareTransaction = async (
    request: PrepareTransactionRequestDto,
  ): Promise<boolean> => {
    let swap: Swaps | null;

    const { route, data, userId } = request;

    const user = await this.userRepository.findOneBy({ id: userId });

    if (!user) {
      throw new BadRequestException(`user not found`);
    }

    swap = await this.fetchSwap(route, userId);
    if (!swap) {
      swap = await this.saveSwap(request, user);
    }

    data.unsignedTransactions.map(async (eachTx) => {
      await this.transactionService.saveTransaction(
        user,
        swap!,
        eachTx.txData,
        eachTx.approvalTx,
        data.network,
      );
    });

    await this.updateSwapStatus(swap.id, SwapStatus.PROCESSING);

    return true;
  };

  cancelRoute = async (swapId: string): Promise<boolean> => {
    await this.updateSwapStatus(swapId, SwapStatus.CANCELED);
    return true;
  };

  // retryRouteDetails = async (
  //   request: RetryRouteRequest,
  // ): Promise<RetryRouteResponse | undefined> => {
  //   const { swapId, walletId } = request;
  //   const failedSwaps = await this.getFailedUserRoutes(walletId);
  //   const swap = failedSwaps.find((e) => e.id === swapId);
  //   if (!swap) {
  //     return;
  //   }

  //   return this.retryDetails(swap, walletId);
  // };

  // retryDetails(swap: Swaps, walletId: string): RetryRouteResponse {
  //   const route = swap.swapRoute as SwapRouteDetailsDto;
  //   switch (route.provider) {
  //     case SwapProvider.LiFi: {
  //       const previousStepDetails =
  //         route.routeObject.steps[
  //           swap.currentStepIndex - 1 >= 0 ? swap.currentStepIndex - 1 : 0
  //         ];
  //       const stepDetails = route.routeObject.steps[swap.currentStepIndex];
  //       const lastStepDetails = route.routeObject.steps[route.steps - 1];
  //       return {
  //         fromToken: stepDetails.action.fromToken.address,
  //         fromNetwork: getNetworkByChainId(stepDetails.action.fromChainId),
  //         toToken: lastStepDetails.action.toToken.address,
  //         toNetwork: getNetworkByChainId(lastStepDetails.action.toChainId),
  //         fromAmount: previousStepDetails.estimate.toAmountMin,
  //         swapId: swap.id,
  //         walletId,
  //       };
  //     }
  //     case SwapProvider.Socket: {
  //       const previousStepDetails =
  //         route.routeObject.userTxs[
  //           swap.currentStepIndex - 1 >= 0 ? swap.currentStepIndex - 1 : 0
  //         ];
  //       const stepDetails = route.routeObject.userTxs[swap.currentStepIndex];
  //       const finalStepDetails = route.routeObject.userTxs[route.steps - 1];
  //       return {
  //         fromToken:
  //           stepDetails.fromAsset.address ??
  //           route.routeObject.fromAsset.address,
  //         fromNetwork: getNetworkByChainId(
  //           stepDetails.fromAsset.chainId ??
  //             route.routeObject.fromAsset.chainId,
  //         ),
  //         toToken: finalStepDetails.toAsset.address,
  //         toNetwork: getNetworkByChainId(finalStepDetails.toAsset.chainId),
  //         fromAmount: previousStepDetails.estimate.toAmount,
  //         swapId: swap.id,
  //         walletId,
  //       };
  //     }
  //   }
  // }

  async getOrCreateUser(walletId: string): Promise<User> {
    const user = await this.userRepository.findOneBy({ walletId });
    if (user) {
      return user;
    }
    const newUser = this.userRepository.create();
    newUser.walletId = walletId;

    const row = await this.userRepository.save(newUser);
    this.logger.log(`Created user, ${(row.id, walletId)}`);
    return row;
  }

  async getBridgeStatus(
    provider: SwapProvider,
    request: BridgeStatusRequest,
  ): Promise<BridgeStatus> {
    return this.providers[provider]!.getBridgeStatus(request);
  }

  async getUserRoutes(walletId: string): Promise<SwapTransactionDto[]> {
    const user = await this.userRepository.findOneBy({ walletId });
    if (!user) {
      this.logger.log(`user not found`);
      throw new BadRequestException(`user not found`);
    }

    const swapEntries = await this.swapRepository.find({
      where: {
        user: { id: user.id },
        status: Not(SwapStatus.CANCELED),
      },
    });

    return swapEntries.map(
      ({
        id,
        status,
        fromAddress,
        currentStepIndex,
        error,
        swapRoute,
        createdAt,
      }) => {
        const isSwap =
          swapRoute.fromToken.network === swapRoute.toToken.network;
        return {
          id,
          status,
          fromAddress,
          currentStepIndex,
          error,
          type: isSwap ? 'swap' : 'bridge',
          time: new Date(createdAt).getTime().toString(),
          route: swapRoute,
        };
      },
    );
  }

  async getSwapbyId(swapId: string): Promise<Swaps> {
    const swap = await this.swapRepository.findOneBy([{ id: swapId }]);
    return swap!;
  }

  async getPendingUserRoutes(walletId: string): Promise<SwapTransactionDto[]> {
    const routes = await this.getUserRoutes(walletId);
    return routes.filter(
      (x) =>
        x.status === SwapStatus.PROCESSING ||
        x.status === SwapStatus.WAITING_FOR_NEXT,
    );
  }

  async getPendingRoutes(): Promise<Swaps[]> {
    return await this.swapRepository.findBy([
      {
        status: SwapStatus.PROCESSING,
      },
      {
        status: SwapStatus.WAITING_FOR_NEXT,
      },
    ]);
  }

  async getFailedUserRoutes(walletId: string): Promise<Swaps[]> {
    const user = await this.userRepository.findOneBy({ walletId });
    if (!user) {
      this.logger.log(`user not found`);
      return [];
    }

    return await this.swapRepository.find({
      where: {
        user: { id: user.id },
        status: SwapStatus.FAILED,
      },
    });
  }

  async fetchSwap(
    route: SwapRouteDetails,
    userId: string,
  ): Promise<Swaps | null> {
    const swapRouteIdentifier = getRouteIdentifier(userId, route);
    const exists = await this.swapRepository.findOneBy({ swapRouteIdentifier });
    if (exists) {
      this.logger.log(`Fetching swap ${exists.id}`);
      return exists;
    }
    return null;
  }

  async saveSwap(
    requestDto: ExecuteRouteRequest | ReviewRouteResponseDto,
    user: User,
  ): Promise<Swaps> {
    const { route, address, index } = requestDto;
    const swapRouteIdentifier = getRouteIdentifier(user.id, requestDto.route);
    const row = await this.swapRepository
      .createQueryBuilder()
      .insert()
      .values({
        swapRoute: JSON.parse(JSON.stringify(route)),
        currentStepIndex: index,
        fromAddress: address,
        user: user,
        swapRouteIdentifier,
      })
      .returning('*')
      .execute()
      .then((row) => {
        return row.raw[0];
      });

    this.logger.log(`Saving swap  ${row.id}`);
    return row;
  }

  async updateSwapStatus(swapId: string, status: SwapStatus) {
    await this.swapRepository
      .createQueryBuilder()
      .update({
        status,
      })
      .where({
        id: swapId,
      })
      .execute();
  }

  async fetchSwapTransactions(swapId: string): Promise<Transactions[]> {
    return await this.transactionRepository.find({
      relations: { swap: true, user: true },
      where: {
        swap: { id: swapId },
        approvalTx: false,
      },
    });
  }

  async getWaitingRoutes(): Promise<Swaps[]> {
    return await this.swapRepository.findBy([
      {
        status: SwapStatus.WAITING_FOR_NEXT,
      },
    ]);
  }

  async checkAndTrySendSwapNotification() {
    const waitingSwaps = await this.getWaitingRoutes();

    if (!waitingSwaps.length) {
      return;
    }

    await Promise.all(
      waitingSwaps.map(async (eachSwap) => {
        const message = this.generateSwapReminderMessage(eachSwap);
        if (!message) {
          return;
        }

        await sendPushNotification(eachSwap.fromAddress, message);
        this.logger.info(`Notification sent to ${eachSwap.fromAddress}`);
        this.logger.log(message);
      }),
    );
  }

  generateSwapReminderMessage(swap: Swaps) {
    const { fromToken, toToken } = swap.swapRoute;
    return {
      title: `Your swap requires action`,
      body: `Fren, your ${fromToken.symbol} in (${fromToken.network}) to ${toToken.symbol} in (${toToken.network}) swap is one step closer to completion and now requires your approval to proceed.`,
    };
  }

  async process(): Promise<void> {
    const pendingSwaps = await this.getPendingRoutes();

    await Promise.all(
      pendingSwaps.map(async (swap) => {
        const swapTxFailed =
          await this.transactionService.getFailedTransactions(swap.id);
        const swapTxMined = await this.transactionService.getMinedTransactions(
          swap.id,
        );
        const allSwapTxs = await this.transactionRepository.find({
          where: [
            {
              swap: { id: swap.id },
              approvalTx: false,
              status: TransactionStatus.CONFIRMED,
            },
            {
              swap: { id: swap.id },
              approvalTx: false,
              status: TransactionStatus.BROADCASTED,
            },
          ],
        });

        if (swapTxFailed.length) {
          return this.updateSwapStatus(swap.id, SwapStatus.FAILED);
        }

        if (!swapTxMined.length) {
          return;
        }

        const isSwap =
          swap.swapRoute.fromToken.network === swap.swapRoute.toToken.network;

        if (!isSwap) {
          const nonApprovalTxHash = swapTxMined.find(
            (e) =>
              e.approvalTx == false &&
              e.network === swap.swapRoute.fromToken.network,
          )?.transactionHash;
          try {
            const bridgeStatus = await this.getBridgeStatus(
              swap.swapRoute.provider,
              {
                txHash: nonApprovalTxHash!,
                fromChainId: parseInt(
                  getChainIdByNetwork(swap.swapRoute.fromToken.network)!,
                ),
                toChainId: parseInt(
                  getChainIdByNetwork(swap.swapRoute.toToken.network)!,
                ),
              },
            );

            if (bridgeStatus === BridgeStatus.FAILED) {
              return await this.updateSwapStatus(swap.id, SwapStatus.FAILED);
            }
            if (bridgeStatus === BridgeStatus.PENDING) {
              return;
            }
          } catch (e) {
            console.log('Bridge status error: ', e);
            return;
          }
        }

        if (
          swap.swapRoute.steps === 1 ||
          allSwapTxs.length === swap.swapRoute.steps
        ) {
          return await this.updateSwapStatus(swap.id, SwapStatus.SUCCESS);
        }
        await this.updateSwapStatus(swap.id, SwapStatus.WAITING_FOR_NEXT);
        const message = this.generateSwapReminderMessage(swap);

        if (!message) {
          return;
        }

        await sendPushNotification(swap.fromAddress, message);
        this.logger.info(`Notification sent to ${swap.fromAddress}`);
        this.logger.log(message);
      }),
    );
  }
}
