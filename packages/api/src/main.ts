import fs from 'fs';
import yaml from 'yaml';

import { ValidationPipe } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';

import { AppModule } from './app.module';
import { createSchema } from './db';
import { OmniLogger } from './logger/logger.service';

import { ErrorInterceptor } from './errors';

async function bootstrap() {
  await createSchema();

  const app = await NestFactory.create(AppModule);
  app.useLogger(new OmniLogger());
  app.useGlobalInterceptors(new ErrorInterceptor());
  app.enableVersioning();
  app.enableCors();

  app.useGlobalPipes(
    new ValidationPipe({
      transform: true,
      transformOptions: {
        enableImplicitConversion: false,
      },
    }),
  );

  const config = new DocumentBuilder()
    .addApiKey({ type: 'apiKey', name: 'X-API-KEY', in: 'header' }, 'X-API-KEY')
    .setTitle('API Reference')
    .setDescription('Documentation for the Omni REST API')
    .setVersion('1.0');

  const swaggerDoc = SwaggerModule.createDocument(app, config.build());
  const yamlString = yaml.stringify(swaggerDoc, {});
  fs.writeFileSync('./swagger-spec.yaml', yamlString);
  SwaggerModule.setup('docs', app, swaggerDoc);

  const apiSwaggerDoc = SwaggerModule.createDocument(
    app,
    config.addServer('https://api-server.steakwallet.fi').build(),
  );
  const apiRouteWhitelist = [
    '/health',
    '/chains/submit',
    '/chains/token_balances',
    '/network/gas',
  ];
  Object.keys(apiSwaggerDoc.paths).forEach((path) => {
    if (!apiRouteWhitelist.find((x) => path.startsWith(x))) {
      delete apiSwaggerDoc.paths[path];
    }
  });

  const apiYamlString = yaml.stringify(apiSwaggerDoc, {});
  fs.writeFileSync('./swagger-spec-api.yaml', apiYamlString);

  await app.listen(3000);
}
void bootstrap();
