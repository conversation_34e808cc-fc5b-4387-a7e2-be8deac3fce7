{"name": "@steakwallet/chains", "version": "1.0.109", "scripts": {"clean": "rm -rf lib", "build": "tsc -b .", "test": "jest", "lint": "eslint \"src/**/*.ts\"", "update-token-lists": "./scripts/update-token-lists.sh", "update-diffs": "ts-node --project tsconfig.scripts.json src/test/diffs/update_diffs.ts"}, "files": ["lib"], "main": "lib/index.js", "types": "lib/index.d.ts", "dependencies": {"@anchor-protocol/anchor.js": "^5.0.2", "@axelar-network/axelarjs-sdk": "^0.11.6", "@binance-chain/javascript-sdk": "^4.2.0", "@cosmjs/cosmwasm-stargate": "^0.32.2", "@cosmjs/encoding": "^0.32.2", "@cosmjs/proto-signing": "^0.32.2", "@cosmjs/stargate": "^0.32.2", "@cosmjs/tendermint-rpc": "^0.32.2", "@ensdomains/address-encoder": "^0.2.10", "@ethersproject/experimental": "^5.7.0", "@harmony-js/staking": "^0.1.56", "@lifi/sdk": "^1.7.2", "@metamask/eth-sig-util": "^4.0.1", "@solana/buffer-layout": "^4.0.0", "@solana/spl-token": "^0.3.4", "@solana/spl-token-registry": "^0.2.4574", "@solana/web3.js": "1.52.0", "@steakwallet/signers": "*", "@steakwallet/types": "*", "@sushiswap/default-token-list": "^43.3.0", "@taquito/signer": "^17.0.0", "@taquito/taquito": "^17.0.0", "@taquito/tzip12": "^17.0.0", "@taquito/tzip16": "^17.0.0", "abi-decoder": "^2.4.0", "axios": "0.27.2", "bignumber.js": "^9.1.0", "bip32": "^2.0.6", "bip39": "^3.0.3", "bitcoinjs-lib": "5.2.0", "borsh": "^0.7.0", "bs58": "^5.0.0", "buffer": "^6.0.3", "buffer-layout": "^1.2.2", "cosmjs-types": "^0.9.0", "create-hmac": "^1.1.7", "ethereum-multicall": "^2.14.0", "ethers": "^5.7.0", "isomorphic-dompurify": "^0.15.0", "lodash.invert": "^4.3.0", "lodash.startcase": "^4.4.0", "memoizee": "^0.4.15", "near-api-js": "^1.1.0", "near-seed-phrase": "^0.2.0", "tronweb": "5.1.1", "ts-is-present": "^1.2.1", "web3": "^1.7.3"}, "devDependencies": {"@babel/core": "7.14.8", "@babel/plugin-proposal-numeric-separator": "^7.18.6", "@babel/runtime": "7.14.8", "@types/create-hash": "^1.2.2", "@types/create-hmac": "^1.1.0", "@types/hdkey": "^2.0.1", "@types/jest": "28.1.3", "@types/lodash.invert": "^4.3.7", "@types/lodash.startcase": "^4.4.6", "@types/memoizee": "^0.4.7", "@types/mocha": "^9.0.0", "babel-jest": "28.1.3", "babel-loader": "^8.2.3", "jest": "28.1.3", "ts-jest": "28.0.8", "ts-node": "^10.0.0", "typescript": "^4.6.2"}, "jest": {"bail": 2, "collectCoverage": false, "moduleFileExtensions": ["js", "ts", "json", "node"], "preset": "ts-jest", "testRegex": ".test.ts", "testPathIgnorePatterns": ["/node_modules/", "/lib/"], "testEnvironment": "node", "transform": {"^.+\\.(js|ts)$": "ts-jest"}, "verbose": true}}