[{"network": "optimism", "name": "0xBitcoin", "decimals": 8, "address": "******************************************", "symbol": "0xBTC", "logoURI": "https://ethereum-optimism.github.io/data/0xBTC/logo.png"}, {"network": "optimism", "name": "1INCH Token", "decimals": 18, "address": "******************************************", "symbol": "1INCH", "logoURI": "https://raw.githubusercontent.com/steakwallet/assets/master/tokenicons/1inch.png"}, {"network": "optimism", "name": "<PERSON><PERSON>", "decimals": 18, "address": "******************************************", "symbol": "AAVE", "logoURI": "https://raw.githubusercontent.com/steakwallet/assets/master/tokenicons/aave.png"}, {"network": "optimism", "name": "AladdinCRV", "decimals": 18, "address": "******************************************", "symbol": "aCRV", "logoURI": "https://ethereum-optimism.github.io/data/aCRV/logo.svg"}, {"network": "optimism", "name": "Across Protocol Token", "decimals": 18, "address": "******************************************", "symbol": "ACX", "logoURI": "https://ethereum-optimism.github.io/data/ACX/logo.png"}, {"network": "optimism", "name": "Aelin Protocol", "decimals": 18, "address": "******************************************", "symbol": "AELIN", "logoURI": "https://ethereum-optimism.github.io/data/AELIN/logo.png"}, {"network": "optimism", "name": "<PERSON><PERSON><PERSON>", "decimals": 18, "address": "******************************************", "symbol": "AJNA", "logoURI": "https://ethereum-optimism.github.io/data/AJNA/logo.svg"}, {"network": "optimism", "name": "Alchemix ETH", "decimals": 18, "address": "******************************************", "symbol": "alETH", "logoURI": "https://ethereum-optimism.github.io/data/alETH/logo.svg"}, {"network": "optimism", "name": "Alongside Crypto Market Index", "decimals": 18, "address": "******************************************", "symbol": "AMKT", "logoURI": "https://ethereum-optimism.github.io/data/AMKT/logo.svg"}, {"network": "optimism", "name": "Aptos", "decimals": 8, "address": "******************************************", "symbol": "APT", "logoURI": "https://ethereum-optimism.github.io/data/APT/logo.svg"}, {"network": "optimism", "name": "ARPA Token", "decimals": 18, "address": "******************************************", "symbol": "ARPA", "logoURI": "https://ethereum-optimism.github.io/data/ARPA/logo.svg"}, {"network": "optimism", "name": "AthenaDAO Token", "decimals": 18, "address": "******************************************", "symbol": "ATH", "logoURI": "https://ethereum-optimism.github.io/data/ATH/logo.svg"}, {"network": "optimism", "name": "<PERSON>ra", "decimals": 18, "address": "******************************************", "symbol": "AURA", "logoURI": "https://ethereum-optimism.github.io/data/AURA/logo.svg"}, {"network": "optimism", "name": "Balancer", "decimals": 18, "address": "******************************************", "symbol": "BAL", "logoURI": "https://ethereum-optimism.github.io/data/BAL/logo.png"}, {"network": "optimism", "name": "Bankless Token", "decimals": 18, "address": "******************************************", "symbol": "BANK", "logoURI": "https://ethereum-optimism.github.io/data/BANK/logo.png"}, {"network": "optimism", "name": "BAXagent Coin", "decimals": 18, "address": "******************************************", "symbol": "BAXA", "logoURI": "https://ethereum-optimism.github.io/data/BAXA/logo.svg"}, {"network": "optimism", "name": "BananaCat", "decimals": 9, "address": "******************************************", "symbol": "BCAT", "logoURI": "https://ethereum-optimism.github.io/data/BCAT/logo.png"}, {"network": "optimism", "name": "Biconomy", "decimals": 18, "address": "******************************************", "symbol": "BICO", "logoURI": "https://ethereum-optimism.github.io/data/BICO/logo.svg"}, {"network": "optimism", "name": "BitANT", "decimals": 18, "address": "******************************************", "symbol": "BitANT", "logoURI": "https://ethereum-optimism.github.io/data/BitANT/logo.png"}, {"network": "optimism", "name": "BitBTC", "decimals": 18, "address": "******************************************", "symbol": "BitBTC", "logoURI": "https://ethereum-optimism.github.io/data/BitBTC/logo.png"}, {"network": "optimism", "name": "BOB Token", "decimals": 18, "address": "******************************************", "symbol": "BOB", "logoURI": "https://ethereum-optimism.github.io/data/BOB/logo.svg"}, {"network": "optimism", "name": "<PERSON><PERSON>", "decimals": 18, "address": "******************************************", "symbol": "BOBA", "logoURI": "https://ethereum-optimism.github.io/data/BOBA/logo.svg"}, {"network": "optimism", "name": "BarnBridge Governance Token", "decimals": 18, "address": "******************************************", "symbol": "BOND", "logoURI": "https://ethereum-optimism.github.io/data/BOND/logo.svg"}, {"network": "optimism", "name": "<PERSON>son <PERSON>", "decimals": 18, "address": "******************************************", "symbol": "BOSON", "logoURI": "https://ethereum-optimism.github.io/data/BOSON/logo.svg"}, {"network": "optimism", "name": "Bridged USDC", "decimals": 6, "address": "******************************************", "symbol": "USDC.e", "logoURI": "https://ethereum-optimism.github.io/data/BridgedUSDC/logo.png"}, {"network": "optimism", "name": "<PERSON>", "decimals": 18, "address": "******************************************", "symbol": "BRIGHT", "logoURI": "https://ethereum-optimism.github.io/data/BRIGHT/logo.svg"}, {"network": "optimism", "name": "BTRST", "decimals": 18, "address": "******************************************", "symbol": "BTRST", "logoURI": "https://ethereum-optimism.github.io/data/BTRST/logo.svg"}, {"network": "optimism", "name": "Burn Wrapped AJNA", "decimals": 18, "address": "******************************************", "symbol": "bwAJNA", "logoURI": "https://ethereum-optimism.github.io/data/bwAJNA/logo.svg"}, {"network": "optimism", "name": "Coinbase Wrapped Staked ETH", "decimals": 18, "address": "******************************************", "symbol": "cbETH", "logoURI": "https://ethereum-optimism.github.io/data/cbETH/logo.svg"}, {"network": "optimism", "name": "Chi USD", "decimals": 18, "address": "******************************************", "symbol": "CHI", "logoURI": "https://ethereum-optimism.github.io/data/CHI/logo.svg"}, {"network": "optimism", "name": "Changer", "decimals": 18, "address": "******************************************", "symbol": "CNG", "logoURI": "https://ethereum-optimism.github.io/data/CNG/logo.svg"}, {"network": "optimism", "name": "CryptoOracle Collective", "decimals": 18, "address": "******************************************", "symbol": "COC", "logoURI": "https://ethereum-optimism.github.io/data/COC/logo.svg"}, {"network": "optimism", "name": "Collab.Land", "decimals": 18, "address": "******************************************", "symbol": "COLLAB", "logoURI": "https://ethereum-optimism.github.io/data/COLLAB/logo.png"}, {"network": "optimism", "name": "Curve DAO Token", "decimals": 18, "address": "******************************************", "symbol": "CRV", "logoURI": "https://raw.githubusercontent.com/steakwallet/assets/master/tokenicons/crv.png"}, {"network": "optimism", "name": "Curve.Fi USD Stablecoin", "decimals": 18, "address": "******************************************", "symbol": "crvUSD", "logoURI": "https://ethereum-optimism.github.io/data/crvUSD/logo.svg"}, {"network": "optimism", "name": "CryoDAO", "decimals": 18, "address": "******************************************", "symbol": "CRYO", "logoURI": "https://ethereum-optimism.github.io/data/CRYO/logo.svg"}, {"network": "optimism", "name": "Travel Deals", "decimals": 4, "address": "******************************************", "symbol": "CTRAVL", "logoURI": "https://ethereum-optimism.github.io/data/CTRAVL/logo.svg"}, {"network": "optimism", "name": "<PERSON><PERSON><PERSON>", "decimals": 18, "address": "******************************************", "symbol": "CTSI", "logoURI": "https://ethereum-optimism.github.io/data/CTSI/logo.svg"}, {"network": "optimism", "name": "Prime", "decimals": 18, "address": "******************************************", "symbol": "D2D", "logoURI": "https://ethereum-optimism.github.io/data/D2D/logo.svg"}, {"network": "optimism", "name": "Dai", "decimals": 18, "address": "******************************************", "coinGeckoId": "dai", "symbol": "DAI", "logoURI": "https://raw.githubusercontent.com/steakwallet/assets/master/blockchains/ethereum/assets/******************************************/logo.png"}, {"network": "optimism", "name": "Dentacoin", "decimals": 0, "address": "******************************************", "symbol": "DCN", "logoURI": "https://ethereum-optimism.github.io/data/DCN/logo.svg"}, {"network": "optimism", "name": "dForce", "decimals": 18, "address": "******************************************", "symbol": "DF", "logoURI": "https://ethereum-optimism.github.io/data/DF/logo.svg"}, {"network": "optimism", "name": "DHK DAO", "decimals": 18, "address": "******************************************", "symbol": "DHK", "logoURI": "https://ethereum-optimism.github.io/data/DHK/logo.svg"}, {"network": "optimism", "name": "dHEDGE DAO Token", "decimals": 18, "address": "******************************************", "symbol": "DHT", "logoURI": "https://ethereum-optimism.github.io/data/DHT/logo.svg"}, {"network": "optimism", "name": "<PERSON><PERSON>", "decimals": 18, "address": "******************************************", "symbol": "DIMO", "logoURI": "https://ethereum-optimism.github.io/data/DIMO/logo.svg"}, {"network": "optimism", "name": "Dola USD Stablecoin", "decimals": 18, "address": "******************************************", "symbol": "DOLA", "logoURI": "https://ethereum-optimism.github.io/data/DOLA/logo.svg"}, {"network": "optimism", "name": "DRODEC", "decimals": 18, "address": "******************************************", "symbol": "DRODEC", "logoURI": "https://ethereum-optimism.github.io/data/DRODEC/logo.svg"}, {"network": "optimism", "name": "Unit protocol", "decimals": 18, "address": "******************************************", "symbol": "DUCK", "logoURI": "https://ethereum-optimism.github.io/data/DUCK/logo.png"}, {"network": "optimism", "name": "Eco", "decimals": 18, "address": "******************************************", "symbol": "ECO", "logoURI": "https://ethereum-optimism.github.io/data/ECO/logo.svg"}, {"network": "optimism", "name": "Ethereum Name Service", "decimals": 18, "address": "******************************************", "symbol": "ENS", "logoURI": "https://ethereum-optimism.github.io/data/ENS/logo.png"}, {"network": "optimism", "name": "Equilibria Pendle", "decimals": 18, "address": "******************************************", "symbol": "<PERSON><PERSON><PERSON><PERSON>", "logoURI": "https://ethereum-optimism.github.io/data/ePENDLE/logo.svg"}, {"network": "optimism", "name": "Epoch", "decimals": 18, "address": "******************************************", "symbol": "EPOCH", "logoURI": "https://ethereum-optimism.github.io/data/EPOCH/logo.svg"}, {"network": "optimism", "name": "Equilibria Token", "decimals": 18, "address": "******************************************", "symbol": "EQB", "logoURI": "https://ethereum-optimism.github.io/data/EQB/logo.svg"}, {"network": "optimism", "name": "Equalizer", "decimals": 18, "address": "******************************************", "symbol": "EQZ", "logoURI": "https://ethereum-optimism.github.io/data/EQZ/logo.png"}, {"network": "optimism", "name": "<PERSON>", "decimals": 18, "address": "******************************************", "symbol": "EST", "logoURI": "https://ethereum-optimism.github.io/data/EST/logo.png"}, {"network": "optimism", "name": "<PERSON><PERSON>", "decimals": 18, "address": "******************************************", "symbol": "ETH", "logoURI": "https://raw.githubusercontent.com/steakwallet/assets/master/tokenicons/eth.png"}, {"network": "optimism", "name": "StaFi <PERSON>", "decimals": 18, "address": "******************************************", "symbol": "FIS", "logoURI": "https://ethereum-optimism.github.io/data/FIS/logo.svg"}, {"network": "optimism", "name": "Flashstake", "decimals": 18, "address": "******************************************", "symbol": "FLASH", "logoURI": "https://ethereum-optimism.github.io/data/FLASH/logo.svg"}, {"network": "optimism", "name": "<PERSON>", "decimals": 4, "address": "******************************************", "symbol": "FLy", "logoURI": "https://ethereum-optimism.github.io/data/FLY/logo.png"}, {"network": "optimism", "name": "FOAM Token", "decimals": 18, "address": "******************************************", "symbol": "FOAM", "logoURI": "https://ethereum-optimism.github.io/data/FOAM/logo.svg"}, {"network": "optimism", "name": "ShapeShift FOX", "decimals": 18, "address": "******************************************", "symbol": "FOX", "logoURI": "https://ethereum-optimism.github.io/data/FOX/logo.svg"}, {"network": "optimism", "name": "Frax Price Index", "decimals": 18, "address": "******************************************", "symbol": "FPI", "logoURI": "https://ethereum-optimism.github.io/data/FPI/logo.svg"}, {"network": "optimism", "name": "Frax Price Index Share", "decimals": 18, "address": "******************************************", "symbol": "FPIS", "logoURI": "https://ethereum-optimism.github.io/data/FPIS/logo.svg"}, {"network": "optimism", "name": "FRAX", "decimals": 18, "address": "******************************************", "symbol": "FRAX", "logoURI": "https://ethereum-optimism.github.io/data/FRAX/logo.svg"}, {"network": "optimism", "name": "Fr<PERSON>", "decimals": 18, "address": "******************************************", "symbol": "frxETH", "logoURI": "https://ethereum-optimism.github.io/data/frxETH/logo.svg"}, {"network": "optimism", "name": "FXS", "decimals": 18, "address": "******************************************", "symbol": "FXS", "logoURI": "https://ethereum-optimism.github.io/data/FXS/logo.svg"}, {"network": "optimism", "name": "Giveth Token", "decimals": 18, "address": "******************************************", "symbol": "GIV", "logoURI": "https://ethereum-optimism.github.io/data/GIV/logo.svg"}, {"network": "optimism", "name": "<PERSON><PERSON><PERSON>", "decimals": 18, "address": "******************************************", "symbol": "GMAC", "logoURI": "https://ethereum-optimism.github.io/data/GMAC/logo.svg"}, {"network": "optimism", "name": "R<PERSON>", "decimals": 18, "address": "******************************************", "symbol": "GRG", "logoURI": "https://ethereum-optimism.github.io/data/GRG/logo.svg"}, {"network": "optimism", "name": "ValleyDAO Token", "decimals": 18, "address": "******************************************", "symbol": "GROW", "logoURI": "https://ethereum-optimism.github.io/data/GROW/logo.svg"}, {"network": "optimism", "name": "Gitcoin", "decimals": 18, "address": "******************************************", "symbol": "GTC", "logoURI": "https://ethereum-optimism.github.io/data/GTC/logo.svg"}, {"network": "optimism", "name": "GMO JPY", "decimals": 6, "address": "******************************************", "symbol": "GYEN", "logoURI": "https://ethereum-optimism.github.io/data/GYEN/logo.svg"}, {"network": "optimism", "name": "Geyser", "decimals": 18, "address": "******************************************", "symbol": "GYSR", "logoURI": "https://ethereum-optimism.github.io/data/GYSR/logo.png"}, {"network": "optimism", "name": "HairDAO Token", "decimals": 18, "address": "******************************************", "symbol": "HAIR", "logoURI": "https://ethereum-optimism.github.io/data/HAIR/logo.svg"}, {"network": "optimism", "name": "<PERSON><PERSON><PERSON><PERSON>", "decimals": 18, "address": "******************************************", "symbol": "HAN", "logoURI": "https://ethereum-optimism.github.io/data/HAN/logo.svg"}, {"network": "optimism", "name": "HANePlatform", "decimals": 18, "address": "******************************************", "symbol": "HANeP", "logoURI": "https://ethereum-optimism.github.io/data/HANeP/logo.svg"}, {"network": "optimism", "name": "DAOhaus Token on Optimism", "decimals": 18, "address": "******************************************", "symbol": "HAUS", "logoURI": "https://ethereum-optimism.github.io/data/HAUS/logo.png"}, {"network": "optimism", "name": "Hop", "decimals": 18, "address": "******************************************", "symbol": "HOP", "logoURI": "https://ethereum-optimism.github.io/data/HOP/logo.svg"}, {"network": "optimism", "name": "Impermax", "decimals": 18, "address": "******************************************", "symbol": "IBEX", "logoURI": "https://ethereum-optimism.github.io/data/IBEX/logo.png"}, {"network": "optimism", "name": "Interest Protocol", "decimals": 18, "address": "******************************************", "symbol": "IPT", "logoURI": "https://ethereum-optimism.github.io/data/IPT/logo.svg"}, {"network": "optimism", "name": "Kyber Network Crystal v2", "decimals": 18, "address": "******************************************", "symbol": "KNC", "logoURI": "https://ethereum-optimism.github.io/data/KNC/logo.png"}, {"network": "optimism", "name": "Kryll", "decimals": 18, "address": "******************************************", "symbol": "KRL", "logoURI": "https://ethereum-optimism.github.io/data/KRL/logo.svg"}, {"network": "optimism", "name": "Kromatika", "decimals": 18, "address": "******************************************", "symbol": "KROM", "logoURI": "https://ethereum-optimism.github.io/data/KROM/logo.png"}, {"network": "optimism", "name": "Lido DAO Token", "decimals": 18, "address": "******************************************", "symbol": "LDO", "logoURI": "https://ethereum-optimism.github.io/data/LDO/logo.svg"}, {"network": "optimism", "name": "Chainlink", "decimals": 18, "address": "******************************************", "symbol": "LINK", "logoURI": "https://raw.githubusercontent.com/steakwallet/assets/master/tokenicons/link.png"}, {"network": "optimism", "name": "Theranos Coin", "decimals": 18, "address": "******************************************", "symbol": "LIZ", "logoURI": "https://ethereum-optimism.github.io/data/LIZ/logo.png"}, {"network": "optimism", "name": "Loopfi", "decimals": 18, "address": "******************************************", "symbol": "LPF", "logoURI": "https://ethereum-optimism.github.io/data/LPF/logo.svg"}, {"network": "optimism", "name": "LoopringCoin V2", "decimals": 18, "address": "******************************************", "symbol": "LRC", "logoURI": "https://ethereum-optimism.github.io/data/LRC/logo.png"}, {"network": "optimism", "name": "LUSD Stablecoin", "decimals": 18, "address": "******************************************", "symbol": "LUSD", "logoURI": "https://ethereum-optimism.github.io/data/LUSD/logo.svg"}, {"network": "optimism", "name": "Lyra", "decimals": 18, "address": "******************************************", "symbol": "LYRA", "logoURI": "https://ethereum-optimism.github.io/data/LYRA/logo.png"}, {"network": "optimism", "name": "Mask Network", "decimals": 18, "address": "******************************************", "symbol": "MASK", "logoURI": "https://ethereum-optimism.github.io/data/MASK/logo.svg"}, {"network": "optimism", "name": "Metronome2", "decimals": 18, "address": "******************************************", "symbol": "MET", "logoURI": "https://ethereum-optimism.github.io/data/MET/logo.svg"}, {"network": "optimism", "name": "Maker", "decimals": 18, "address": "******************************************", "symbol": "MKR", "logoURI": "https://raw.githubusercontent.com/steakwallet/assets/master/tokenicons/mkr.png"}, {"network": "optimism", "name": "<PERSON><PERSON>", "decimals": 18, "address": "******************************************", "symbol": "MOCHI", "logoURI": "https://ethereum-optimism.github.io/data/MOCHI/logo.svg"}, {"network": "optimism", "name": "Monetum", "decimals": 18, "address": "******************************************", "symbol": "MOM", "logoURI": "https://ethereum-optimism.github.io/data/MOM/logo.svg"}, {"network": "optimism", "name": "MetaPoolETH", "decimals": 18, "address": "******************************************", "symbol": "mpETH", "logoURI": "https://ethereum-optimism.github.io/data/mpETH/logo.svg"}, {"network": "optimism", "name": "Meta", "decimals": 18, "address": "******************************************", "symbol": "MTA", "logoURI": "https://ethereum-optimism.github.io/data/MTA/logo.svg"}, {"network": "optimism", "name": "Mugloo", "decimals": 4, "address": "******************************************", "symbol": "MUGLOO", "logoURI": "https://ethereum-optimism.github.io/data/MUGLOO/logo.svg"}, {"network": "optimism", "name": "NFTEarthOFT", "decimals": 18, "address": "******************************************", "symbol": "NFTE", "logoURI": "https://ethereum-optimism.github.io/data/NFTE/logo.png"}, {"network": "optimism", "name": "Ocean Token", "decimals": 18, "address": "******************************************", "symbol": "OCEAN", "logoURI": "https://ethereum-optimism.github.io/data/OCEAN/logo.png"}, {"network": "optimism", "name": "Autonolas", "decimals": 18, "address": "******************************************", "symbol": "OLAS", "logoURI": "https://ethereum-optimism.github.io/data/OLAS/logo.svg"}, {"network": "optimism", "name": "Optimism", "decimals": 18, "address": "******************************************", "symbol": "OP", "logoURI": "https://ethereum-optimism.github.io/data/OP/logo.png"}, {"network": "optimism", "name": "Ethereans", "decimals": 18, "address": "******************************************", "symbol": "OS", "logoURI": "https://ethereum-optimism.github.io/data/OS/logo.svg"}, {"network": "optimism", "name": "Paper", "decimals": 18, "address": "******************************************", "symbol": "PAPER", "logoURI": "https://ethereum-optimism.github.io/data/PAPER/logo.svg"}, {"network": "optimism", "name": "<PERSON><PERSON>", "decimals": 18, "address": "******************************************", "symbol": "PENDLE", "logoURI": "https://ethereum-optimism.github.io/data/PENDLE/logo.png"}, {"network": "optimism", "name": "<PERSON><PERSON><PERSON>", "decimals": 18, "address": "******************************************", "symbol": "PEPE", "logoURI": "https://ethereum-optimism.github.io/data/PEPE/logo.svg"}, {"network": "optimism", "name": "Perpetual", "decimals": 18, "address": "******************************************", "symbol": "PERP", "logoURI": "https://ethereum-optimism.github.io/data/PERP/logo.png"}, {"network": "optimism", "name": "PhunToken", "decimals": 18, "address": "******************************************", "symbol": "PHTK", "logoURI": "https://ethereum-optimism.github.io/data/PHTK/logo.svg"}, {"network": "optimism", "name": "PoolTogether", "decimals": 18, "address": "******************************************", "symbol": "POOL", "logoURI": "https://ethereum-optimism.github.io/data/POOL/logo.svg"}, {"network": "optimism", "name": "Popcorn", "decimals": 18, "address": "******************************************", "symbol": "POP", "logoURI": "https://ethereum-optimism.github.io/data/POP/logo.svg"}, {"network": "optimism", "name": "Premia", "decimals": 18, "address": "******************************************", "symbol": "PREMIA", "logoURI": "https://ethereum-optimism.github.io/data/PREMIA/logo.svg"}, {"network": "optimism", "name": "ParaSwap", "decimals": 18, "address": "******************************************", "symbol": "PSP", "logoURI": "https://ethereum-optimism.github.io/data/PSP/logo.svg"}, {"network": "optimism", "name": "Rai Reflex Index", "decimals": 18, "address": "******************************************", "symbol": "RAI", "logoURI": "https://ethereum-optimism.github.io/data/RAI/logo.svg"}, {"network": "optimism", "name": "RAZOR", "decimals": 18, "address": "******************************************", "symbol": "RAZOR", "logoURI": "https://ethereum-optimism.github.io/data/RAZOR/logo.svg"}, {"network": "optimism", "name": "Rocket Pool ETH", "decimals": 18, "address": "******************************************", "symbol": "rETH", "logoURI": "https://ethereum-optimism.github.io/data/rETH/logo.svg"}, {"network": "optimism", "name": "StaFi Staked ETH", "decimals": 18, "address": "******************************************", "symbol": "rETH", "logoURI": "https://ethereum-optimism.github.io/data/rETH_StaFi/logo.svg"}, {"network": "optimism", "name": "Respawn Finance Wrapped Staked Ethereum", "decimals": 18, "address": "******************************************", "symbol": "RFWSTETH", "logoURI": "https://ethereum-optimism.github.io/data/RFWSTETH/logo.svg"}, {"network": "optimism", "name": "Rari Go<PERSON>ken", "decimals": 18, "address": "******************************************", "symbol": "RGT", "logoURI": "https://ethereum-optimism.github.io/data/RGT/logo.png"}, {"network": "optimism", "name": "Rocket Pool Protocol", "decimals": 18, "address": "******************************************", "symbol": "RPL", "logoURI": "https://ethereum-optimism.github.io/data/RPL/logo.svg"}, {"network": "optimism", "name": "SAIL Token", "decimals": 18, "address": "******************************************", "symbol": "SAIL", "logoURI": "https://ethereum-optimism.github.io/data/SAIL/logo.png"}, {"network": "optimism", "name": "Sarcophagus", "decimals": 18, "address": "******************************************", "symbol": "SARCO", "logoURI": "https://ethereum-optimism.github.io/data/SARCO/logo.png"}, {"network": "optimism", "name": "Synthetic Bitcoin", "decimals": 18, "address": "******************************************", "symbol": "sBTC", "logoURI": "https://ethereum-optimism.github.io/data/sBTC/logo.svg"}, {"network": "optimism", "name": "Scry Protocol", "decimals": 18, "address": "******************************************", "symbol": "SCRY", "logoURI": "https://ethereum-optimism.github.io/data/SCRY/logo.svg"}, {"network": "optimism", "name": "Savings Dai", "decimals": 18, "address": "******************************************", "symbol": "sDAI", "logoURI": "https://ethereum-optimism.github.io/data/sDAI/logo.svg"}, {"network": "optimism", "name": "Saddle DAO", "decimals": 18, "address": "******************************************", "symbol": "SDL", "logoURI": "https://ethereum-optimism.github.io/data/SDL/logo.svg"}, {"network": "optimism", "name": "Synthetic Ether", "decimals": 18, "address": "******************************************", "symbol": "sETH", "logoURI": "https://ethereum-optimism.github.io/data/sETH/logo.svg"}, {"network": "optimism", "name": "Staked Frax Ether", "decimals": 18, "address": "******************************************", "symbol": "sfrxETH", "logoURI": "https://ethereum-optimism.github.io/data/sfrxETH/logo.svg"}, {"network": "optimism", "name": "Shutter Token", "decimals": 18, "address": "******************************************", "symbol": "SHU", "logoURI": "https://ethereum-optimism.github.io/data/SHU/logo.png"}, {"network": "optimism", "name": "Silo Governance Token", "decimals": 18, "address": "******************************************", "symbol": "Silo", "logoURI": "https://ethereum-optimism.github.io/data/Silo/logo.svg"}, {"network": "optimism", "name": "<PERSON><PERSON>", "decimals": 18, "address": "******************************************", "symbol": "SIPHER", "logoURI": "https://ethereum-optimism.github.io/data/SIPHER/logo.svg"}, {"network": "optimism", "name": "Synthetic Chainlink", "decimals": 18, "address": "******************************************", "symbol": "sLINK", "logoURI": "https://ethereum-optimism.github.io/data/sLINK/logo.svg"}, {"network": "optimism", "name": "Swarm Markets", "decimals": 18, "address": "******************************************", "symbol": "SMT", "logoURI": "https://ethereum-optimism.github.io/data/SMT/logo.svg"}, {"network": "optimism", "name": "Status Network Token", "decimals": 18, "address": "******************************************", "symbol": "SNT", "logoURI": "https://ethereum-optimism.github.io/data/SNT/logo.svg"}, {"network": "optimism", "name": "Synthetix", "decimals": 18, "address": "******************************************", "symbol": "SNX", "logoURI": "https://raw.githubusercontent.com/steakwallet/assets/master/tokenicons/snx.png"}, {"network": "optimism", "name": "Solana", "decimals": 9, "address": "******************************************", "symbol": "SOL", "logoURI": "https://raw.githubusercontent.com/steakwallet/assets/master/tokenicons/sol.png"}, {"network": "optimism", "name": "SPANK", "decimals": 18, "address": "******************************************", "symbol": "SPANK", "logoURI": "https://ethereum-optimism.github.io/data/SPANK/logo.png"}, {"network": "optimism", "name": "SpaceChainV2", "decimals": 18, "address": "******************************************", "symbol": "SPC", "logoURI": "https://ethereum-optimism.github.io/data/SPC/logo.png"}, {"network": "optimism", "name": "Staked Lyra", "decimals": 18, "address": "******************************************", "symbol": "stkLYRA", "logoURI": "https://ethereum-optimism.github.io/data/stkLYRA/logo.svg"}, {"network": "optimism", "name": "SUKU", "decimals": 18, "address": "******************************************", "symbol": "SUKU", "logoURI": "https://ethereum-optimism.github.io/data/SUKU/logo.png"}, {"network": "optimism", "name": "Synthetix USD", "decimals": 18, "address": "******************************************", "symbol": "sUSD", "logoURI": "https://ethereum-optimism.github.io/data/sUSD/logo.svg"}, {"network": "optimism", "name": "SushiToken", "decimals": 18, "address": "******************************************", "symbol": "SUSHI", "logoURI": "https://raw.githubusercontent.com/steakwallet/assets/master/tokenicons/sushi.png"}, {"network": "optimism", "name": "Synth", "decimals": 18, "address": "******************************************", "symbol": "SYNTH", "logoURI": "https://ethereum-optimism.github.io/data/SYNTH/logo.svg"}, {"network": "optimism", "name": "Threshold Network Token", "decimals": 18, "address": "******************************************", "symbol": "T", "logoURI": "https://ethereum-optimism.github.io/data/T/logo.svg"}, {"network": "optimism", "name": "Optimistic Thales Token", "decimals": 18, "address": "******************************************", "symbol": "THALES", "logoURI": "https://ethereum-optimism.github.io/data/THALES/logo.png"}, {"network": "optimism", "name": "TheDAO", "decimals": 16, "address": "******************************************", "symbol": "TheDAO", "logoURI": "https://ethereum-optimism.github.io/data/TheDAO/logo.svg"}, {"network": "optimism", "name": "Token Name Service", "decimals": 18, "address": "******************************************", "symbol": "TKN", "logoURI": "https://ethereum-optimism.github.io/data/TKN/logo.png"}, {"network": "optimism", "name": "<PERSON><PERSON>", "decimals": 18, "address": "******************************************", "symbol": "TRB", "logoURI": "https://ethereum-optimism.github.io/data/TRB/logo.png"}, {"network": "optimism", "name": "TRON", "decimals": 6, "address": "******************************************", "symbol": "TRX", "logoURI": "https://ethereum-optimism.github.io/data/TRX/logo.svg"}, {"network": "optimism", "name": "TrueUSD", "decimals": 18, "address": "******************************************", "symbol": "TUSD", "logoURI": "https://ethereum-optimism.github.io/data/TUSD/logo.png"}, {"network": "optimism", "name": "Universal Basic Income", "decimals": 18, "address": "******************************************", "symbol": "UBI", "logoURI": "https://ethereum-optimism.github.io/data/UBI/logo.svg"}, {"network": "optimism", "name": "Unlock Discount Token", "decimals": 18, "address": "******************************************", "symbol": "UDT", "logoURI": "https://ethereum-optimism.github.io/data/UDT/logo.svg"}, {"network": "optimism", "name": "UMA Voting Token v1", "decimals": 18, "address": "******************************************", "symbol": "UMA", "logoURI": "https://ethereum-optimism.github.io/data/UMA/logo.png"}, {"network": "optimism", "name": "Uniswap", "decimals": 18, "address": "******************************************", "symbol": "UNI", "logoURI": "https://raw.githubusercontent.com/steakwallet/assets/master/tokenicons/uni.png"}, {"network": "optimism", "name": "USD Coin", "decimals": 6, "address": "******************************************", "coinGeckoId": "usd-coin", "symbol": "USDC", "logoURI": "https://raw.githubusercontent.com/steakwallet/assets/master/blockchains/ethereum/assets/******************************************/logo.png"}, {"network": "optimism", "name": "Decentralized USD", "decimals": 18, "address": "******************************************", "symbol": "USDD", "logoURI": "https://ethereum-optimism.github.io/data/USDD/logo.png"}, {"network": "optimism", "name": "Glo Dollar", "decimals": 18, "address": "******************************************", "symbol": "USDGLO", "logoURI": "https://ethereum-optimism.github.io/data/USDGLO/logo.svg"}, {"network": "optimism", "name": "USD+", "decimals": 6, "address": "******************************************", "symbol": "USD+", "logoURI": "https://ethereum-optimism.github.io/data/USDplus/logo.svg"}, {"network": "optimism", "name": "<PERSON><PERSON>", "decimals": 6, "address": "******************************************", "coinGeckoId": "tether", "symbol": "USDT", "logoURI": "https://raw.githubusercontent.com/steakwallet/assets/master/blockchains/ethereum/assets/******************************************/logo.png"}, {"network": "optimism", "name": "UST (Wormhole)", "decimals": 6, "address": "******************************************", "symbol": "UST", "logoURI": "https://ethereum-optimism.github.io/data/UST/logo.png"}, {"network": "optimism", "name": "dForce USD", "decimals": 18, "address": "******************************************", "symbol": "USX", "logoURI": "https://ethereum-optimism.github.io/data/USX/logo.svg"}, {"network": "optimism", "name": "Validator", "decimals": 18, "address": "******************************************", "symbol": "VALX", "logoURI": "https://ethereum-optimism.github.io/data/VALX/logo.png"}, {"network": "optimism", "name": "veKwenta", "decimals": 18, "address": "******************************************", "symbol": "veKWENTA", "logoURI": "https://ethereum-optimism.github.io/data/veKWENTA/logo.svg"}, {"network": "optimism", "name": "Velodrome", "decimals": 18, "address": "******************************************", "symbol": "VELO", "logoURI": "https://ethereum-optimism.github.io/data/VELO/logo.svg"}, {"network": "optimism", "name": "VitaDAO Token", "decimals": 18, "address": "******************************************", "symbol": "VITA", "logoURI": "https://ethereum-optimism.github.io/data/VITA/logo.svg"}, {"network": "optimism", "name": "VesperToken", "decimals": 18, "address": "******************************************", "symbol": "VSP", "logoURI": "https://ethereum-optimism.github.io/data/VSP/logo.svg"}, {"network": "optimism", "name": "VUSD", "decimals": 18, "address": "******************************************", "symbol": "VUSD", "logoURI": "https://ethereum-optimism.github.io/data/VUSD/logo.svg"}, {"network": "optimism", "name": "WardenSwap", "decimals": 18, "address": "******************************************", "symbol": "WAD", "logoURI": "https://ethereum-optimism.github.io/data/WAD/logo.png"}, {"network": "optimism", "name": "Wrapped BTC", "decimals": 8, "address": "******************************************", "symbol": "wBTC", "logoURI": "https://raw.githubusercontent.com/steakwallet/assets/master/tokenicons/wbtc.png"}, {"network": "optimism", "name": "WalletConnect", "decimals": 18, "address": "******************************************", "symbol": "WCT", "logoURI": "https://ethereum-optimism.github.io/data/WCT/logo.svg"}, {"network": "optimism", "name": "Wrapped eETH", "decimals": 18, "address": "******************************************", "symbol": "weETH", "logoURI": "https://ethereum-optimism.github.io/data/weETH/logo.svg"}, {"network": "optimism", "name": "Wrapped Ether", "decimals": 18, "address": "******************************************", "symbol": "wETH", "logoURI": "https://raw.githubusercontent.com/steakwallet/assets/master/tokenicons/weth.png"}, {"network": "optimism", "name": "Worldcoin", "decimals": 18, "address": "******************************************", "symbol": "WLD", "logoURI": "https://ethereum-optimism.github.io/data/WLD/logo.svg"}, {"network": "optimism", "name": "Wootrade Network", "decimals": 18, "address": "******************************************", "symbol": "WOO", "logoURI": "https://ethereum-optimism.github.io/data/WOO/logo.svg"}, {"network": "optimism", "name": "OpenX Optimism", "decimals": 18, "address": "******************************************", "symbol": "wOpenX", "logoURI": "https://ethereum-optimism.github.io/data/wOpenX/logo.svg"}, {"network": "optimism", "name": "Wrapped liquid staked Ether 2.0", "decimals": 18, "address": "******************************************", "symbol": "wstETH", "logoURI": "https://ethereum-optimism.github.io/data/wstETH/logo.svg"}, {"network": "optimism", "name": "wTBT(Bridge Token)", "decimals": 18, "address": "******************************************", "symbol": "wTBT", "logoURI": "https://ethereum-optimism.github.io/data/wTBT/logo.svg"}, {"network": "optimism", "name": "CryptoFranc", "decimals": 18, "address": "******************************************", "symbol": "XCHF", "logoURI": "https://ethereum-optimism.github.io/data/XCHF/logo.png"}, {"network": "optimism", "name": "Darwinia Commitment xKTON", "decimals": 18, "address": "******************************************", "symbol": "xKTON", "logoURI": "https://ethereum-optimism.github.io/data/XKTON/logo.svg"}, {"network": "optimism", "name": "MetalSwap", "decimals": 18, "address": "******************************************", "symbol": "XMT", "logoURI": "https://ethereum-optimism.github.io/data/XMT/logo.svg"}, {"network": "optimism", "name": "Darwinia Network xRING", "decimals": 18, "address": "******************************************", "symbol": "xRING", "logoURI": "https://ethereum-optimism.github.io/data/XRING/logo.svg"}, {"network": "optimism", "name": "XY Oracle", "decimals": 18, "address": "******************************************", "symbol": "XYO", "logoURI": "https://ethereum-optimism.github.io/data/XYO/logo.svg"}, {"network": "optimism", "name": "yearn.finance", "decimals": 18, "address": "******************************************", "symbol": "YFI", "logoURI": "https://raw.githubusercontent.com/steakwallet/assets/master/tokenicons/yfi.png"}, {"network": "optimism", "name": "xZoomerCoin", "decimals": 18, "address": "******************************************", "symbol": "xZOOMER", "logoURI": "https://ethereum-optimism.github.io/data/ZOOMER/logo.png"}, {"network": "optimism", "name": "0x Protocol Token", "decimals": 18, "address": "******************************************", "symbol": "ZRX", "logoURI": "https://ethereum-optimism.github.io/data/ZRX/logo.png"}, {"network": "optimism", "name": "Zunami Governance Token", "decimals": 18, "address": "******************************************", "symbol": "ZUN", "logoURI": "https://ethereum-optimism.github.io/data/ZUN/logo.svg"}, {"network": "optimism", "name": "Zunami ETH", "decimals": 18, "address": "******************************************", "symbol": "zunETH", "logoURI": "https://ethereum-optimism.github.io/data/zunETH/logo.svg"}, {"network": "optimism", "name": "Zunami USD", "decimals": 18, "address": "******************************************", "symbol": "zunUSD", "logoURI": "https://ethereum-optimism.github.io/data/zunUSD/logo.svg"}, {"network": "optimism", "name": "Z.com USD", "decimals": 6, "address": "******************************************", "symbol": "ZUSD", "logoURI": "https://ethereum-optimism.github.io/data/ZUSD/logo.svg"}, {"network": "optimism", "name": "agEUR", "decimals": 18, "address": "******************************************", "symbol": "agEUR", "logoURI": "https://raw.githubusercontent.com/sushiswap/list/master/logos/token-logos/network/optimism/******************************************.jpg"}, {"network": "optimism", "name": "Axelar Wrapped USDC", "decimals": 6, "address": "******************************************", "symbol": "axlUSDC", "logoURI": "https://raw.githubusercontent.com/sushiswap/list/master/logos/token-logos/network/optimism/******************************************.jpg"}, {"network": "optimism", "name": "Mai Stablecoin", "decimals": 18, "address": "******************************************", "symbol": "MAI", "logoURI": "https://raw.githubusercontent.com/sushiswap/assets/master/blockchains/polygon/assets/******************************************/logo.png"}]