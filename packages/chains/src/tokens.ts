import * as arbitrum from './arbitrum';
import { avalanche } from './avalanche';
import { binanceSmartChain } from './binance';
import * as celo from './celo';
import { Domain } from './common';
import { Token, Networks } from '@steakwallet/types';
import { domains as cosmos } from './cosmos/cosmos-chains';
import { ethereum } from './ethereum';
import * as fantom from './fantom';
import * as ethereumGoerli from './ethereum/testnets/goerli';
import * as gnosis from './gnosis';
import * as harmony from './harmony';
import * as moonriver from './moonriver';
import * as near from './near';
import * as optimism from './optimism';
import * as okc from './okc';
import * as polygon from './polygon';
import * as solana from './solana';
import * as tezos from './tezos';
import * as zkSync from './zksync';
import tron from './tron';
import bitcoin from './bitcoin';

import sortedTokens from './token-lists/sorted.json';
export const domains: Domain[] = [
  arbitrum,
  avalanche,
  binanceSmartChain,
  celo,
  ...cosmos,
  ethereum,
  fantom,
  harmony,
  moonriver,
  optimism,
  okc,
  polygon,
  near,
  solana,
  tezos,
  gnosis,
  zkSync,
  bitcoin,
  tron,
];

export const testnetDomains: Domain[] = [ethereumGoerli];

export const allTokens = sortedTokens as Token[];

export const BaseTokens: Token[] = allTokens.filter((t) => !t.address);

export function isCosmosChain(network: Networks) {
  return !!Object.values(cosmos).find((x) => x.network === network);
}
