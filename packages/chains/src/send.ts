import {
  CosmosNetworks,
  ErrorType,
  EvmNetworks,
  Networks,
  ErrorCode,
  OmniError,
} from '@steakwallet/types';
import { GetSendNftTransactions, GetSendTransactions } from './common';

import * as evm from './evm';
import * as near from './near';
import * as cosmos from './cosmos';
import * as tezos from './tezos';
import * as solana from './solana';
import * as bitcoin from './bitcoin';
import * as tron from './tron';

const sends: { [n in Networks]?: GetSendTransactions } = {
  ...Object.values(EvmNetworks).reduce(
    (accum, n) => ({ ...accum, [n]: evm.send }),
    {},
  ),
  ...Object.values(CosmosNetworks).reduce(
    (accum, n) => ({ ...accum, [n]: cosmos.send }),
    {},
  ),
  [Networks.Near]: near.send,
  [Networks.Tezos]: tezos.send,
  [Networks.Solana]: solana.send,
  [Networks.Bitcoin]: bitcoin.send,
  [Networks.Tron]: tron.send,
};

export const getSendTransactions: GetSendTransactions = (token, ...args) => {
  const impl = sends[token.network];
  if (!impl) {
    throw new OmniError(
      ErrorType.NOT_IMPLEMENTED,
      ErrorCode.NOT_IMPLEMENTED,
      'Missing send implementation for network',
    );
  }

  return impl(token, ...args);
};

export const sendsNft: { [n in Networks]?: GetSendNftTransactions } = {
  ...Object.values(EvmNetworks).reduce(
    (accum, n) => ({ ...accum, [n]: evm.sendNft }),
    {},
  ),
  [Networks.Solana]: solana.sendNft,
  [Networks.Stargaze]: cosmos.sendNft,
  [Networks.Tezos]: tezos.sendNft,
};

export const getSendNftTransactions: GetSendNftTransactions = (
  network,
  ...args
) => {
  const impl = sendsNft[network];
  if (!impl) {
    throw new OmniError(
      ErrorType.NOT_IMPLEMENTED,
      ErrorCode.NOT_IMPLEMENTED,
      'Missing send implementation for network',
    );
  }

  return impl(network, ...args);
};
