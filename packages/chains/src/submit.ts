import {
  Networks,
  EvmNetworks,
  CosmosNetworks,
  OmniError,
  ErrorType,
  ErrorCode,
} from '@steakwallet/types';
import { Submit } from './common';

import * as evm from './evm';
import * as near from './near';
import * as cosmos from './cosmos';
import * as solana from './solana';
import * as tezos from './tezos';
import * as tron from './tron';
import * as bitcoin from './bitcoin';
const getters: { [n in Networks]?: Submit } = {
  ...Object.values(EvmNetworks).reduce(
    (accum, n) => ({
      ...accum,
      [n]: (signed: string) => evm.submit(signed, n),
    }),
    {},
  ),
  ...Object.values(CosmosNetworks).reduce(
    (accum, n) => ({
      ...accum,
      [n]: (signed: string) => cosmos.submit(signed, n),
    }),
    {},
  ),
  [Networks.Near]: near.submit,
  [Networks.Solana]: solana.submit,
  [Networks.Tezos]: tezos.submit,
  [Networks.Bitcoin]: bitcoin.submit,
  [Networks.Tron]: tron.submit,
};

export const submit = (network: Networks, signedTransaction: string) => {
  const impl = getters[network];
  if (!impl) {
    throw new OmniError(
      ErrorType.NOT_IMPLEMENTED,
      ErrorCode.NOT_IMPLEMENTED,
      'Missing submit implementation for network',
    );
  }

  return impl(signedTransaction);
};
