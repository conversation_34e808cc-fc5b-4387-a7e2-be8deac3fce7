import {
  ASSOCIATED_TOKEN_PROGRAM_ID,
  getAssociatedTokenAddress,
  TOKEN_PROGRAM_ID,
} from '@solana/spl-token';
import {
  Connection,
  PublicKey,
  sendAndConfirmRawTransaction,
  StakeProgram,
  SystemProgram,
  Transaction,
} from '@solana/web3.js';
import { getSolanaWallet, SolanaSigner } from '@steakwallet/signers';
import {
  Networks,
  RunnableTxError,
  RunnableTxErrorType,
  RunnableTxResponse,
  RunnableTxSuccess,
  Token,
} from '@steakwallet/types';
import * as bip32 from 'bip32';
import base58 from 'bs58';
import memoize from 'memoizee';

import { ASSETS_URL, BaseChain, ChainServices, fromWei } from '../common';
import { SolanaNFTWrapper } from './nft.wrapper';
import { getOrCreateAssociatedTokenAccount } from './utils';

export const baseConfig: Token = {
  network: Networks.Solana,
  name: '<PERSON><PERSON>',
  symbol: 'SOL',
  decimals: 9,
  coinGeckoId: 'solana',
  logoURI: `${ASSETS_URL}/tokenicons/sol.png`,
};

export const getConnection = memoize((rpcUri: string) => {
  return new Connection(rpcUri, 'confirmed');
});

const getConfig = memoize(async (connection: Connection) => {
  const [minimumBalance, { blockhash }] = await Promise.all([
    connection.getMinimumBalanceForRentExemption(StakeProgram.space),
    connection.getLatestBlockhash(),
  ]);
  return { minimumBalance, blockhash };
});

export class BaseSolana extends BaseChain {
  supportsMemos = false;

  public connection?: Connection;
  public signer?: SolanaSigner;
  protected recentBlockhash?: string;
  protected minimumBalance?: number;
  protected node?: Promise<bip32.BIP32Interface>;

  constructor(token: Token, services: ChainServices, protected rpcUri: string) {
    super(token, services);
  }

  private async setupConnection() {
    if (this.connection) {
      return;
    }

    this.connection = getConnection(this.rpcUri);
    const { minimumBalance, blockhash } = await getConfig(this.connection!);
    this.minimumBalance = minimumBalance;
    this.recentBlockhash = blockhash;
  }

  getRpcUri(): string {
    return this.rpcUri;
  }

  async getTransactionPrice(signatureCount?: number) {
    const lamportsPerSignature = await new Transaction({
      recentBlockhash: this.recentBlockhash,
      feePayer: new PublicKey('CcaHc2L43ZWjwCHART3oZoJvHLAe9hzT2DJNUpBzoTN1'),
    })
      .add(
        SystemProgram.transfer({
          fromPubkey: new PublicKey(
            'CcaHc2L43ZWjwCHART3oZoJvHLAe9hzT2DJNUpBzoTN1',
          ),
          toPubkey: new PublicKey(
            // random address fetching lamportPerSignature
            '********************************************',
          ),
          lamports: parseInt(this.toWei('10')), // same random value for fetching lamportPerSignature
        }),
      )
      .getEstimatedFee(this.connection!);
    return fromWei(
      (signatureCount! * lamportsPerSignature).toFixed(),
      baseConfig.decimals,
    );
  }

  async setupAddress() {
    this.signer = await getSolanaWallet(this.walletOptions!);
    this.address = (await this.signer!.getPublicKey()).toBase58();
  }

  async setupForReading() {
    await this.setupConnection();
  }

  async setupForWriting() {
    await this.setupConnection();
  }

  isValidAddress(address: string) {
    try {
      new PublicKey(address);
      return true;
    } catch {
      return false;
    }
  }

  checksumAddress(address: string) {
    return address;
  }

  async sendTransactions(
    transactions: { tx: Transaction; signers: SolanaSigner[] }[],
  ): Promise<RunnableTxSuccess | RunnableTxError> {
    let txid = '';
    this.log('sending', transactions.length, 'transactions');
    const recentBlockhash = await this.connection!.getRecentBlockhash();

    for (const { tx, signers } of transactions) {
      try {
        tx.feePayer = await this.signer!.getPublicKey();
        tx.recentBlockhash = recentBlockhash.blockhash;

        for (const signer of [this.signer!, ...signers]) {
          await signer.signTransaction(tx);
        }

        txid = await sendAndConfirmRawTransaction(
          this.connection!,
          tx.serialize({
            verifySignatures: false,
            requireAllSignatures: false,
          }),
          {
            skipPreflight: true,
            commitment: 'processed',
          },
        );
        this.log('got txid', txid);
      } catch (e: any) {
        this.log('unable to send transaction', e.message);
        return {
          success: false,
          links: txid !== '' ? [this.withExplorerUri('tx', txid)] : [],
          rawLog: e.message,
          type: RunnableTxErrorType.Unknown,
        };
      }
    }
    return {
      success: true,
      links: [this.withExplorerUri('tx', txid)],
    };
  }

  async sendTransaction(
    tx: Transaction,
  ): Promise<RunnableTxSuccess | RunnableTxError> {
    return await this.sendTransactions([{ tx, signers: [] }]);
  }

  getOrCreateAssociatedTokenAccount(token: string, to: string) {
    return getOrCreateAssociatedTokenAccount(
      this.connection!,
      this.signer!,
      new PublicKey(token),
      new PublicKey(to),
      true,
    );
  }

  public async getSplTokenAddress(tokenAddress: string) {
    return getAssociatedTokenAddress(
      new PublicKey(this.token.address!),
      new PublicKey(tokenAddress),
      true,
      TOKEN_PROGRAM_ID,
      ASSOCIATED_TOKEN_PROGRAM_ID,
    );
  }

  getNFT = async () => new SolanaNFTWrapper(this);

  broadcast = async (tx: string): Promise<RunnableTxResponse> => {
    try {
      const result = await sendAndConfirmRawTransaction(
        this.connection!,
        Buffer.from(base58.decode(tx)),
        { commitment: 'processed', skipPreflight: true },
      );
      return {
        success: true,
        links: [this.withExplorerUri('tx', result)],
      };
    } catch (error) {
      return {
        success: false,
        links: [],
        rawLog: (error as any).message as string,
        type: RunnableTxErrorType.Unknown,
      };
    }
  };

  sign = async (unsignedTx: string): Promise<string> => {
    const tx = Transaction.from(Buffer.from(unsignedTx, 'base64'));
    const signedTx = await this.signer!.signTransaction(tx);
    return base58.encode(signedTx.serialize());
  };

  /**
   * Confirming transactions kind of sucks in Solana because you need
   * the latestBlockHash that the transaction was submitted with. Given
   * we just have the TX ID, we're doing the best we can
   */
  getTransactionStatus = async (txid: string): Promise<boolean> => {
    let attempts = 0;

    while (attempts < 5) {
      const tx = await this.connection!.getTransaction(txid);
      if (tx) {
        return !tx.meta?.err;
      }

      await new Promise((resolve) => setTimeout(resolve, 1000));
      attempts++;
    }
    return false;
  };
}
