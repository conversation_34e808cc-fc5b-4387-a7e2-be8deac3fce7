import { Networks } from '@steakwallet/types';

function mintscan(prefix: string, type: 'address' | 'tx', thing: string) {
  const param = type === 'tx' ? 'txs' : 'account';
  return `https://www.mintscan.io/${prefix}/${param}/${thing}`;
}

const explorerLinks: {
  [x in Networks]: (type: 'address' | 'tx', thing: string) => string;
} = {
  [Networks.Akash]: (type, thing) => mintscan('akash', type, thing),
  [Networks.Arbitrum]: (type, thing) => `https://arbiscan.io/${type}/${thing}`,
  [Networks.AvalancheC]: (type, thing) =>
    `https://snowtrace.io/${type}/${thing}`,
  [Networks.Binance]: (type, thing) => `https://bscscan.com/${type}/${thing}`,
  [Networks.Celo]: (type, thing) => `https://celoscan.io/${type}/${thing}`,
  [Networks.Cosmos]: (type, thing) => mintscan('cosmos', type, thing),
  [Networks.Ethereum]: (type, thing) => `https://etherscan.io/${type}/${thing}`,
  [Networks.EthereumGoerli]: (type, thing) =>
    `https://goerli.etherscan.io/${type}/${thing}`,
  [Networks.Fantom]: (type, thing) => `https://ftmscan.com/${type}/${thing}`,
  [Networks.Gnosis]: (type, thing) =>
    `https://blockscout.com/xdai/mainnet/${type}/${thing}`,
  // Harmony staking transactions need to use the `staking-tx` prefix,
  // so we don't use this function for them
  [Networks.Harmony]: (type, thing) =>
    `https://explorer.harmony.one/${type}/${thing}`,
  [Networks.Juno]: (type, thing) => mintscan('juno', type, thing),
  [Networks.Kava]: (type, thing) => mintscan('kava', type, thing),
  [Networks.Moonriver]: (type, thing) =>
    `https://moonriver.moonscan.io/${type}/${thing}`,
  [Networks.Near]: (type, thing) =>
    `https://explorer.mainnet.near.org/${
      type === 'tx' ? 'transactions' : 'accounts'
    }/${thing}`,
  [Networks.Optimism]: (type, thing) =>
    `https://optimistic.etherscan.io/${type}/${thing}`,
  [Networks.Osmosis]: (type, thing) => mintscan('osmosis', type, thing),
  [Networks.OKC]: (type, thing) =>
    `https://www.oklink.com/en/okc/${type}/${thing}`,
  [Networks.Persistence]: (type, thing) => mintscan('persistence', type, thing),
  [Networks.Polygon]: (type, thing) =>
    `https://polygonscan.com/${type}/${thing}`,
  [Networks.Solana]: (type, thing) =>
    `https://explorer.solana.com/${type}/${thing}`,
  [Networks.Stargaze]: (type, thing) => mintscan('stargaze', type, thing),
  [Networks.Terra]: (type, thing) =>
    `https://finder.terra.money/classic/${type}/${thing}`,
  [Networks.Tezos]: (type, thing) => `https://tzstats.com/${thing}`,
  [Networks.zkSync]: (type, thing) =>
    `https://explorer.zksync.io/${type}/${thing}`,
  [Networks.Bitcoin]: (type, thing) => `https://mempool.space/${type}/${thing}`,
  [Networks.Tron]: (type, thing) =>
    `https://tronscan.org/#/${
      type === 'tx' ? 'transaction' : 'address'
    }/${thing}`,
};

export function withExplorerUri(
  network: Networks,
  type: 'address' | 'tx',
  thing: string,
) {
  return explorerLinks[network](type, thing);
}
