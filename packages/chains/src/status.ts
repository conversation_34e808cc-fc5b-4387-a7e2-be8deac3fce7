import {
  CosmosNetworks,
  ErrorType,
  EvmNetworks,
  Networks,
  ErrorCode,
  OmniError,
} from '@steakwallet/types';
import { TransactionStatusRequest } from './common';

import * as evm from './evm';
import * as cosmos from './cosmos';
import * as near from './near';
import * as solana from './solana';
import * as tezos from './tezos';
import * as bitcoin from './bitcoin';
import * as tron from './tron';

const status: { [n in Networks]?: TransactionStatusRequest } = {
  ...Object.values(EvmNetworks).reduce(
    (accum, n) => ({ ...accum, [n]: evm.getTransactionStatus }),
    {},
  ),
  ...Object.values(CosmosNetworks).reduce(
    (accum, n) => ({ ...accum, [n]: cosmos.getTransactionStatus }),
    {},
  ),
  [Networks.Solana]: solana.getTransactionStatus,
  [Networks.Tezos]: tezos.getTransactionStatus,
  [Networks.Near]: near.getTransactionStatus,
  [Networks.Bitcoin]: bitcoin.getTransactionStatus,
  [Networks.Tron]: tron.getTransactionStatus,
};

export const getTransactionStatus = (network: Networks, txHash: string) => {
  const statusFn = status[network];
  if (!statusFn) {
    throw new OmniError(
      ErrorType.NOT_IMPLEMENTED,
      ErrorCode.NOT_IMPLEMENTED,
      'Missing status implementation for network',
    );
  }

  return statusFn(network, txHash);
};
