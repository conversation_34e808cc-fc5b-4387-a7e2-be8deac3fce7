import { Networks, rpcUris } from '@steakwallet/types';
import { Contract, ethers, utils } from 'ethers';
import { ERC897 } from '../../ethereum/abis';

export async function getProxy(network: Networks, contract: string) {
  const provider = new ethers.providers.JsonRpcProvider(rpcUris[network][0]);
  try {
    const implementation = await provider.getStorageAt(
      contract,
      // https://eips.ethereum.org/EIPS/eip-1967
      // bytes32(uint256(keccak256('eip1967.proxy.implementation')) - 1)
      '0x360894a13ba1a3210667c828492db98dca3e2076cc3735a920a3ca505d382bbc',
    );

    const address = utils
      .hexlify(utils.stripZeros(implementation))
      .toLowerCase();

    if (address === '0x') {
      throw new Error();
    }

    return address;
  } catch {
    const SC = new Contract(
      contract,
      ERC897 as ethers.ContractInterface,
      provider,
    );
    return (await SC.implementation()).toLowerCase();
  }
}
