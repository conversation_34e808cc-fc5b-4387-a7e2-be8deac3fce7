import { fromBech32 } from '@harmony-js/crypto';
import { ethers } from 'ethers';

import { baseConfig } from '.';

import { AvailableBalance, Balances, ChainServices } from '../common';
import { BalanceTypes, GasMode, GasPriceStep } from '@steakwallet/types';
import { EvmBaseChain, EvmOptions } from '../evm';
import { isOneAddress } from './common';

export class One extends EvmBaseChain {
  constructor(options: EvmOptions, services: ChainServices) {
    super(baseConfig, options, services);
  }

  async setupAddress() {
    await super.setupAddress();

    if (isOneAddress(this.address!)) {
      this.address = fromBech32(this.address!);
    }
  }

  isValidAddress = (a: string) => {
    return ethers.utils.isAddress(a) || isOneAddress(a);
  };

  async getBalancesForAddress(
    address: string,
    additionalAddresses?: string[],
    options?: any,
  ): Promise<{
    balances: Balances | AvailableBalance;
    pricePerShare?: string;
  }> {
    const available = await this.provider!.getBalance(address);
    return {
      balances: {
        [BalanceTypes.Available]: { amount: this.fromWei(available) },
      } as AvailableBalance,
    };
  }

  async send(
    from: string,
    to: string,
    amount: string,
    comment?: string,
    gasPriceStep: GasPriceStep = { gasMode: GasMode.average },
  ) {
    this.log('send', to, amount, comment, gasPriceStep);

    const toAddress = isOneAddress(to) ? fromBech32(to) : to;
    return super.send(from, toAddress, amount, comment, gasPriceStep);
  }
}
