import {
  AxelarAssetTransfer,
  AxelarQueryAPI,
  Environment,
} from '@axelar-network/axelarjs-sdk';
import { BigNumber, Contract, ethers } from 'ethers';
import { v4 as uuidv4 } from 'uuid';
import { Networks, rpcUris } from '@steakwallet/types';

import { IAxelarGateway } from './abis/IAxelarGateway';
import {
  AxelarCosmosNetworks,
  AxelarEvmNetworks,
  channelIds,
  gatewayContractAddress,
} from './constant';

import { isEvmChain } from '../../common';
import { ERC20 as ERC20Abi } from '../../ethereum/abis/ERC20';
import { ERC20_APPROVAL_GAS_LIMIT } from '../constants';
import { SwapWrapper } from '../interface';
import {
  InternalSwapRouteRequest,
  SwapProvider,
  SwapRouteDetails,
  ExecuteRouteRequest,
  SwapTools,
  ExecuteRouteResponse,
  TxDetails,
  BridgeStatusRequest,
  BridgeStatus,
} from '../types';

const OMNI_SWAP_FEE_BPS = 0;

const allSupportNetworks = [...AxelarCosmosNetworks, ...AxelarEvmNetworks];

//[TODO]{Never use this unless squid never deliver the cosmos GMP}
// Mostly Axelar should be scrapped off as, we will be only
export class AxelarSwap implements SwapWrapper {
  transferClient: AxelarAssetTransfer;
  queryClient: AxelarQueryAPI;
  provider = SwapProvider.Axelar;

  constructor() {
    this.transferClient = new AxelarAssetTransfer({
      environment: Environment.MAINNET,
    });
    this.queryClient = new AxelarQueryAPI({ environment: Environment.MAINNET });
  }

  async getTransferFee(
    srcChain: string,
    destChain: string,
    symbol: string,
    amount: string,
  ) {
    const denom = (await this.queryClient.getDenomFromSymbol(
      symbol,
      srcChain,
    )) as string;

    const feeResponse = await this.queryClient.getTransferFee(
      srcChain,
      destChain,
      denom,
      parseInt(amount),
    );

    return feeResponse!.fee!.amount;
  }

  async getQuotations(
    data: InternalSwapRouteRequest,
  ): Promise<SwapRouteDetails[]> {
    const { fromToken, toToken, fromAmount } = data;
    // Two ways make a swap tokens work in Axelar network,
    // https://docs.axelar.dev/dev/build/tokens

    if (
      !allSupportNetworks.includes(fromToken.network as any) ||
      !allSupportNetworks.includes(toToken.network as any)
    ) {
      return [];
    }

    if (fromToken.network in AxelarEvmNetworks && !fromToken.address) {
      return [];
    }

    const transferFee = await this.getTransferFee(
      fromToken.network,
      toToken.network,
      fromToken.symbol,
      fromAmount,
    );

    return [
      {
        provider: SwapProvider.Axelar,
        routeId: uuidv4(),
        fromAmount,
        toAmount: BigNumber.from(fromAmount).sub(transferFee).toString(),
        fromToken: data.fromToken,
        toToken: data.toToken,
        steps: 1,
        tools: [
          {
            name: 'axelar',
            image:
              'https://github.com/axelarnetwork/axelarscan-docs/blob/94cd1f22c167100278a8b6c1f778fbb1afff1f17/public/logo/logo.png',
          },
        ],
        minDuration: '500',
        fee: {
          feeToken: data.fromToken,
          omniFeeBps: OMNI_SWAP_FEE_BPS.toString(),
          amount: transferFee.toString(),
        },
        routeObject: data,
        slippage: '1',
        gas: [
          {
            gasToSpend: BigNumber.from(transferFee).toString(),
            gasToken: fromToken,
          },
        ],
        tag: [],
      } as SwapRouteDetails,
    ];
  }

  async getTools(): Promise<SwapTools> {
    return {
      bridges: [
        {
          name: 'axelar',
          logoURI:
            'https://github.com/axelarnetwork/axelarscan-docs/blob/94cd1f22c167100278a8b6c1f778fbb1afff1f17/public/logo/logo.png',
        },
      ],
      exchanges: [],
    };
  }

  async executeRoute(
    requestDto: ExecuteRouteRequest,
    mnemonic?: string,
  ): Promise<ExecuteRouteResponse | null> {
    const { fromToken, toToken, fromAmount } = requestDto.route;
    const toAddress = requestDto.route.routeObject.toAddress;
    const unsignedTxs: TxDetails[] = [];
    if (
      isEvmChain(fromToken.network) &&
      fromToken.network in AxelarEvmNetworks
    ) {
      // Less frequently used mechanism, may be we will also avoid this send token method to transfer

      const srcProvider = new ethers.providers.JsonRpcProvider(
        rpcUris[fromToken.network][0],
      );
      const srcGatewayContract = new Contract(
        //@ts-ignore
        gatewayContractAddress[fromToken.network],
        IAxelarGateway,
        srcProvider,
      );

      // calling the sendToken on the gateway.
      // Get token address from the gateway contract for the src chain
      const srcTokenAddress = await srcGatewayContract.tokenAddresses(
        fromToken.symbol,
      );

      const srcErc20 = new Contract(srcTokenAddress, ERC20Abi, srcProvider);

      const allowance = await srcErc20!.allowance(
        requestDto.address,
        srcGatewayContract.address,
      );

      // ++++++++++++++++ PRE-STEP Tx ++++++++++++++++++++++ //
      // Check if action's start token is native or ERC20
      if (allowance.lt(fromAmount)) {
        // Approve the token for the amount to be sent
        const { gasPrice, ...transactionRequest } =
          await srcErc20.populateTransaction.approve(
            srcGatewayContract.address,
            ethers.utils.parseUnits(
              BigNumber.from(fromAmount).toString(),
              fromToken.decimals,
            ),
          );

        unsignedTxs.push({
          txData: JSON.stringify({
            ...transactionRequest,
            ...{ gasLimit: ERC20_APPROVAL_GAS_LIMIT, value: '0x00' },
          }),
          approvalTx: true,
        });
      }

      // Send the token
      const { gasPrice, ...transactionRequest } =
        await srcGatewayContract.populateTransaction.sendToken(
          toToken.network,
          requestDto.route.routeObject.toAddress,
          toToken.symbol,
          ethers.utils.parseUnits(
            BigNumber.from(fromAmount).toString(),
            toToken.decimals,
          ),
        );

      unsignedTxs.push({
        txData: JSON.stringify({
          ...transactionRequest,
          value: transactionRequest.value ?? '0x00',
        }),
        approvalTx: false,
      });

      return {
        network: fromToken.network,
        unsignedTransactions: unsignedTxs,
        finalTx: true,
        changedRouteId: undefined,
      };
    }

    if (
      allSupportNetworks.includes(fromToken.network as any) &&
      allSupportNetworks.includes(toToken.network as any)
    ) {
      const denom = (await this.queryClient.getDenomFromSymbol(
        fromToken.symbol,
        fromToken.network,
      )) as string;

      const assetConfig = await this.queryClient.getAssetConfigFromDenom(
        denom,
        fromToken.network,
      );

      const depositAddress = await this.transferClient.getDepositAddress(
        fromToken.network, // source chain
        toToken.network, // destination chain
        toAddress, // destination address
        denom, // denom of asset. See note (2) below
      );

      if (fromToken.network in AxelarEvmNetworks) {
        if (fromToken.address) {
          const srcProvider = new ethers.providers.JsonRpcProvider(
            rpcUris[fromToken.network][0],
          );
          const srcErc20 = new Contract(
            fromToken.address,
            ERC20Abi,
            srcProvider,
          );
          // Send the token
          const { gasPrice, ...transactionRequest } =
            await srcErc20.populateTransaction.transfer(
              depositAddress,
              ethers.utils.parseUnits(
                BigNumber.from(fromAmount).toString(),
                toToken.decimals,
              ),
            );

          unsignedTxs.push({
            txData: JSON.stringify({
              ...transactionRequest,
              value: transactionRequest.value ?? '0x00',
            }),
            approvalTx: false,
          });
        }
      }

      unsignedTxs.push({
        txData: JSON.stringify({
          senderAddress: requestDto.address,
          recipientAddress: toAddress,
          transferAmount: {
            denom: assetConfig?.ibcDenom as string,
            amount: BigNumber.from(10)
              .pow(assetConfig?.decimals as number)
              .mul(fromAmount)
              .toString(),
          },
          sourcePort: 'transfer',
          // @ts-ignore
          sourceChannel: channelIds[fromToken.network],
        }),
        approvalTx: false,
      });

      return {
        network: fromToken.network,
        unsignedTransactions: unsignedTxs,
        finalTx: true,
        changedRouteId: undefined,
      };
    }

    return null;
  }
  async getBridgeStatus(request: BridgeStatusRequest) {
    return BridgeStatus.SUCCESS;
  }
}
