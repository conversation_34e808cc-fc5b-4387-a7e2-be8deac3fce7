import {
  InternalSwapRouteRequest,
  SwapRouteDetails,
  ExecuteRouteRequest,
  SwapTools,
  ExecuteRouteResponse,
  BridgeStatus,
  BridgeStatusRequest,
} from './types';

export abstract class SwapWrapper {
  getTools: () => Promise<SwapTools>;
  getQuotations: (
    data: InternalSwapRouteRequest,
  ) => Promise<SwapRouteDetails[]>;
  executeRoute: (
    request: ExecuteRouteRequest,
    mnemonic?: string,
  ) => Promise<ExecuteRouteResponse | null>;

  getBridgeStatus: (request: BridgeStatusRequest) => Promise<BridgeStatus>;
}
