import LIFI, { Step } from '@lifi/sdk';
import axios from 'axios';
import { constants, BigNumber as eBigNumber } from 'ethers';
import { BigNumber } from 'bignumber.js';
import _ from 'lodash';

import { LifiError } from './utils';

import { isEvm<PERSON>hain } from '../../common';
import {
  getChainIdByNetwork,
  getNetworkByChainId,
  getTokenByTicker,
} from '../../getters';
import {
  ERC20_APPROVAL_GAS_LIMIT,
  multicallAddresses,
  rpcs,
} from '../constants';
import { SwapWrapper } from '../interface';
import {
  InternalSwapRouteRequest,
  SwapProvider,
  SwapRouteDetails,
  ExecuteRouteRequest,
  SwapTools,
  ExecuteRouteResponse,
  TxDetails,
  BridgeStatus,
  BridgeStatusRequest,
} from '../types';
import {
  fromBPStoDecimals,
  fromDecimalsToBps,
  getApprovedAmount,
  prettifyName,
  setApproval,
} from '../utils';

const API_URL = 'https://li.quest/v1/';

const BASE_TOKEN_ADDRESS = '******************************************';

const OMNI_SWAP_FEE_BPS = 50;

const LIFI_INTEGRATOR_STRING = 'omni.app';

const log = (...args: any[]) => console.log('[lifi]', ...args);

export class LiFiSwap implements SwapWrapper {
  lifi: LIFI;
  provider = SwapProvider.LiFi;

  constructor() {
    this.lifi = new LIFI({ rpcs, multicallAddresses });
  }

  async getQuotations(
    data: InternalSwapRouteRequest,
  ): Promise<SwapRouteDetails[]> {
    const { options, ...routeData } = data;

    if (
      !isEvmChain(data.fromToken.network) ||
      !isEvmChain(data.fromToken.network)
    ) {
      return [];
    }

    const fromChainId = parseInt(getChainIdByNetwork(data.fromToken.network)!);
    const toChainId = parseInt(getChainIdByNetwork(data.toToken.network)!);

    const lifiResponse = await this.lifi.getRoutes({
      fromAmount: routeData.fromAmount,
      fromAddress: routeData.fromAddress,
      toAddress: routeData.toAddress,
      fromChainId,
      toChainId,
      fromTokenAddress: data.fromToken.address ?? BASE_TOKEN_ADDRESS,
      toTokenAddress: data.toToken.address ?? BASE_TOKEN_ADDRESS,
      options: {
        order: options.priority,
        slippage: parseFloat(fromBPStoDecimals(options.slippage)),
        integrator: LIFI_INTEGRATOR_STRING,
        fee: parseFloat(fromBPStoDecimals(OMNI_SWAP_FEE_BPS.toString())),
      },
    });

    return lifiResponse.routes.map((route) => {
      const omniFeeDeduction = new BigNumber(
        fromBPStoDecimals(OMNI_SWAP_FEE_BPS.toString()),
      )
        .multipliedBy(route.toAmount)
        .toFixed(0, BigNumber.ROUND_DOWN)
        .toString();
      return {
        routeId: route.id,
        provider: this.provider,
        fromAmount: route.fromAmount,
        toAmount: new BigNumber(route.toAmountMin)
          .minus(omniFeeDeduction)
          .toString(),
        fromToken: data.fromToken,
        toToken: data.toToken,
        gas: route.steps.flatMap((eachStep) =>
          eachStep.estimate.gasCosts?.flatMap((eachCost) => {
            return {
              gasToSpend: eachCost.amount,
              gasToken: getTokenByTicker(
                getNetworkByChainId(eachCost.token.chainId.toString()),
                eachCost.token.symbol,
              ),
            };
          }),
        ),
        steps: route.steps.length,
        tools: route.steps.map((step) => ({
          name: prettifyName(step.toolDetails.name),
          image: step.toolDetails.logoURI,
        })),
        minDuration: route.steps
          .reduce((sum, e) => e.estimate.executionDuration + sum, 60)
          .toString(),
        fee: {
          feeToken: data.toToken,
          amount: omniFeeDeduction,
          omniFeeBps: OMNI_SWAP_FEE_BPS.toString(),
        },
        slippage: fromDecimalsToBps(
          route.steps.reduce((sum, e) => e.action.slippage + sum, 0).toString(),
        ),
        routeObject: route,
        tag: [],
      } as SwapRouteDetails;
    });
  }

  async getBridgeStatus(request: BridgeStatusRequest): Promise<BridgeStatus> {
    const response = await this.lifi.getStatus(
      Object.assign(request, {
        fromChain: request.fromChainId,
        toChain: request.toChainId,
      }),
    );
    const bridgeStatus = response.status;
    return bridgeStatus == 'DONE'
      ? BridgeStatus.SUCCESS
      : bridgeStatus == 'FAILED'
      ? BridgeStatus.FAILED
      : BridgeStatus.PENDING;
  }

  async getTools(): Promise<SwapTools> {
    const tools = await this.lifi.getTools();

    return {
      bridges: [
        ...tools.bridges!.map((e) => {
          return { name: e.name, logoURI: e.logoURI };
        }),
      ].filter((v, i, a) => a.indexOf(v) === i),
      exchanges: [
        ...tools.exchanges!.map((e) => {
          return { name: e.name, logoURI: e.logoURI };
        }),
      ].filter((v, i, a) => a.indexOf(v) === i),
    };
  }

  async executeRoute(
    requestDto: ExecuteRouteRequest,
    mnemonic?: string,
  ): Promise<ExecuteRouteResponse | null> {
    const { route, address, index } = requestDto;
    const unsignedTxs: TxDetails[] = [];

    if (index >= route.steps) {
      log(
        'unable to request transaction as the index exceeds the number of steps in the route',
      );
      return null;
    }

    const initialTx = route.routeObject.steps[index];

    // ++++++++++++++++ PRE-STEP Tx ++++++++++++++++++++++ //
    // Check if action's start token is native or ERC20
    if (initialTx.action.fromToken.address !== constants.AddressZero) {
      const approved = await getApprovedAmount({
        network: getNetworkByChainId(initialTx.action!.fromChainId!.toString()),
        tokenAddress: initialTx.action.fromToken.address,
        contractAddress: initialTx.estimate.approvalAddress,
        userAddress: address!,
      });

      // Check if approval
      if (approved.lt(initialTx.action.fromAmount ?? 0)) {
        const { gasPrice, ...transactionRequest } = await setApproval({
          network: getNetworkByChainId(
            initialTx.action!.fromChainId!.toString(),
          ),
          tokenAddress: initialTx.action.fromToken.address,
          contractAddress: initialTx.estimate.approvalAddress,
          amount: constants.MaxUint256.toString(),
        });
        unsignedTxs.push({
          txData: JSON.stringify({
            ...transactionRequest,
            ...{ gasLimit: ERC20_APPROVAL_GAS_LIMIT, value: '0x00' },
          }),
          approvalTx: true,
        });
      }
    }

    // ++++++++++++++++ STEP Tx ++++++++++++++++++++++ //
    // fetch the step transaction
    const fetchStepTx = await this.getStepTransaction(initialTx);
    const { gasPrice, value, ...transactionRequest } =
      fetchStepTx.transactionRequest!;
    const gasLimit = fetchStepTx.estimate.gasCosts![0].limit;

    unsignedTxs.push({
      txData: JSON.stringify({
        ...transactionRequest,
        ...(gasLimit ? { gasLimit } : {}),
        value: eBigNumber.from(value).toHexString(),
      }),
      approvalTx: false,
    });
    return {
      network: getNetworkByChainId(initialTx.action!.fromChainId!.toString()),
      unsignedTransactions: unsignedTxs,
      finalTx: index + 1 >= route.steps,
    };
  }

  async getStepTransaction(step: Step) {
    try {
      const result = await axios.post<Step>(
        API_URL + 'advanced/stepTransaction',
        step,
      );
      return result.data;
    } catch (e) {
      if (axios.isAxiosError(e)) {
        throw new LifiError(
          (e as any).response?.status,
          (e as any).response?.data?.message,
        );
      } else {
        throw new LifiError(500, (e as any).msg);
      }
    }
  }
}
