import { Connection, PublicKey } from '@solana/web3.js';
import BigNumber from 'bignumber.js';
import { isPresent } from 'ts-is-present';

import { getSolanaWallet, ImportableWallets } from '@steakwallet/signers';
import { Networks, rpcUris } from '@steakwallet/types';

import { getTokenByTicker } from '../../getters';
import { getOrCreateAssociatedTokenAccount } from '../../solana/utils';
import { SwapWrapper } from '../interface';
import {
  ExecuteRouteRequest,
  ExecuteRouteResponse,
  InternalSwapRouteRequest,
  SwapProvider,
  SwapRouteDetails,
  SwapTools,
  TxDetails,
  BridgeStatusRequest,
  BridgeStatus,
} from '../types';
import { fromBPStoDecimals, fromBPStoPercent } from '../utils';
import {
  isJupiterQuoteError,
  JupiterRoutesResult,
  JupiterRouteSuccess,
} from './types';

const SOL_MINT_ADDRESS = 'So11111111111111111111111111111111111111112';

const OMNI_SWAP_FEE_BPS = 50;

const log = (...args: any[]) => console.log('[jupiter]', ...args);

export class JupiterSwap implements SwapWrapper {
  provider = SwapProvider.Jupiter;

  constructor() {}

  fetchFeeAccount = async (
    mnemonic: string,
    tokenAddress: string | undefined,
  ): Promise<string | undefined> => {
    const feeKeyPair = await getSolanaWallet({
      mnemonic, // need to fill the fee receiving steakwallet mnemonic from env or any other way
      index: 0,
      walletType: ImportableWallets.Steakwallet,
    });

    if (!tokenAddress) {
      return (await feeKeyPair.getPublicKey()).toString();
    }
    const connection = new Connection(rpcUris[Networks.Solana][0], 'confirmed');

    const feePublicKey = await getOrCreateAssociatedTokenAccount(
      connection,
      feeKeyPair,
      new PublicKey(tokenAddress),
      await feeKeyPair.getPublicKey(),
    );

    return feePublicKey.address.toString();
  };

  async executeRoute(
    requestDto: ExecuteRouteRequest,
    mnemonic?: string,
  ): Promise<ExecuteRouteResponse | null> {
    const unsignedTransactions = await this.getRouteTx(
      requestDto.address,
      requestDto.route,
      await this.fetchFeeAccount(mnemonic!, requestDto.route.toToken.address),
    );
    return {
      network: Networks.Solana,
      unsignedTransactions: unsignedTransactions.map(
        (eachTx) =>
          Object.assign({ txData: eachTx }, { approvalTx: false }) as TxDetails,
      ),
      finalTx: true,
    };
  }

  async getBridgeStatus(request: BridgeStatusRequest) {
    return BridgeStatus.SUCCESS;
  }

  async getQuotations({
    fromToken,
    toToken,
    fromAmount,
    options,
  }: InternalSwapRouteRequest): Promise<SwapRouteDetails[]> {
    if (
      fromToken.network !== Networks.Solana ||
      toToken.network !== Networks.Solana
    ) {
      return [];
    }

    const request = `https://quote-api.jup.ag/v1/quote?inputMint=${
      fromToken.address ?? SOL_MINT_ADDRESS
    }&outputMint=${
      toToken.address ?? SOL_MINT_ADDRESS
    }&amount=${fromAmount}&slippage=${fromBPStoPercent(
      options.slippage,
    )}&feeBps=${OMNI_SWAP_FEE_BPS}`;
    const response = await fetch(request);

    if (response.status !== 200) {
      log('invalid quote response status:', response.status);
      return [];
    }

    const result: JupiterRoutesResult = await response.json();
    if (isJupiterQuoteError(result)) {
      log('invalid quote response', result.error);
      return [];
    }

    return result.data.map((d) => {
      const dexes = d.marketInfos.map(({ label }) => ({
        name: label,
        image: `https://raw.githubusercontent.com/steakwallet/assets/master/swaps/${label
          .split(' ')
          .join('_')
          .toLowerCase()}.png`,
      }));
      const result = {
        routeId: dexes.map((e) => e.name).join('-'),
        provider: this.provider,
        fromAmount: d.inAmount.toString(),
        toAmount: d.outAmount.toString(),
        fromToken,
        toToken,
        gas: [
          {
            gasToSpend: '0',
            gasToken: getTokenByTicker(Networks.Solana, 'SOL'),
          },
        ],
        steps: 1,
        tools: dexes,
        minDuration: '60',
        fee: {
          feeToken: toToken,
          amount: new BigNumber(fromBPStoDecimals(OMNI_SWAP_FEE_BPS.toString()))
            .multipliedBy(d.outAmount)
            .toFixed(0, BigNumber.ROUND_DOWN)
            .toString(),
          omniFeeBps: OMNI_SWAP_FEE_BPS.toString(),
        },
        slippage: options.slippage,
        routeObject: d,
        tag: [],
      } as SwapRouteDetails;
      return result;
    });
  }

  async getRouteTx(
    user: string,
    route: SwapRouteDetails,
    feeAccount: string | undefined,
  ) {
    // get serialized transactions for the swap
    const response = await fetch('https://quote-api.jup.ag/v1/swap', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        // route from /quote apiF
        route: route.routeObject,
        userPublicKey: user,
        // auto wrap and unwrap SOL. default is true
        wrapUnwrapSOL: true,
        // feeAccount is optional. Use if you want to charge a fee.  feeBps must have been passed in /quote API.
        // This is the ATA account for the output token where the fee will be sent to. If you are swapping from SOL->USDC then this would be the USDC ATA you want to collect the fee.
        feeAccount,
      }),
    });

    if (response.status !== 200) {
      log('unable to pull route, status code', response.status);
      return [];
    }

    const result: JupiterRouteSuccess = await response.json();
    console.log(result);
    const { setupTransaction, swapTransaction, cleanupTransaction } = result;

    return [setupTransaction, swapTransaction, cleanupTransaction].filter(
      isPresent,
    );
  }

  async getTools(): Promise<SwapTools> {
    return {
      bridges: [],
      exchanges: [],
    };
  }
}
