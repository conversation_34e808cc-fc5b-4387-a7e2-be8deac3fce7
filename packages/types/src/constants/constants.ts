import { Networks, BalanceTypes } from '../enums';

export const MAX_DATE = new Date('2050-01-01');

export const apiUrl = 'https://api-2.steakwallet.fi/v1';

export const testSeed = process.env.TEST_SEED as string;

export const figmentApiKey = 'f7c6e747eeafad46c73dc27c615c56f4';

export const TIMEOUT_ERROR = 'TIMEOUT_ERROR';

// based on SLIP-0044.md here https://github.com/satoshilabs/slips/blob/master/slip-0044.md
export const CoinType: { [x in Networks]?: number } = {
  // [Networks.Akash]: 0,
  [Networks.Arbitrum]: 9001,
  [Networks.AvalancheC]: 9000,
  [Networks.Binance]: 714,
  [Networks.Celo]: 52752,
  [Networks.Cosmos]: 118,
  // [Networks.Ethereum]: 0,
  [Networks.Fantom]: 1007,
  [Networks.Harmony]: 1023,
  // [Networks.Juno]: 0,
  [Networks.Kava]: 459,
  // [Networks.Kusama]: 434,
  [Networks.Moonriver]: 1285,
  [Networks.Near]: 397,
  [Networks.Optimism]: 614,
  [Networks.Polygon]: 966,
  [Networks.Solana]: 501,
  [Networks.Terra]: 330,
  [Networks.Tezos]: 1729,
  [Networks.Gnosis]: 700,
};

export const MultiCallAddresses: { [x in Networks]?: string } = {
  [Networks.AvalancheC]: '******************************************',
  [Networks.Ethereum]: '******************************************',
  [Networks.Fantom]: '******************************************',
};

export const ZERO_BALANCE = { [BalanceTypes.Available]: { amount: '0' } };

export const covalentSupportedChains = [
  Networks.Arbitrum,
  Networks.AvalancheC,
  Networks.Binance,
  Networks.Ethereum,
  Networks.Fantom,
  Networks.Harmony,
  Networks.Moonriver,
  Networks.Polygon,
  Networks.Solana,
];
