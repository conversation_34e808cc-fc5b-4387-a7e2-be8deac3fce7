import { Networks } from '../enums';
import { fetchSecrets } from '@omni/common';

// Awesome list for rpc providers:
// https://github.com/arddluma/awesome-list-rpc-nodes-providers
// infura: Provides as well Polygon, Arbitrum, Optimism after providing a CC
// cloudflare: It seems that we need to register our website in order to access paid subscriptions
// blast Avax/Eth: Not sure about the purpose of each rpc
// 1rpc: Subscription to 1rpc+ is on waitlist
// nowNode: Free trial ending on 02/03/2023

const {
  alchemy,
  quicknode,
  infura,
  getBlock,
  onFinality,
  groveCity,
  chainstack,
  nowNode,
  chainBase,
} = await fetchSecrets([
  'alchemy',
  'quicknode',
  'infura',
  'getBlock',
  'onFinality',
  'groveCity',
  'chainstack',
  'nowNode',
  'chainBase',
]);

export const providers = {
  [Networks.Ethereum]: {
    alchemy: `https://eth-mainnet.g.alchemy.com/v2/${alchemy}`,
    quickNode: `https://ultra-thrumming-night.quiknode.pro/${quicknode}`,
  },
  [Networks.EthereumGoerli]: {
    alchemy: `https://eth-goerli.g.alchemy.com/v2/${alchemy}`,
  },
  [Networks.Arbitrum]: {
    alchemy: `https://arb-mainnet.g.alchemy.com/v2/${alchemy}`,
    quickNode: `https://delicate-special-theorem.arbitrum-mainnet.quiknode.pro/${quicknode}`,
  },
  [Networks.AvalancheC]: {
    quickNode: `https://blissful-boldest-sea.avalanche-mainnet.quiknode.pro/${quicknode}/ext/bc/C/rpc/`,
    infura: `https://avalanche-mainnet.infura.io/v3/${infura}/ext/bc/C/rpc`,
    getBlock: `https://avax.getblock.io/${getBlock}/mainnet/ext/bc/C/rpc`,
    chainBase: `https://avalanche-mainnet.s.chainbase.online/v1/${chainBase}/ext/bc/C/rpc`,
  },
  [Networks.Binance]: {
    chainBase: `https://bsc-mainnet.s.chainbase.online/v1/${chainBase}`,
    onFinality: `https://bnb.api.onfinality.io/rpc?apikey=${onFinality}`,

    quickNode: `https://nameless-small-vineyard.bsc.quiknode.pro/${quicknode}`,
    public_1: 'https://bsc-dataseed1.binance.org',
    public_2: 'https://bsc-dataseed2.binance.org/',
    public_3: 'https://bsc-dataseed3.binance.org/',
    public_4: 'https://bsc-dataseed4.binance.org/',

    alchemy: `https://bnb-mainnet.g.alchemy.com/v2/${alchemy}`,
  },
  [Networks.Celo]: {
    quickNode: `https://quick-cosmological-sponge.celo-mainnet.quiknode.pro/${quicknode}`,
    infura: `https://celo-mainnet.infura.io/v3/${infura}`,
  },
  [Networks.Cosmos]: {
    public_2: 'https://cosmos-rpc.polkachu.com',
    public_3: 'https://cosmos.chorus.one',
    getBlock: `https://go.getblock.io/${getBlock}`,
    nowNode: `https://atom-tendermint.nownodes.io/${nowNode}`,
  },
  [Networks.Kava]: {
    pokt: `https://kava.rpc.grove.city/v1/${groveCity}`,
    public_1: 'https://rpc.kava.io',
    public_2: 'https://kava-rpc.polkachu.com',
  },
  [Networks.Akash]: {
    public_1: 'https://rpc.akash.forbole.com:443',
    public_2: 'https://rpc-akash.ecostake.com:443',
    public_3: 'https://akash-rpc.lavenderfive.com:443',
  },
  [Networks.Osmosis]: {
    pokt: `https://osmosis-mainnet.rpc.grove.city/v1/${groveCity}`,
    nowNode: `https://osmo-tendermint.nownodes.io/${nowNode}`,
    public_1: 'https://rpc.osmosis.zone/',
    public_4: 'https://osmosis-rpc.quickapi.com:443',
    public_6: 'https://osmosis-rpc.lavenderfive.com:443',
    public_7: 'https://rpc-osmosis.ecostake.com',
  },
  [Networks.Juno]: {
    public_3: 'https://rpc-juno.itastakers.com',
    public_4: 'http://juno.rpc.m.stavr.tech:1067',
    public_6: 'https://juno-rpc.polkachu.com',
    public_7: 'https://juno-rpc.lavenderfive.com:443',
  },
  [Networks.Persistence]: {
    public_3: 'https://persistence-rpc.polkachu.com',
    public_4: 'https://persistence-rpc.kleomedes.network',
    public_5: 'https://rpc-persistence.architectnodes.com',
  },
  [Networks.Stargaze]: {
    public_1: 'https://rpc.stargaze-apis.com/',
    public_3: 'https://rpc-stargaze.ezstaking.dev',
    public_4: 'https://stargaze-rpc.polkachu.com',
    public_8: 'https://stargaze-rpc.ibs.team',
  },
  [Networks.Terra]: {
    public_1: 'https://terra-rpc.easy2stake.com:443',
    public_2: 'https://terra.stakesystems.io:2053',
  },
  [Networks.Fantom]: {
    quickNode: `https://fluent-summer-isle.fantom.quiknode.pro/${quicknode}/`,
    pokt: `https://fantom-mainnet.rpc.grove.city/v1/${groveCity}`,
  },
  [Networks.Gnosis]: {
    pokt: `https://gnosischain-mainnet.gateway.pokt.network/v1/lb/${groveCity}`,
    public_1: 'https://rpc.gnosischain.com/',
  },
  [Networks.Harmony]: {
    pokt: `https://harmony-0.rpc.grove.city/v1/${groveCity}`,
    public_1: 'https://api.harmony.one',
  },
  [Networks.Moonriver]: {
    pokt: `https://moonriver-mainnet.gateway.pokt.network/v1/lb/${groveCity}`,
    public_1: 'https://moonriver.api.onfinality.io/public',
  },
  [Networks.Near]: {
    grove: `https://near.rpc.grove.city/v1/${groveCity}`,
    public_1: 'https://rpc.mainnet.near.org',
    quickNode: `https://twilight-delicate-bridge.near-mainnet.quiknode.pro/${quicknode}`,
  },
  [Networks.Optimism]: {
    alchemy: `https://opt-mainnet.g.alchemy.com/v2/${alchemy}`,
    infura: `https://optimism-mainnet.infura.io/v3/${infura}`,
    quickNode: `https://convincing-methodical-vineyard.optimism.quiknode.pro/${quicknode}`,
  },
  [Networks.OKC]: {
    pokt: `https://oKc-mainnet.gateway.pokt.network/v1/lb/${groveCity}`,
    public_1: 'https://exchainrpc.okex.org',
  },
  [Networks.Polygon]: {
    alchemy: `https://polygon-mainnet.g.alchemy.com/v2/${alchemy}`,
    pokt: `https://poly-mainnet.rpc.grove.city/v1/${groveCity}`,
    infura: `https://polygon-mainnet.infura.io/v3/${infura}`,
    quickNode: `https://still-wiser-ensemble.matic.quiknode.pro/${quicknode}`,
  },
  [Networks.Solana]: {
    chainstack: `https://solana-mainnet.core.chainstack.com/${chainstack}`,
    nowNode: `https://sol.nownodes.io/${nowNode}`,
    quickNode: `https://neat-tame-patina.solana-mainnet.quiknode.pro/${quicknode}`,
  },
  [Networks.Tezos]: {
    nowNode: `https://xtz.nownodes.io/${nowNode}`,
    public_1: `https://rpc.tzbeta.net/`,
    public_2: 'https://mainnet.api.tez.ie',
    public_3: 'https://mainnet.smartpy.io',
  },
  [Networks.zkSync]: {
    public_1: 'https://mainnet.era.zksync.io',
    public_2: 'https://zksync2-mainnet.zksync.io',
  },
  [Networks.Bitcoin]: {
    quickNode: `https://frosty-old-fire.btc.quiknode.pro/${quicknode}/`,
  },
  [Networks.Tron]: {
    public: {
      tronGrid: 'https://api.trongrid.io',
      allNodes: 'https://tron-rpc.publicnode.com',
    },
  },
};

export let rpcUris: { [network in Networks]: string[] };
process.env.METRICS === 'true'
  ? Object.values(Networks).forEach(async (network) => {
      if (rpcUris === undefined) {
        // @ts-expect-error initial assigment
        rpcUris = { [network]: Object.values(providers[network]) };
      } else {
        rpcUris[network] = Object.values(providers[network]);
      }
    })
  : (rpcUris = {
      [Networks.Ethereum]: [
        providers.ethereum.quickNode,
        providers.ethereum.alchemy,
      ],
      [Networks.EthereumGoerli]: [providers[Networks.EthereumGoerli].alchemy],
      [Networks.Arbitrum]: [
        providers.arbitrum.alchemy,
        providers.arbitrum.quickNode,
      ],
      [Networks.AvalancheC]: [
        providers[Networks.AvalancheC].quickNode,
        providers[Networks.AvalancheC].infura,
      ],
      [Networks.Binance]: [
        providers.binance.quickNode,
        providers.binance.alchemy,
      ],
      [Networks.Celo]: [providers.celo.quickNode, providers.celo.infura],
      [Networks.Cosmos]: [providers.cosmos.getBlock, providers.cosmos.nowNode],
      [Networks.Kava]: [providers.kava.pokt, providers.kava.public_1],
      [Networks.Akash]: [providers.akash.public_1, providers.akash.public_2],
      [Networks.OKC]: [providers.okc.pokt, providers.okc.public_1],
      [Networks.Osmosis]: [providers.osmosis.pokt, providers.osmosis.nowNode], // pokt is 100% erroring out
      [Networks.Juno]: [providers.juno.public_4, providers.juno.public_3],
      [Networks.Persistence]: [
        providers.persistence.public_3,
        providers.persistence.public_4,
      ],
      [Networks.Stargaze]: [
        providers.stargaze.public_1,
        providers.stargaze.public_3,
      ],
      [Networks.Terra]: [providers.terra.public_1, providers.terra.public_2],
      [Networks.Fantom]: [providers.fantom.pokt, providers.fantom.quickNode],
      [Networks.Gnosis]: [providers.gnosis.pokt, providers.gnosis.public_1],
      [Networks.Harmony]: [providers.harmony.pokt, providers.harmony.public_1],
      [Networks.Moonriver]: [
        providers.moonriver.pokt,
        providers.moonriver.public_1,
      ],
      [Networks.Near]: [providers.near.quickNode, providers.near.grove],
      [Networks.Optimism]: [
        providers.optimism.alchemy,
        providers.optimism.quickNode,
      ],
      [Networks.Polygon]: [
        providers.polygon.alchemy,
        providers.polygon.quickNode,
      ],
      [Networks.Solana]: [providers.solana.nowNode, providers.solana.quickNode],
      [Networks.Tezos]: [providers.tezos.nowNode, providers.tezos.public_2],
      [Networks.zkSync]: [providers.zksync.public_1, providers.zksync.public_2],
      [Networks.Bitcoin]: [providers.bitcoin.quickNode],
      [Networks.Tron]: [
        providers.tron.public.tronGrid,
        providers.tron.public.allNodes,
      ],
    });

export const avalancePChain =
  'https://avalanche.api.onfinality.io/ext/bc/P?apikey=d2184993-4b67-48dd-84c5-26e58f4ed5a5';
