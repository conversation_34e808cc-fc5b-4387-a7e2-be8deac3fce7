export enum EvmNetworks {
  AvalancheC = 'avalanche-c', // NEVER USE A DASH FOR NETWORK NAMES
  Arbitrum = 'arbitrum',
  Binance = 'binance',
  Celo = 'celo',
  Ethereum = 'ethereum',
  EthereumGoerli = 'ethereum-goerli',
  Fantom = 'fantom',
  Gnosis = 'gnosis',
  Harmony = 'harmony',
  Moonriver = 'moonriver',
  Optimism = 'optimism',
  OKC = 'okc',
  Polygon = 'polygon',
  zkSync = 'zksync',
}

export enum CosmosNetworks {
  Akash = 'akash',
  Cosmos = 'cosmos',
  Juno = 'juno',
  Kava = 'kava',
  Osmosis = 'osmosis',
  Persistence = 'persistence',
  Stargaze = 'stargaze',
}

export enum MiscNetworks {
  Near = 'near',
  Solana = 'solana',
  Tron = 'tron',
  Tezos = 'tezos',
  Terra = 'terra',
  Bitcoin = 'bitcoin',
}

export const Networks = { ...EvmNetworks, ...CosmosNetworks, ...MiscNetworks };
export type Networks = EvmNetworks | CosmosNetworks | MiscNetworks;
