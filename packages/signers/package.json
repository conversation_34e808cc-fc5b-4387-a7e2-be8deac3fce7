{"name": "@steakwallet/signers", "version": "1.1.5", "scripts": {"clean": "rm -rf lib", "build": "tsc -b .", "lint": "eslint \"src/**/*.ts\"", "test": "jest", "prepublishOnly": "yarn build && npm pack"}, "files": ["lib"], "main": "lib/index.js", "types": "lib/index.d.ts", "jest": {"bail": 2, "collectCoverage": false, "moduleFileExtensions": ["js", "ts", "json", "node"], "preset": "ts-jest", "testRegex": ".test.ts", "testPathIgnorePatterns": ["/node_modules/", "/lib/"], "testEnvironment": "node", "transform": {"^.+\\.(js|ts)$": "ts-jest"}, "verbose": true}, "bundleDependencies": ["ed25519-hd-key"], "dependencies": {"@anchor-protocol/anchor.js": "^5.0.2", "@bitcoin-js/tiny-secp256k1-asmjs": "^2.2.3", "@cosmjs/amino": "0.27.0", "@cosmjs/cosmwasm-stargate": "0.27.0", "@cosmjs/crypto": "0.27.0", "@cosmjs/encoding": "0.27.0", "@cosmjs/proto-signing": "0.27.0", "@cosmjs/stargate": "0.27.0", "@ensdomains/address-encoder": "^0.2.10", "@ethereumjs/common": "^2.6.0", "@ethereumjs/tx": "3.4.0", "@graphprotocol/contracts": "^1.8.0", "@harmony-js/staking": "^0.1.56", "@ledgerhq/types-live": "^6.45.0", "@metamask/eth-sig-util": "^4.0.1", "@solana/buffer-layout": "^4.0.0", "@solana/spl-token": "^0.3.4", "@solana/web3.js": "1.52.0", "@steakwallet/types": "*", "@sushiswap/default-token-list": "^43.3.0", "@taquito/signer": "^17.0.0", "@taquito/taquito": "^17.0.0", "@taquito/tzip12": "^17.0.0", "@taquito/tzip16": "^17.0.0", "@taquito/utils": "^17.0.0", "axios": "0.27.2", "bignumber.js": "^9.0.1", "bip32": "^2.0.6", "bip39": "^3.0.3", "bitcoinjs-lib": "5.2.0", "borsh": "^0.7.0", "bs58": "^5.0.0", "buffer": "^6.0.3", "buffer-layout": "^1.2.2", "cosmjs-types": "^0.8.0", "create-hmac": "^1.1.7", "ecpair": "2.0.1", "ed25519-hd-key": "^1.3.0", "ethereum-multicall": "^2.14.0", "ethereumjs-util": "7.1.4", "ethereumjs-wallet": "^1.0.2", "ethers": "^5.7.0", "hdkey": "2.0.1", "isomorphic-dompurify": "^0.15.0", "js-sha256": "^0.10.1", "lodash.startcase": "^4.4.0", "memoizee": "^0.4.15", "near-api-js": "^1.1.0", "near-seed-phrase": "^0.2.0", "patch-package": "^8.0.0", "tronweb": "5.1.1", "ts-is-present": "^1.2.1", "tweetnacl": "^1.0.3", "web3": "^1.7.3"}, "devDependencies": {"@commitlint/cli": "^17.4.4", "@commitlint/config-conventional": "^17.4.4", "@commitlint/config-lerna-scopes": "^17.4.2", "@types/create-hash": "^1.2.2", "@types/eslint": "^8", "@types/hdkey": "^2.0.1", "@types/jest": "^29.5.2", "@types/memoizee": "^0.4.8", "@types/node": "^20.5.9", "@types/prettier": "^2", "@typescript-eslint/eslint-plugin": "^5.58.0", "@typescript-eslint/parser": "^5.58.0", "eslint": "^8.38.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-prettier": "^4.2.1", "husky": "^8.0.0", "jest": "^29.5.0", "jest-cli": "^29.5.0", "jest-environment-node": "^29.5.0", "jest-runner": "^29.5.0", "prettier": "^2.8.7", "ts-jest": "^29.1.0", "typescript": "^4.6.2"}}