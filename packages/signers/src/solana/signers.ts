import { Keypair, PublicKey, Transaction } from '@solana/web3.js';
import * as nacl from 'tweetnacl';

export interface SolanaSigner {
  sign: (x: Buffer) => Promise<Buffer>;
  signTransaction: (x: Transaction) => Promise<Transaction>;

  getPublicKey: () => Promise<PublicKey>;
}

export class SolanaKeyPairSigner implements SolanaSigner {
  constructor(private keyPair: Keypair) {}

  async signTransaction(tx: Transaction) {
    const signature = nacl.sign.detached(
      tx.compileMessage().serialize(),
      this.keyPair.secretKey,
    );

    const existingIndex = tx.signatures.findIndex(
      (x) => x.publicKey.toBase58() === this.keyPair.publicKey.toBase58(),
    );
    if (existingIndex !== -1) {
      tx.signatures[existingIndex].signature = Buffer.from(signature);
    } else {
      tx.addSignature(this.keyPair.publicKey, Buffer.from(signature));
    }

    return tx;
  }

  async getPublicKey() {
    return this.keyPair.publicKey;
  }

  async sign(message: Buffer) {
    const signature = nacl.sign.detached(message, this.keyPair.secretKey);
    return Buffer.from(signature);
  }
}
