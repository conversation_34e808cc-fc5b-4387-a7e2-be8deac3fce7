import { log } from 'console';

import { flags } from '@oclif/command';
import {
  Balances,
  getBalancesFromChains,
  isPortfolioBalanceErrorResponse,
} from '@steakwallet/chains/src';

import { BaseCommand } from '../base';

export default class Balance extends BaseCommand {
  static description = 'Read the balance of an account';

  static examples = [
    `$ cli balance celo cusd`,
    `$ cli balance avalanche-c avax`,
    `$ cli balance cosmos atom --validators cosmosvaloper1hjct6q7npsspsg3dgvzk3sdf89spmlpfdn6m9d,cosmosvaloper156gqf9837u7d4c4678yt3rl4ls9c5vuursrrzf`,
  ];

  static args = [...BaseCommand.args, { name: 'address', required: false }];
  static flags = {
    ...BaseCommand.flags,
    validators: flags.string({ required: false }),
  };

  async run() {
    let additionalAddresses = {};

    const validatorAddresses =
      // @ts-expect-error
      this.options.flags.validators && this.options.flags.validators.split(',');

    await this.initChain(this.integration.chains[0]);
    additionalAddresses = {
      ...additionalAddresses,
      ...(await this.integration.chains[0].getAdditionalAddresses()),
    };

    const address =
      this.options.args.address ?? this.integration.chains[0].getAddress();

    const b = await getBalancesFromChains(
      {
        network: this.options.args.network,
        tokenAddress: this.token.address,
        address,
        additionalAddresses,
      },
      {
        chains: [this.integration.chains[0]],
      },
    );

    if (isPortfolioBalanceErrorResponse(b)) {
      log('No balances found');
      return b;
    }

    function printBalance(balances: Balances) {
      Object.entries(balances).forEach(([key, value]) => {
        if (!value) {
          return;
        }

        console.log(
          '  ',
          key,
          value.amount,
          value.date ? `${value.date.toDateString()}` : '',
        );
      });
    }
    printBalance(b.balances);
  }
}
