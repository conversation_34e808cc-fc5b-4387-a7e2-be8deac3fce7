{"name": "@steakwallet/backend-common", "version": "0.0.0", "private": true, "scripts": {"build": "tsc -b .", "lint": "eslint \"src/**/*.ts\"", "clean": "rm -rf lib", "test": "exit 0"}, "main": "lib/index.js", "types": "lib/index.d.ts", "dependencies": {"@aws-sdk/client-cloudwatch": "^3.171.0", "@aws-sdk/client-s3": "^3.113.0", "@aws-sdk/client-secrets-manager": "^3.826.0", "@aws-sdk/client-sqs": "^3.113.0", "@cosmjs/encoding": "^0.32.2", "@nestjs/common": "^8.0.0", "@nestjs/core": "^8.0.0", "@nestjs/swagger": "^6.0.1", "@nestjs/typeorm": "^9.0.0", "@steakwallet/chains": "*", "@steakwallet/types": "*", "@types/pg": "^8.6.1", "axios": "0.27.2", "class-transformer": "^0.5.1", "dotenv": "^16.0.0", "firebase-admin": "^9.11.0", "lodash.capitalize": "^4.2.1", "logform": "^2.4.2", "memoizee": "^0.4.15", "moment-timezone": "^0.5.34", "pg": "^8.7.1", "typeorm": "^0.3.7", "winston": "^3.7.2"}, "devDependencies": {"@types/lodash.capitalize": "^4.2.6", "@types/node": "*", "@types/node-fetch": "2.6.2", "@types/pg": "^8.6.1", "@types/text-encoding": "^0.0.36", "class-validator": "^0.13.2", "nodemon": "^2.0.15", "typescript": "*"}}