import { decode } from './aave-v2';

const tests: [string, { data: string; value?: string }, any][] = [
  [
    'borrow',
    {
      data: '0xa415bcad0000000000000000000000006b175474e89094c44da98b954eedeac495271d0f00000000000000000000000000000000000000000000000340aad21b3b700000000000000000000000000000000000000000000000000000000000000000000100000000000000000000000000000000000000000000000000000000000000000000000000000000000000003ff4988d38007c1740beedfedc6bde39b2616509',
    },
    {
      title: 'Borrow',
      params: [
        {
          key: 'Asset',
          type: 'amount',
          data: {
            amount: '60000000000000000000',
            tokenAddress: '0x6B175474E89094C44Da98b954EedeAC495271d0F',
          },
        },
        {
          key: 'Interest rate mode',
          type: 'string',
          data: {
            value: 'Variable',
          },
        },
      ],
    },
  ],
  [
    'deposit',
    {
      data: '0xe8eda9df0000000000000000000000002260fac5e5542a773aa44fbcfedf7c193bc2c59900000000000000000000000000000000000000000000000000000000031b68b500000000000000000000000066b0d6cd3a7704baeeb42a78122311949ac3b7430000000000000000000000000000000000000000000000000000000000000000',
    },
    {
      title: 'Supply',
      params: [
        {
          key: 'Asset',
          type: 'amount',
          data: {
            amount: '52127925',
            tokenAddress: '0x2260FAC5E5542a773Aa44fBCfeDf7C193bc2C599',
          },
        },
      ],
    },
  ],
  [
    'withdraw',
    {
      data: '0x69328dec000000000000000000000000a0b86991c6218b36c1d19d4a2e9eb0ce3606eb480000000000000000000000000000000000000000000000000000000d42cdc985000000000000000000000000bb0f66f52ccefc7d36c50d7e7c2c208e53c09cc6',
    },
    {
      title: 'Withdraw',
      params: [
        {
          key: 'Asset',
          type: 'amount',
          data: {
            tokenAddress: '0xA0b86991c6218b36c1d19D4a2e9Eb0cE3606eB48',
            amount: '56955357573',
          },
        },
      ],
    },
  ],
  [
    'repay',
    {
      data: '0x573ade81000000000000000000000000dac17f958d2ee523a2206206994597c13d831ec70000000000000000000000000000000000000000000000000000000026165dc00000000000000000000000000000000000000000000000000000000000000002000000000000000000000000f4eb06b92a5686834dc8eeaac37ed34c1ce93eef',
    },
    {
      title: 'Repay',
      params: [
        {
          key: 'Asset',
          type: 'amount',
          data: {
            tokenAddress: '0xdAC17F958D2ee523a2206206994597C13D831ec7',
            amount: '639000000',
          },
        },
        {
          key: 'Interest rate mode',
          type: 'string',
          data: {
            value: 'Variable',
          },
        },
      ],
    },
  ],
];

describe.skip('Aave V2', () => {
  tests.forEach(([name, input, expected]) => {
    it(name, async () => {
      expect(decode(input)).toStrictEqual(expected);
    });
  });
});
