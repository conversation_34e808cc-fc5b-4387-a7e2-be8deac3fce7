{"name": "steakwallet-monorepo", "private": true, "workspaces": ["packages/*", "packages/indexers/*", "packages/indexers/evm/*", "app"], "scripts": {"format": "prettier --write ./packages ./app", "lint": "turbo run lint --filter='./packages/**'", "test": "turbo run test --filter='./packages/**'", "test:mobile": "turbo run test --filter='./app/**'", "clean": "git clean -xfd -e '*.env' -e 'terraform'", "build": "turbo run build --filter='./packages/**'", "prepare": "husky install", "cron-jobs": "ts-node packages/cron/src/index.ts", "local": "yarn install && yarn build", "setup": "yarn install && yarn build && cd app && yarn local", "evm": "ts-node packages/indexers/evm/v1/bin/tip/run.ts", "docker": "cd packages/legacy-api && docker-compose up -d", "server:legacy": "cd packages/legacy-api && yarn dev", "server:dev": "cd packages/api && yarn start:dev", "update:tokens": "cd packages/chains && ./scripts/update-token-lists.sh"}, "devDependencies": {"@babel/preset-typescript": "^7.14.5", "@types/jest": "28.1.3", "@types/mocha": "^9.0.0", "@types/node": "^18.13.0", "@typescript-eslint/eslint-plugin": "^5", "@typescript-eslint/parser": "^5", "babel-jest": "28.1.3", "babel-loader": "^8.2.3", "dotenv": "^16.0.0", "eslint": "^8.14.0", "eslint-import-resolver-typescript": "^2.5.0", "eslint-plugin-import": "^2.25.3", "husky": "^7.0.0", "jest": "28.1.3", "jest-cli": "28.1.3", "prettier": "^2.5.1", "ts-jest": "28.0.8", "ts-node": "^10.0.0", "ts-node-dev": "^2.0.0", "turbo": "2.5.4", "typescript": "^4.6.2"}, "dependencies": {"eslint-plugin-jest": "^26.1.5", "lint-staged": "^13.0.3", "patch-package": "^6.5.1"}, "resolutions": {"@solana/web3.js": "1.52.0", "follow-redirects": ">=1.14.7", "axios": "0.27.2", "node-forge": ">=1.3.0", "@types/react": "17.0.52", "react-devtools-core": "4.24.3", "promise": "8.3.0", "appcenter-cli@^2.13.2": "patch:appcenter-cli@npm%3A2.13.2#./.yarn/patches/appcenter-cli-npm-2.13.2-f89ce4128d.patch", "react-native@0.67.5": "patch:react-native@npm%3A0.67.5#./.yarn/patches/react-native-npm-0.67.5-5ec5e6c032.patch", "apg-js@^4.1.1": "patch:apg-js@npm%3A4.1.3#./.yarn/patches/apg-js-npm-4.1.3-70ea94c283.patch", "big-integer@1.6.36": "patch:big-integer@npm%3A1.6.51#./.yarn/patches/big-integer-npm-1.6.51-1a244d8e1f.patch", "big-integer@^1.6.51": "patch:big-integer@npm%3A1.6.51#./.yarn/patches/big-integer-npm-1.6.51-1a244d8e1f.patch", "big-integer@^1.6.16": "patch:big-integer@npm%3A1.6.51#./.yarn/patches/big-integer-npm-1.6.51-1a244d8e1f.patch", "big-integer@1.6.x": "patch:big-integer@npm%3A1.6.51#./.yarn/patches/big-integer-npm-1.6.51-1a244d8e1f.patch", "big-integer@^1.6.17": "patch:big-integer@npm%3A1.6.51#./.yarn/patches/big-integer-npm-1.6.51-1a244d8e1f.patch", "shell-quote": "^1.7.3"}, "packageManager": "yarn@3.5.1"}